// frontend/src/components/Chat/MessageStatus.tsx
import React from 'react';
import { Icon } from '../ui/Icon';
import type { MessageStatusType } from '../../store/slices/messageSlice';

interface MessageStatusProps {
  status?: MessageStatusType;
  isOwnMessage: boolean;
  isSending?: boolean;
  onRetry?: () => void;
  className?: string;
}

const MessageStatus: React.FC<MessageStatusProps> = ({
  status,
  isOwnMessage,
  isSending = false,
  onRetry,
  className = ''
}) => {
  // Only show status for own messages
  if (!isOwnMessage) {
    return null;
  }

  // Show loading indicator when sending
  if (isSending) {
    return (
      <div className={`flex items-center space-x-1 text-xs ${className}`}>
        <Icon name="loader" size={12} className="animate-spin opacity-70" />
        <span className="opacity-70">Sending...</span>
      </div>
    );
  }

  // Show status indicators based on message status
  switch (status) {
    case 'SENT':
      return (
        <div className={`flex items-center ${className}`}>
          <Icon name="check" size={12} className="text-gray-400" />
        </div>
      );

    case 'DELIVERED':
      return (
        <div className={`flex items-center ${className}`}>
          <Icon name="check" size={12} className="text-gray-500" />
        </div>
      );

    case 'READ':
      return (
        <div className={`flex items-center ${className}`}>
          <Icon name="check-check" size={12} className="text-green-500" />
        </div>
      );

    case 'FAILED':
      return (
        <div className={`flex items-center ${className}`}>
          {onRetry ? (
            <button
              onClick={onRetry}
              className="flex items-center space-x-1 text-red-500 hover:text-red-600 transition-colors"
              title="Retry sending message"
            >
              <Icon name="refresh-cw" size={12} />
              <span className="text-xs">Retry</span>
            </button>
          ) : (
            <Icon name="alert-circle" size={12} className="text-red-500" />
          )}
        </div>
      );

    default:
      return null;
  }
};

export default MessageStatus;
