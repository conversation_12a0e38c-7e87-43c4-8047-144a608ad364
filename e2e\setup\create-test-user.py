#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create test users for E2E testing
Run this script to ensure test users exist in the database
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to the Python path
backend_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'backend')
sys.path.insert(0, backend_dir)

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatapp.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()

def create_test_users():
    """Create test users for E2E testing"""
    
    test_users = [
        {
            'email': '<EMAIL>',
            'name': '<PERSON>',
            'password': 'testpass123'
        },
        {
            'email': '<EMAIL>',
            'name': 'Test User',
            'password': 'password123'
        },
        {
            'email': '<EMAIL>',
            'name': '<PERSON>',
            'password': 'testpass123'
        },
        {
            'email': '<EMAIL>',
            'name': '<PERSON>',
            'password': 'testpass123'
        }
    ]
    
    created_users = []
    updated_users = []
    
    with transaction.atomic():
        for user_data in test_users:
            user, created = User.objects.get_or_create(
                email=user_data['email'],
                defaults={
                    'name': user_data['name'],
                    'is_verified': True,
                    'is_active': True
                }
            )
            
            if created:
                user.set_password(user_data['password'])
                user.save()
                created_users.append(user.email)
                print(f"✅ Created user: {user.email}")
            else:
                # Update password in case it changed
                user.set_password(user_data['password'])
                user.name = user_data['name']
                user.is_verified = True
                user.is_active = True
                user.save()
                updated_users.append(user.email)
                print(f"🔄 Updated user: {user.email}")
    
    print(f"\n📊 Summary:")
    print(f"   Created: {len(created_users)} users")
    print(f"   Updated: {len(updated_users)} users")
    print(f"   Total test users: {len(test_users)}")
    
    return created_users, updated_users

def verify_test_users():
    """Verify that test users can authenticate"""
    
    test_credentials = [
        ('<EMAIL>', 'testpass123'),
        ('<EMAIL>', 'password123'),
        ('<EMAIL>', 'testpass123'),
        ('<EMAIL>', 'testpass123')
    ]
    
    print("\n🔐 Verifying user authentication:")
    
    for email, password in test_credentials:
        try:
            user = User.objects.get(email=email)
            if user.check_password(password):
                print(f"✅ {email} - Authentication successful")
            else:
                print(f"❌ {email} - Authentication failed")
        except User.DoesNotExist:
            print(f"❌ {email} - User not found")

def main():
    print("🚀 Setting up test users for E2E testing...")
    print(f"📍 Backend directory: {backend_dir}")
    print(f"📍 Django settings: {settings.SETTINGS_MODULE}")
    
    try:
        created, updated = create_test_users()
        verify_test_users()
        
        print("\n🎉 Test user setup completed successfully!")
        print("\nTest users available for E2E testing:")
        print("- <EMAIL> (testpass123)")
        print("- <EMAIL> (password123)")
        print("- <EMAIL> (testpass123)")
        print("- <EMAIL> (testpass123)")
        
    except Exception as e:
        print(f"❌ Error setting up test users: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
