// frontend/src/components/Chat/ChatHeader.tsx
import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { User, MoreVertical, Phone, Video, Loader2 } from 'lucide-react';
import { useSocket } from '../../contexts/SocketContext';
import { useInitiateCallMutation } from '../../services/callingApi';

import type { RootState, AppDispatch } from '../../store';
import type { Conversation, DraftConversation } from '../../store/slices/conversationSlice';
import {
  selectIsInCall,
  showActiveCallInterface,
  updateCallStatus,
  setCurrentCall
} from '../../store/slices/callingSlice';

interface ChatHeaderProps {
  conversationId: string;
  currentUserId: string;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({ conversationId }) => {
  const dispatch = useDispatch<AppDispatch>();
  const isDraftConversation = conversationId.startsWith('draft-');
  const { isConnected } = useSocket();
  const [initiateCall, { isLoading: isInitiatingCall }] = useInitiateCallMutation();
  const [callError, setCallError] = useState<string | null>(null);

  // Get conversation from Redux store for draft conversations
  const draftConversation = useSelector((state: RootState) =>
    state.conversations.draftConversations.find(draft => draft.id === conversationId)
  );

  // Get real conversation from Redux store
  const reduxConversation = useSelector((state: RootState) =>
    state.conversations.conversations.find(conv => conv.id === conversationId)
  );

  // Get calling state
  const isInCall = useSelector(selectIsInCall);

  // Determine the conversation data - use Redux store only
  const conversation: Conversation | DraftConversation | undefined = isDraftConversation
    ? draftConversation
    : reduxConversation;

  const isLoading = false; // No API loading since we're using Redux store only

  console.log('conversation1111111111111', {
    conversationId,
    isDraftConversation,
    draftConversation,
    reduxConversation,
    finalConversation: conversation,
    isLoading
  });

  if (isLoading || !conversation) {
    return (
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
            <div>
              <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="w-16 h-3 bg-gray-200 rounded animate-pulse mt-1"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Get the display name for the conversation
  const getDisplayName = () => {
    if (conversation.type === 'GROUP') {
      return conversation.name || 'Group Chat';
    }
    return 'Direct message';
  };

  // Get the subtitle for the conversation
  const getSubtitle = () => {
    if (conversation.type === 'GROUP') {
      const participantCount = conversation.participants.length;
      return `${participantCount} members`;
    }
    if (isDraftConversation) {
      return 'New conversation';
    }
    return 'Direct message';
  };

  // Call button handlers
  const handleAudioCall = async () => {
    if (!isConnected) {
      console.warn('Cannot initiate call - not connected to server');
      return;
    }

    if (isInCall) {
      console.warn('Cannot initiate call - already in a call');
      return;
    }

    if (isDraftConversation) {
      console.warn('Cannot initiate call - conversation is a draft');
      return;
    }

    if (isInitiatingCall) {
      console.warn('Cannot initiate call - already initiating a call');
      return;
    }

    try {
      console.log('📞 Initiating audio call for conversation:', conversationId);

      // Clear any previous errors
      setCallError(null);

      // Set call state to initiating but DON'T show interface yet
      dispatch(setCurrentCall({
        id: null, // Will be set when API responds
        conversationId,
        type: 'audio',
        status: 'initiating',
        startTime: null, // Don't set start time until API succeeds
        duration: 0
      }));

      // Make API call first - only show interface if successful
      const result = await initiateCall({
        conversation_id: conversationId,
        call_type: 'audio'
      }).unwrap();

      console.log('✅ Audio call API succeeded:', result);

      if (result?.success) {
        dispatch(setCurrentCall({
          id: result.data.id,
          conversationId,
          type: 'audio',
          status: 'ringing',
          startTime: Date.now(),
          duration: 0,
          isInitiator: true
        }));
        dispatch(showActiveCallInterface());

        // Trigger WebRTC offer creation for the caller
        setTimeout(() => {
          const event = new CustomEvent('webrtc-create-offer', { detail: { callId: result.data.id } });
          window.dispatchEvent(event);
        }, 100);
      }

      // Only NOW show the interface after API success


      console.log('✅ Audio call initiated successfully with ID:', result.data.id);
    } catch (error) {
      console.error('❌ Failed to initiate audio call:', error);

      // Reset call state on API failure
      dispatch(setCurrentCall({
        id: null,
        conversationId: null,
        type: null,
        status: 'idle',
        startTime: null,
        duration: 0
      }));

      // Show error message to user
      const errorMessage = error instanceof Error ? error.message : 'Failed to initiate call';
      setCallError(`Call failed: ${errorMessage}`);

      // Clear error after 5 seconds
      setTimeout(() => setCallError(null), 5000);
    }
  };

  const handleVideoCall = async () => {
    if (!isConnected) {
      console.warn('Cannot initiate call - not connected to server');
      return;
    }

    if (isInCall) {
      console.warn('Cannot initiate call - already in a call');
      return;
    }

    if (isDraftConversation) {
      console.warn('Cannot initiate call - conversation is a draft');
      return;
    }

    if (isInitiatingCall) {
      console.warn('Cannot initiate call - already initiating a call');
      return;
    }

    try {
      console.log('📹 Initiating video call for conversation:', conversationId);

      // Clear any previous errors
      setCallError(null);

      // Set call state to initiating but DON'T show interface yet
      dispatch(setCurrentCall({
        id: null, // Will be set when API responds
        conversationId,
        type: 'video',
        status: 'initiating',
        startTime: null, // Don't set start time until API succeeds
        duration: 0
      }));

      // Make API call first - only show interface if successful
      const result = await initiateCall({
        conversation_id: conversationId,
        call_type: 'video'
      }).unwrap();

      console.log('✅ Video call API succeeded:', result);

      // Only NOW show the interface after API success
      dispatch(setCurrentCall({
        id: result.data.id,
        conversationId,
        type: 'video',
        status: 'calling',
        startTime: Date.now(),
        duration: 0
      }));
      dispatch(showActiveCallInterface());

      console.log('✅ Video call initiated successfully with ID:', result.data.id);
    } catch (error) {
      console.error('❌ Failed to initiate video call:', error);

      // Reset call state on API failure
      dispatch(setCurrentCall({
        id: null,
        conversationId: null,
        type: null,
        status: 'idle',
        startTime: null,
        duration: 0
      }));

      // Show error message to user
      const errorMessage = error instanceof Error ? error.message : 'Failed to initiate call';
      setCallError(`Call failed: ${errorMessage}`);

      // Clear error after 5 seconds
      setTimeout(() => setCallError(null), 5000);
    }
  };

  // Determine if call buttons should be enabled
  const canMakeCall = isConnected && !isInCall && !isDraftConversation && !isInitiatingCall;

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Conversation info */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
            <User className="w-6 h-6 text-gray-600" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">{getDisplayName()}</h2>
            <p className="text-sm text-gray-500">{getSubtitle()}</p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          {/* Call buttons - only show for non-draft conversations */}
          {!isDraftConversation && (
            <>
              {/* Audio call button */}
              <button
                data-testid="audio-call-btn"
                onClick={handleAudioCall}
                disabled={!canMakeCall}
                className={`p-2 rounded-lg transition-colors flex items-center justify-center ${canMakeCall
                    ? 'text-gray-600 hover:text-green-600 hover:bg-green-50'
                    : 'text-gray-400 cursor-not-allowed'
                  }`}
                title={
                  !isConnected
                    ? 'Not connected to server'
                    : isInCall
                      ? 'Already in a call'
                      : isInitiatingCall
                        ? 'Initiating call...'
                        : 'Start audio call'
                }
              >
                {isInitiatingCall ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Phone className="w-5 h-5" />
                )}
              </button>

              {/* Video call button */}
              <button
                data-testid="video-call-btn"
                onClick={handleVideoCall}
                disabled={!canMakeCall}
                className={`p-2 rounded-lg transition-colors flex items-center justify-center ${canMakeCall
                    ? 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                    : 'text-gray-400 cursor-not-allowed'
                  }`}
                title={
                  !isConnected
                    ? 'Not connected to server'
                    : isInCall
                      ? 'Already in a call'
                      : isInitiatingCall
                        ? 'Initiating call...'
                        : 'Start video call'
                }
              >
                {isInitiatingCall ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Video className="w-5 h-5" />
                )}
              </button>
            </>
          )}

          {/* More options */}
          <button
            className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            title="More options"
          >
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Error message display */}
      {callError && (
        <div className="px-4 py-2 bg-red-50 border-b border-red-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-4 w-4 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-2">
              <p className="text-sm text-red-800">{callError}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
