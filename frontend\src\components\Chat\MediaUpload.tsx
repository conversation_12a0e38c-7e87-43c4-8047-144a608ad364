// frontend/src/components/Chat/MediaUpload.tsx
import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Upload, X, File, Image, Video, Music, Archive, FileText, RefreshCw } from 'lucide-react';
import {
  mediaApiService,
  useStartChunkedUploadMutation,
  useUploadChunkMutation,
  type UploadProgress
} from '../../services/mediaApi';
import { useSocket } from '../../contexts/SocketContext';
import { useAuth } from '../../contexts/AuthContext';
import {
  createUploadSession,
  updateFileStatus,
  updateSessionStatus,
  storeChunkedUploadResponse
} from '../../store/slices/mediaUploadSlice';
import { addMessage } from '../../store/slices/messageSlice';
import type { AppDispatch } from '../../store';

interface MediaUploadProps {
  conversationId: string;
  messageId?: string;
  onUploadStart?: (file: File) => void;
  onUploadProgress?: (progress: UploadProgress) => void;
  onUploadComplete?: (mediaFile: any) => void;
  onUploadError?: (error: string) => void;
  disabled?: boolean;
}

interface FilePreview {
  file: File;
  id: string;
  preview?: string;
  uploading: boolean;
  progress: number;
  error?: string;
  retryCount?: number;
  sessionId?: string;
}

const MAX_RETRY_ATTEMPTS = 3;
const CHUNK_RETRY_DELAY = 1000; // 1 second

export const MediaUpload: React.FC<MediaUploadProps> = ({
  conversationId,
  messageId,
  onUploadStart,
  onUploadProgress,
  onUploadComplete,
  onUploadError,
  disabled = false
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [files, setFiles] = useState<FilePreview[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { socket } = useSocket();
  const { user } = useAuth();

  // RTK Query hooks
  const [startChunkedUpload] = useStartChunkedUploadMutation();
  const [uploadChunk] = useUploadChunkMutation();

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image': return <Image className="w-8 h-8 text-blue-500" />;
      case 'video': return <Video className="w-8 h-8 text-purple-500" />;
      case 'audio': return <Music className="w-8 h-8 text-green-500" />;
      case 'archive': return <Archive className="w-8 h-8 text-orange-500" />;
      case 'document': return <FileText className="w-8 h-8 text-red-500" />;
      default: return <File className="w-8 h-8 text-gray-500" />;
    }
  };

  const createFilePreview = useCallback(async (file: File): Promise<FilePreview> => {
    const id = `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    let preview: string | undefined;

    // Create preview for images
    if (file.type.startsWith('image/')) {
      preview = URL.createObjectURL(file);
    }

    return {
      file,
      id,
      preview,
      uploading: false,
      progress: 0
    };
  }, []);

  const handleFiles = useCallback(async (fileList: FileList) => {
    if (disabled) return;

    const newFiles: FilePreview[] = [];

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];

      // Validate file
      const validation = mediaApiService.validateFile(file);
      if (!validation.valid) {
        onUploadError?.(validation.error || 'Invalid file');
        continue;
      }

      const filePreview = await createFilePreview(file);
      newFiles.push(filePreview);
    }

    setFiles(prev => [...prev, ...newFiles]);
  }, [disabled, createFilePreview, onUploadError]);

  /**
   * Upload a single chunk with retry logic using RTK Query
   */
  const uploadChunkWithRetry = async (
    chunk: Blob,
    uploadSession: string,
    chunkNumber: number,
    totalChunks: number,
    chunkHash: string,
    mediaFileId: string,
    tempId: string,
    retryCount = 0
  ): Promise<any> => {
    try {
      const result = await uploadChunk({
        chunk,
        uploadSession,
        chunkNumber,
        totalChunks,
        chunkHash,
        mediaFileId,
        conversationId,
        tempId,
      }).unwrap();

      return result;
    } catch (error) {
      // Retry logic for failed chunks
      if (retryCount < MAX_RETRY_ATTEMPTS) {
        console.warn(`Chunk ${chunkNumber + 1} failed, retrying (${retryCount + 1}/${MAX_RETRY_ATTEMPTS})...`);
        await new Promise(resolve => setTimeout(resolve, CHUNK_RETRY_DELAY * (retryCount + 1)));
        return uploadChunkWithRetry(chunk, uploadSession, chunkNumber, totalChunks, chunkHash, mediaFileId, tempId, retryCount + 1);
      }
      throw error;
    }
  };

  const uploadFile = useCallback(async (filePreview: FilePreview) => {
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    try {
      // Validate file size (25MB max)
      const MAX_FILE_SIZE = 25 * 1024 * 1024;
      if (filePreview.file.size > MAX_FILE_SIZE) {
        throw new Error('File size exceeds 25MB limit');
      }

      // Update file state to uploading
      setFiles(prev => prev.map(f =>
        f.id === filePreview.id
          ? { ...f, uploading: true, progress: 0, error: undefined, sessionId }
          : f
      ));

      // Create Redux upload session
      dispatch(createUploadSession({
        sessionId,
        conversationId,
        fileMetadata: [{
          name: filePreview.file.name,
          size: filePreview.file.size,
          type: filePreview.file.type,
          lastModified: filePreview.file.lastModified,
          fileId: filePreview.id
        }]
      }));

      // Update Redux status to uploading
      dispatch(updateFileStatus({
        sessionId,
        fileId: filePreview.id,
        status: 'uploading',
        progress: 0
      }));

      onUploadStart?.(filePreview.file);

      // Generate encryption key and nonce (placeholder - integrate with encryption context)
      const fileKey = crypto.getRandomValues(new Uint8Array(32));
      const nonce = crypto.getRandomValues(new Uint8Array(12));

      // Convert to base64 for API
      const wrappedFileKey = btoa(String.fromCharCode(...fileKey));
      const fileNonce = btoa(String.fromCharCode(...nonce));

      // Call startChunkedUpload API
      const startResult = await startChunkedUpload({
        conversationId,
        originalFilename: filePreview.file.name,
        fileType: mediaApiService.getFileType(filePreview.file.type),
        mimeType: filePreview.file.type,
        fileSize: filePreview.file.size,
        wrappedFileKey,
        fileNonce,
        thumbnailNonce: btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(12)))),
      }).unwrap();

      // Store the response in Redux
      dispatch(storeChunkedUploadResponse({
        sessionId,
        uploadSession: startResult.uploadSession,
        mediaFileId: startResult.mediaFileId
      }));

      // Add file message to Redux store
      if (user) {
        dispatch(addMessage({
          id: `temp-${Date.now()}`, // Temporary ID
          conversationId,
          content: `📎 ${filePreview.file.name}`,
          messageType: 'FILE' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          sender: {
            id: user.id || '',
            name: user.name || 'Unknown',
            profilePicture: user.profilePicture || null
          }
        }));
      }

      // Update file state to completed
      setFiles(prev => prev.map(f =>
        f.id === filePreview.id
          ? { ...f, uploading: false, progress: 100 }
          : f
      ));

      // Update Redux status to completed
      dispatch(updateFileStatus({
        sessionId,
        fileId: filePreview.id,
        status: 'completed',
        progress: 100,
        mediaFileId: startResult.mediaFileId
      }));

      dispatch(updateSessionStatus({
        sessionId,
        status: 'completed'
      }));

      onUploadComplete?.(startResult);

      // Remove file from preview after successful upload
      setTimeout(() => {
        setFiles(prev => prev.filter(f => f.id !== filePreview.id));
      }, 2000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';

      console.error('Upload error:', error);

      // Update file state with error
      setFiles(prev => prev.map(f =>
        f.id === filePreview.id
          ? {
            ...f,
            uploading: false,
            error: errorMessage,
            retryCount: (f.retryCount || 0)
          }
          : f
      ));

      // Update Redux status to error
      dispatch(updateFileStatus({
        sessionId,
        fileId: filePreview.id,
        status: 'error',
        error: errorMessage
      }));

      dispatch(updateSessionStatus({
        sessionId,
        status: 'error',
        error: errorMessage
      }));

      onUploadError?.(errorMessage);
    }
  }, [conversationId, messageId, socket, onUploadStart, onUploadProgress, onUploadComplete, onUploadError, dispatch, startChunkedUpload, user]);

  const retryUpload = useCallback((fileId: string) => {
    const file = files.find(f => f.id === fileId);
    if (file) {
      // Reset error state and retry
      setFiles(prev => prev.map(f =>
        f.id === fileId
          ? { ...f, error: undefined, retryCount: (f.retryCount || 0) + 1 }
          : f
      ));
      uploadFile(file);
    }
  }, [files, uploadFile]);

  const removeFile = useCallback((fileId: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === fileId);
      if (file?.preview) {
        URL.revokeObjectURL(file.preview);
      }
      return prev.filter(f => f.id !== fileId);
    });
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFiles(droppedFiles);
    }
  }, [disabled, handleFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      handleFiles(selectedFiles);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [handleFiles]);

  const openFileDialog = useCallback(() => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  }, [disabled]);

  return (
    <div className="media-upload">
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        onChange={handleFileSelect}
        className="hidden"
        accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar,.7z"
      />

      {/* Upload button */}
      <button
        onClick={openFileDialog}
        disabled={disabled}
        className={`p-2 rounded-lg transition-colors ${disabled
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
          }`}
        title="Upload file"
      >
        <Upload className="w-5 h-5" />
      </button>

      {/* Drag and drop overlay */}
      {isDragOver && (
        <div
          className="fixed inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center z-50"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="bg-white rounded-lg p-8 shadow-lg border-2 border-dashed border-blue-500">
            <Upload className="w-12 h-12 text-blue-500 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700">Drop files here to upload</p>
          </div>
        </div>
      )}

      {/* File previews */}
      {files.length > 0 && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-white border rounded-lg shadow-lg p-4 max-h-92 min-w-64 overflow-y-auto z-10">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Uploading files</h4>
          <div className="space-y-3">
            {files.map((filePreview) => (
              <div key={filePreview.id} className="flex items-center space-x-3">
                {/* File icon or preview */}
                <div className="flex-shrink-0">
                  {filePreview.preview ? (
                    <img
                      src={filePreview.preview}
                      alt={filePreview.file.name}
                      className="w-10 h-10 object-cover rounded"
                    />
                  ) : (
                    getFileIcon(mediaApiService.getFileType(filePreview.file.type))
                  )}
                </div>

                {/* File info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-700 truncate">
                    {filePreview.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {mediaApiService.formatFileSize(filePreview.file.size)}
                  </p>

                  {/* Progress bar */}
                  {filePreview.uploading && (
                    <div className="mt-1">
                      <div className="bg-gray-200 rounded-full h-1">
                        <div
                          className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${filePreview.progress}%` }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {filePreview.progress}% uploaded
                      </p>
                    </div>
                  )}

                  {/* Error message */}
                  {filePreview.error && (
                    <p className="text-xs text-red-500 mt-1">{filePreview.error}</p>
                  )}
                </div>

                {/* Actions */}
                <div className="flex-shrink-0 flex space-x-2">
                  {!filePreview.uploading && !filePreview.error && (
                    <button
                      onClick={() => uploadFile(filePreview)}
                      className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                    >
                      Upload
                    </button>
                  )}
                  {filePreview.error && (
                    <button
                      onClick={() => retryUpload(filePreview.id)}
                      className="text-blue-600 hover:text-blue-800 text-xs font-medium flex items-center space-x-1"
                      title="Retry upload"
                    >
                      <RefreshCw className="w-3 h-3" />
                      <span>Retry</span>
                    </button>
                  )}
                  <button
                    onClick={() => removeFile(filePreview.id)}
                    className="text-gray-400 hover:text-red-500"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaUpload;
