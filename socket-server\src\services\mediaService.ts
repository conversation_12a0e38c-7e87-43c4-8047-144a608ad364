// socket-server/src/services/mediaService.ts
import { PrismaClient } from '@prisma/client';
import { createReadStream, createWriteStream, existsSync, mkdirSync, statSync } from 'fs';
import { join, extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { createHash } from 'crypto';
import { pipeline } from 'stream/promises';

export interface MediaUploadData {
  conversationId: string;
  uploaderId: string;
  originalFilename: string;
  fileType: 'image' | 'document' | 'audio' | 'video' | 'archive' | 'other';
  mimeType: string;
  fileSize: number;
  wrappedFileKey: string;
  fileNonce: string;
  thumbnailNonce?: string;
}

export interface ChunkUploadData {
  uploadSession: string;
  chunkNumber: number;
  totalChunks: number;
  chunkData: Buffer;
  chunkHash: string;
}

export interface MediaDownloadToken {
  mediaFileId: string;
  downloadToken: string;
  expiresAt: Date;
  maxDownloads: number;
}

export class MediaService {
  private prisma: PrismaClient;
  private mediaStoragePath: string;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.mediaStoragePath = process.env.MEDIA_STORAGE_PATH || './media_storage';
    
    // Ensure media storage directory exists
    if (!existsSync(this.mediaStoragePath)) {
      mkdirSync(this.mediaStoragePath, { recursive: true });
    }
  }

  /**
   * Start chunked upload session
   */
  async startChunkedUpload(data: MediaUploadData): Promise<{ uploadSession: string; mediaFileId: string }> {
    const uploadSession = uuidv4();
    const mediaFileId = uuidv4();

    // Verify conversation exists and user has access
    // TODO: For testing purposes, we'll skip conversation validation
    // In production, uncomment the following code:
    /*
    const conversation = await this.prisma.conversation.findFirst({
      where: {
        id: data.conversationId,
        participants: {
          some: {
            userId: data.uploaderId,
            is_active: true
          }
        }
      }
    });

    if (!conversation) {
      throw new Error('Conversation not found or access denied');
    }
    */

    // Create media file record with temporary status
    await this.prisma.media_files.create({
      data: {
        id: mediaFileId,
        original_filename: data.originalFilename,
        file_type: data.fileType,
        mime_type: data.mimeType,
        file_size: BigInt(data.fileSize),
        encrypted_file_path: '', // Will be set after upload completion
        wrapped_file_key: data.wrappedFileKey,
        file_nonce: data.fileNonce,
        thumbnail_nonce: data.thumbnailNonce || '',
        processing_status: 'temporary',
        virus_scan_status: 'pending',
        created_at: new Date(),
        updated_at: new Date(),
        uploader_id: data.uploaderId
      }
    });

    return { uploadSession, mediaFileId };
  }

  /**
   * Upload chunk
   */
  async uploadChunk(data: ChunkUploadData, mediaFileId?: string): Promise<{ success: boolean; isComplete: boolean }> {
    // Verify chunk hash
    const calculatedHash = createHash('sha256').update(data.chunkData).digest('hex');
    if (calculatedHash !== data.chunkHash) {
      throw new Error('Chunk integrity check failed');
    }

    // Store chunk
    await this.prisma.media_chunks.create({
      data: {
        id: uuidv4(),
        upload_session: data.uploadSession,
        chunk_number: data.chunkNumber,
        total_chunks: data.totalChunks,
        chunk_data: data.chunkData,
        chunk_hash: data.chunkHash,
        uploaded_at: new Date()
      }
    });

    // Check if all chunks are uploaded
    const uploadedChunks = await this.prisma.media_chunks.count({
      where: { upload_session: data.uploadSession }
    });

    const isComplete = uploadedChunks === data.totalChunks;

    if (isComplete) {
      await this.assembleFile(data.uploadSession, mediaFileId);
    }

    return { success: true, isComplete };
  }

  /**
   * Assemble file from chunks
   */
  private async assembleFile(uploadSession: string, mediaFileId?: string): Promise<void> {
    // Get all chunks for this session
    const chunks = await this.prisma.media_chunks.findMany({
      where: { upload_session: uploadSession },
      orderBy: { chunk_number: 'asc' }
    });

    if (chunks.length === 0) {
      throw new Error('No chunks found for upload session');
    }

    // Create file path
    const fileName = `${uploadSession}_${Date.now()}`;
    const filePath = join(this.mediaStoragePath, fileName);

    // Assemble file
    const writeStream = createWriteStream(filePath);
    
    try {
      for (const chunk of chunks) {
        writeStream.write(chunk.chunk_data);
      }
      writeStream.end();

      // Wait for the write stream to finish
      await new Promise<void>((resolve, reject) => {
        writeStream.on('finish', () => resolve());
        writeStream.on('error', reject);
      });

      // Update media file with path
      if (mediaFileId) {
        await this.prisma.media_files.update({
          where: { id: mediaFileId },
          data: {
            encrypted_file_path: filePath,
            processing_status: 'completed'
          }
        });
      } else {
        // If no mediaFileId provided, try to find the media file by upload session
        // This is a fallback for existing uploads
        const mediaFile = await this.prisma.media_files.findFirst({
          where: {
            encrypted_file_path: '',
            processing_status: 'temporary',
            created_at: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Within last 24 hours
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        });

        if (mediaFile) {
          // Update the specific media file record with the assembled file path
          await this.prisma.media_files.update({
            where: { id: mediaFile.id },
            data: {
              encrypted_file_path: filePath,
              processing_status: 'completed'
            }
          });
        }
      }
      
      // Clean up chunks
      await this.prisma.media_chunks.deleteMany({
        where: { upload_session: uploadSession }
      });

    } catch (error) {
      writeStream.destroy();
      throw error;
    }
  }

  /**
   * Create download token
   */
  async createDownloadToken(mediaFileId: string, userId: string, maxDownloads: number = 5): Promise<MediaDownloadToken> {
    // For testing purposes, temporarily bypass the message relationship check
    const mediaFile = await this.prisma.media_files.findFirst({
      where: {
        id: mediaFileId,
        uploader_id: userId
      }
    });

    if (!mediaFile) {
      throw new Error('Media file not found or access denied');
    }

    const downloadToken = uuidv4();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    await this.prisma.media_downloads.create({
      data: {
        id: uuidv4(),
        download_token: downloadToken,
        expires_at: expiresAt,
        download_count: 0,
        max_downloads: maxDownloads,
        ip_address: '0.0.0.0', // Will be set by the endpoint
        user_agent: '', // Will be set by the endpoint
        created_at: new Date(),
        downloaded_by_id: userId,
        media_file_id: mediaFileId
      }
    });

    return {
      mediaFileId,
      downloadToken,
      expiresAt,
      maxDownloads
    };
  }

  /**
   * Get media file for streaming
   */
  async getMediaFileForStreaming(downloadToken: string, ipAddress: string, userAgent: string): Promise<{
    filePath: string;
    fileName: string;
    mimeType: string;
    fileSize: number;
  }> {
    // Verify download token
    const download = await this.prisma.media_downloads.findFirst({
      where: {
        download_token: downloadToken,
        expires_at: { gt: new Date() }
      },
      include: {
        media_files: true
      }
    });

    if (!download) {
      throw new Error('Invalid or expired download token');
    }

    if (download.download_count >= download.max_downloads) {
      throw new Error('Download limit exceeded');
    }

    // Update download count
    await this.prisma.media_downloads.update({
      where: { id: download.id },
      data: {
        download_count: download.download_count + 1,
        last_downloaded_at: new Date(),
        ip_address: ipAddress,
        user_agent: userAgent
      }
    });

    return {
      filePath: download.media_files.encrypted_file_path,
      fileName: download.media_files.original_filename,
      mimeType: download.media_files.mime_type,
      fileSize: Number(download.media_files.file_size)
    };
  }

  /**
   * Get media file info
   */
  async getMediaFileInfo(mediaFileId: string, userId: string): Promise<any> {
    // For testing purposes, temporarily bypass the message relationship check
    const mediaFile = await this.prisma.media_files.findFirst({
      where: {
        id: mediaFileId,
        uploader_id: userId
      }
    });

    if (!mediaFile) {
      throw new Error('Media file not found or access denied');
    }

    return {
      id: mediaFile.id,
      originalFilename: mediaFile.original_filename,
      fileType: mediaFile.file_type,
      mimeType: mediaFile.mime_type,
      fileSize: Number(mediaFile.file_size),
      processingStatus: mediaFile.processing_status,
      virusScanStatus: mediaFile.virus_scan_status,
      createdAt: mediaFile.created_at,
      hasThumbnail: !!mediaFile.encrypted_thumbnail_path
    };
  }

  /**
   * Create streaming response
   */
  createStreamingResponse(filePath: string, range?: string): {
    stream: NodeJS.ReadableStream;
    start: number;
    end: number;
    total: number;
    contentLength: number;
  } {
    const stat = statSync(filePath);
    const total = stat.size;

    let start = 0;
    let end = total - 1;

    if (range) {
      const parts = range.replace(/bytes=/, '').split('-');
      start = parseInt(parts[0], 10);
      end = parts[1] ? parseInt(parts[1], 10) : total - 1;
    }

    const contentLength = end - start + 1;
    const stream = createReadStream(filePath, { start, end });

    return {
      stream,
      start,
      end,
      total,
      contentLength
    };
  }
}