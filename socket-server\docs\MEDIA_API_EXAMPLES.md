# Media Streaming API - cURL Examples

This document provides comprehensive cURL examples for the Media Streaming API implemented in the Socket-server.

## Base URL
```
http://localhost:7000/api/media
```

## Authentication
All authenticated endpoints require:
- `Authorization: Bearer <token>` header
- `X-User-Id: <user-uuid>` header (for simplified auth in this example)

## API Endpoints

### 1. Health Check

Check if the media API is running:

```bash
curl -X GET "http://localhost:7000/api/media/health"
```

**Response:**
```json
{
  "success": true,
  "service": "Media Streaming API",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 2. Start Chunked Upload

Initialize a chunked upload session:

```bash
curl -X POST "http://localhost:7000/api/media/upload/chunked/start" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -H "X-User-Id: d4f0e4d1-0367-45bc-a42e-cf622c695a48" \
  -d '{
    "conversationId": "987fcdeb-51a2-43d1-9f12-123456789abc",
    "originalFilename": "test_file.txt",
    "fileType": "document",
    "mimeType": "text/plain",
    "fileSize": 37,
    "wrappedFileKey": "encrypted_file_key_base64_encoded",
    "fileNonce": "nonce_base64_encoded",
    "thumbnailNonce": "thumbnail_nonce_base64_encoded"
  }'
```

**Response:**
```json
{
  "success": true,
  "uploadSession": "550e8400-e29b-41d4-a716-446655440000",
  "mediaFileId": "660e8400-e29b-41d4-a716-446655440001"
}
```

### 3. Upload File Chunk

Upload a chunk of the file:

```bash
curl -X POST "http://localhost:7000/api/media/upload/chunked/chunk" \
  -H "Authorization: Bearer test-token" \
  -H "X-User-Id: d4f0e4d1-0367-45bc-a42e-cf622c695a48" \
  -F "chunk=@test_file.txt" \
  -F "uploadSession=550e8400-e29b-41d4-a716-446655440000" \
  -F "chunkNumber=0" \
  -F "totalChunks=1" \
  -F "chunkHash=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
```

**Response (chunk uploaded):**
```json
{
  "success": true,
  "isComplete": false,
  "message": "Chunk uploaded successfully"
}
```

**Response (upload complete):**
```json
{
  "success": true,
  "isComplete": true,
  "message": "Upload completed"
}
```

### 4. Upload Multiple Chunks (Example Script)

Here's a bash script example for uploading multiple chunks:

```bash
#!/bin/bash

# Configuration
UPLOAD_SESSION="550e8400-e29b-41d4-a716-446655440000"
USER_ID="d4f0e4d1-0367-45bc-a42e-cf622c695a48"
TOKEN="test-token"
BASE_URL="http://localhost:7000/api/media"

# Upload chunks
for i in {0..2}; do
  echo "Uploading chunk $i..."
  
  curl -X POST "$BASE_URL/upload/chunked/chunk" \
    -H "Authorization: Bearer $TOKEN" \
    -H "X-User-Id: $USER_ID" \
    -F "chunk=@chunk_$i.bin" \
    -F "uploadSession=$UPLOAD_SESSION" \
    -F "chunkNumber=$i" \
    -F "totalChunks=3" \
    -F "chunkHash=$(sha256sum chunk_$i.bin | cut -d' ' -f1)"
    
  echo "Chunk $i uploaded"
done
```

### 5. Get Media File Information

Retrieve information about a media file:

```bash
curl -X GET "http://localhost:7000/api/media/info/660e8400-e29b-41d4-a716-446655440001" \
  -H "Authorization: Bearer test-token" \
  -H "X-User-Id: d4f0e4d1-0367-45bc-a42e-cf622c695a48"
```

**Response:**
```json
{
  "success": true,
  "mediaFile": {
    "id": "660e8400-e29b-41d4-a716-446655440001",
    "originalFilename": "test_file.txt",
    "fileType": "document",
    "mimeType": "text/plain",
    "fileSize": 37,
    "processingStatus": "completed",
    "virusScanStatus": "clean",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "hasThumbnail": false
  }
}
```

### 6. Create Download Token

Generate a secure download token:

```bash
curl -X POST "http://localhost:7000/api/media/download/token/660e8400-e29b-41d4-a716-446655440001" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -H "X-User-Id: d4f0e4d1-0367-45bc-a42e-cf622c695a48" \
  -d '{
    "maxDownloads": 5
  }'
```

**Response:**
```json
{
  "success": true,
  "downloadToken": "770e8400-e29b-41d4-a716-446655440002",
  "expiresAt": "2024-01-16T10:30:00.000Z",
  "maxDownloads": 5
}
```

### 7. Stream Media File

Download/stream the media file using the download token:

```bash
# Full download
curl -X GET "http://localhost:7000/api/media/stream/770e8400-e29b-41d4-a716-446655440002" \
  -o "downloaded_test_file.txt"
```

```bash
# Range request (partial download)
curl -X GET "http://localhost:7000/api/media/stream/770e8400-e29b-41d4-a716-446655440002" \
  -H "Range: bytes=0-10" \
  -o "partial_test_file.txt"
```

```bash
# Stream with progress
curl -X GET "http://localhost:7000/api/media/stream/770e8400-e29b-41d4-a716-446655440002" \
  --progress-bar \
  -o "test_file_with_progress.txt"
```

## Complete Upload Workflow Example

Here's a complete example of uploading a file:

```bash
#!/bin/bash

# Configuration
USER_ID="d4f0e4d1-0367-45bc-a42e-cf622c695a48"
CONVERSATION_ID="987fcdeb-51a2-43d1-9f12-123456789abc"
TOKEN="test-token"
BASE_URL="http://localhost:7000/api/media"
FILE_PATH="./test_file.txt"

# Step 1: Get file information
FILE_SIZE=$(stat -c%s "$FILE_PATH")
FILE_NAME=$(basename "$FILE_PATH")
MIME_TYPE="text/plain"

echo "Uploading file: $FILE_NAME ($FILE_SIZE bytes)"

# Step 2: Start chunked upload
UPLOAD_RESPONSE=$(curl -s -X POST "$BASE_URL/upload/chunked/start" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-User-Id: $USER_ID" \
  -d "{
    \"conversationId\": \"$CONVERSATION_ID\",
    \"originalFilename\": \"$FILE_NAME\",
    \"fileType\": \"document\",
    \"mimeType\": \"$MIME_TYPE\",
    \"fileSize\": $FILE_SIZE,
    \"wrappedFileKey\": \"encrypted_key_placeholder\",
    \"fileNonce\": \"nonce_placeholder\"
  }")

UPLOAD_SESSION=$(echo $UPLOAD_RESPONSE | jq -r '.uploadSession')
MEDIA_FILE_ID=$(echo $UPLOAD_RESPONSE | jq -r '.mediaFileId')

echo "Upload session: $UPLOAD_SESSION"
echo "Media file ID: $MEDIA_FILE_ID"

# Step 3: Split file into chunks and upload
CHUNK_SIZE=1048576  # 1MB chunks
TOTAL_CHUNKS=$(((FILE_SIZE + CHUNK_SIZE - 1) / CHUNK_SIZE))

echo "Total chunks: $TOTAL_CHUNKS"

for ((i=0; i<TOTAL_CHUNKS; i++)); do
  OFFSET=$((i * CHUNK_SIZE))
  
  # Create chunk file
  dd if="$FILE_PATH" of="chunk_$i.bin" bs=$CHUNK_SIZE skip=$i count=1 2>/dev/null
  
  # Calculate chunk hash
  CHUNK_HASH=$(sha256sum "chunk_$i.bin" | cut -d' ' -f1)
  
  echo "Uploading chunk $i/$((TOTAL_CHUNKS-1))..."
  
  # Upload chunk
  curl -s -X POST "$BASE_URL/upload/chunked/chunk" \
    -H "Authorization: Bearer $TOKEN" \
    -H "X-User-Id: $USER_ID" \
    -F "chunk=@chunk_$i.bin" \
    -F "uploadSession=$UPLOAD_SESSION" \
    -F "chunkNumber=$i" \
    -F "totalChunks=$TOTAL_CHUNKS" \
    -F "chunkHash=$CHUNK_HASH"
  
  # Clean up chunk file
  rm "chunk_$i.bin"
done

echo "Upload completed!"

# Step 4: Create download token
echo "Creating download token..."
TOKEN_RESPONSE=$(curl -s -X POST "$BASE_URL/download/token/$MEDIA_FILE_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-User-Id: $USER_ID" \
  -d '{"maxDownloads": 3}')

DOWNLOAD_TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.downloadToken')
echo "Download token: $DOWNLOAD_TOKEN"

# Step 5: Test download
echo "Testing download..."
curl -X GET "$BASE_URL/stream/$DOWNLOAD_TOKEN" \
  -o "downloaded_$FILE_NAME"

echo "File downloaded as: downloaded_$FILE_NAME"
```

## Error Responses

### Validation Error
```json
{
  "error": "Validation failed",
  "details": [
    {
      "field": "fileSize",
      "message": "Number must be greater than 0"
    }
  ]
}
```

### Authentication Error
```json
{
  "error": "Missing or invalid authorization header"
}
```

### Access Denied
```json
{
  "error": "Conversation not found or access denied"
}
```

### File Not Found
```json
{
  "error": "Media file not found or access denied"
}
```

### Invalid Token
```json
{
  "error": "Invalid or expired download token"
}
```

## Notes

1. **File Size Limits**: Maximum file size is 100MB
2. **Chunk Size**: Recommended chunk size is 1MB for optimal performance
3. **Token Expiry**: Download tokens expire after 24 hours
4. **Download Limits**: Each token has a configurable download limit (default: 5)
5. **Range Requests**: The streaming endpoint supports HTTP range requests for partial downloads
6. **Security**: All file content should be encrypted client-side before upload
7. **Virus Scanning**: Files are automatically scanned for viruses after upload

## Testing with Different File Types

### Image Upload
```bash
curl -X POST "http://localhost:7000/api/media/upload/chunked/start" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -H "X-User-Id: d4f0e4d1-0367-45bc-a42e-cf622c695a48" \
  -d '{
    "conversationId": "987fcdeb-51a2-43d1-9f12-123456789abc",
    "originalFilename": "photo.jpg",
    "fileType": "image",
    "mimeType": "image/jpeg",
    "fileSize": 1024000,
    "wrappedFileKey": "encrypted_key",
    "fileNonce": "nonce"
  }'
```

### Video Upload
```bash
curl -X POST "http://localhost:7000/api/media/upload/chunked/start" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -H "X-User-Id: d4f0e4d1-0367-45bc-a42e-cf622c695a48" \
  -d '{
    "conversationId": "987fcdeb-51a2-43d1-9f12-123456789abc",
    "originalFilename": "video.mp4",
    "fileType": "video",
    "mimeType": "video/mp4",
    "fileSize": 50000000,
    "wrappedFileKey": "encrypted_key",
    "fileNonce": "nonce"
  }'
```