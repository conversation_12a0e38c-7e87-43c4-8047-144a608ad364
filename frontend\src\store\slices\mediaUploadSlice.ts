// frontend/src/store/slices/mediaUploadSlice.ts
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { fileManager } from '../../utils/fileManager';

// Serializable file metadata (no File object)
export interface MediaUploadFile {
  id: string;
  fileId: string; // Reference to file in FileManager
  name: string;
  size: number;
  type: 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other';
  mimeType: string;
  preview?: string;
  status: 'pending' | 'encrypting' | 'uploading' | 'completed' | 'error';
  progress: number;
  error?: string;
  mediaFileId?: string; // Backend media file ID after successful upload
}

export interface MediaUploadSession {
  id: string;
  conversationId: string;
  messageId?: string;
  files: MediaUploadFile[];
  status: 'preparing' | 'uploading' | 'completed' | 'error';
  totalProgress: number;
  error?: string;
  createdAt: number;
  uploadSession?: string; // Upload session ID from startChunkedUpload
  mediaFileId?: string; // Media file ID from startChunkedUpload
}

interface MediaUploadState {
  sessions: Record<string, MediaUploadSession>;
  activeSessionId?: string;
  showPreview: boolean;
  showUploadDialog: boolean;
}

const initialState: MediaUploadState = {
  sessions: {},
  showPreview: false,
  showUploadDialog: false,
};

const mediaUploadSlice = createSlice({
  name: 'mediaUpload',
  initialState,
  reducers: {
    // Dialog and preview controls
    showUploadDialog: (state) => {
      state.showUploadDialog = true;
    },
    hideUploadDialog: (state) => {
      state.showUploadDialog = false;
    },
    showPreview: (state) => {
      state.showPreview = true;
    },
    hidePreview: (state) => {
      state.showPreview = false;
    },

    // Session management
    createUploadSession: (
      state,
      action: PayloadAction<{
        sessionId: string;
        conversationId: string;
        fileMetadata: Array<{
          name: string;
          size: number;
          type: string;
          lastModified: number;
          fileId: string; // FileManager ID
        }>;
      }>
    ) => {
      const { sessionId, conversationId, fileMetadata } = action.payload;

      const uploadFiles: MediaUploadFile[] = fileMetadata.map((fileMeta, index) => {
        const uploadFileId = `${sessionId}-${index}`;

        return {
          id: uploadFileId,
          fileId: fileMeta.fileId, // FileManager ID
          name: fileMeta.name,
          size: fileMeta.size,
          type: determineFileType(fileMeta.type),
          mimeType: fileMeta.type,
          status: 'pending' as const,
          progress: 0,
        };
      });

      state.sessions[sessionId] = {
        id: sessionId,
        conversationId,
        files: uploadFiles,
        status: 'preparing',
        totalProgress: 0,
        createdAt: Date.now(),
      };

      state.activeSessionId = sessionId;
    },

    // Store startChunkedUpload response
    storeChunkedUploadResponse: (
      state,
      action: PayloadAction<{
        sessionId: string;
        uploadSession: string;
        mediaFileId: string;
      }>
    ) => {
      const { sessionId, uploadSession, mediaFileId } = action.payload;
      const session = state.sessions[sessionId];
      
      if (session) {
        // Store the response data in the session
        session.uploadSession = uploadSession;
        session.mediaFileId = mediaFileId;
        session.status = 'uploading';
      }
    },

    storeSessionFiles: (
      state,
      action: PayloadAction<{
        sessionId: string;
        files: File[];
      }>
    ) => {
      const { sessionId, files } = action.payload;
      const session = state.sessions[sessionId];
      if (session) {
        session.files.forEach((uploadFile, index) => {
          if (index < files.length) {
            const file = files[index];
            const storedFileId = fileManager.storeFile(file, uploadFile.id);
            uploadFile.fileId = storedFileId;
          }
        });
      }
    },

    updateSessionMessageId: (
      state,
      action: PayloadAction<{ sessionId: string; messageId: string }>
    ) => {
      const { sessionId, messageId } = action.payload;
      if (state.sessions[sessionId]) {
        state.sessions[sessionId].messageId = messageId;
      }
    },

    removeFileFromSession: (
      state,
      action: PayloadAction<{ sessionId: string; fileId: string }>
    ) => {
      const { sessionId, fileId } = action.payload;
      const session = state.sessions[sessionId];
      if (session) {
        const fileToRemove = session.files.find(f => f.id === fileId);
        if (fileToRemove) {
          // Remove file from FileManager
          fileManager.removeFile(fileToRemove.fileId);
          // Clean up preview URL if exists
          if (fileToRemove.preview) {
            URL.revokeObjectURL(fileToRemove.preview);
          }
        }
        
        session.files = session.files.filter(f => f.id !== fileId);
        if (session.files.length === 0) {
          delete state.sessions[sessionId];
          if (state.activeSessionId === sessionId) {
            state.activeSessionId = undefined;
          }
        }
      }
    },

    // File upload progress
    updateFileStatus: (
      state,
      action: PayloadAction<{
        sessionId: string;
        fileId: string;
        status: MediaUploadFile['status'];
        progress?: number;
        error?: string;
        mediaFileId?: string;
      }>
    ) => {
      const { sessionId, fileId, status, progress, error, mediaFileId } = action.payload;
      const session = state.sessions[sessionId];
      if (session) {
        const file = session.files.find(f => f.id === fileId);
        if (file) {
          file.status = status;
          if (progress !== undefined) file.progress = progress;
          if (error !== undefined) file.error = error;
          if (mediaFileId !== undefined) file.mediaFileId = mediaFileId;
        }
        
        // Update session total progress
        const totalProgress = session.files.reduce((sum, f) => sum + f.progress, 0) / session.files.length;
        session.totalProgress = Math.round(totalProgress);
        
        // Update session status
        const allCompleted = session.files.every(f => f.status === 'completed');
        const hasError = session.files.some(f => f.status === 'error');
        
        if (allCompleted) {
          session.status = 'completed';
        } else if (hasError) {
          session.status = 'error';
        } else {
          session.status = 'uploading';
        }
      }
    },

    updateSessionStatus: (
      state,
      action: PayloadAction<{
        sessionId: string;
        status: MediaUploadSession['status'];
        error?: string;
      }>
    ) => {
      const { sessionId, status, error } = action.payload;
      const session = state.sessions[sessionId];
      if (session) {
        session.status = status;
        if (error !== undefined) session.error = error;
      }
    },

    // File preview management
    setFilePreview: (
      state,
      action: PayloadAction<{
        sessionId: string;
        fileId: string;
        preview: string;
      }>
    ) => {
      const { sessionId, fileId, preview } = action.payload;
      const session = state.sessions[sessionId];
      if (session) {
        const file = session.files.find(f => f.id === fileId);
        if (file) {
          file.preview = preview;
        }
      }
    },

    // Cleanup
    clearSession: (state, action: PayloadAction<string>) => {
      const sessionId = action.payload;
      const session = state.sessions[sessionId];
      if (session) {
        // Clean up files from FileManager and preview URLs
        session.files.forEach(file => {
          fileManager.removeFile(file.fileId);
          if (file.preview) {
            URL.revokeObjectURL(file.preview);
          }
        });
      }
      
      delete state.sessions[sessionId];
      if (state.activeSessionId === sessionId) {
        state.activeSessionId = undefined;
      }
    },

    clearCompletedSessions: (state) => {
      const now = Date.now();
      const oneHourAgo = now - 60 * 60 * 1000; // 1 hour

      Object.keys(state.sessions).forEach(sessionId => {
        const session = state.sessions[sessionId];
        if (
          session.status === 'completed' && 
          session.createdAt < oneHourAgo
        ) {
          // Clean up files from FileManager and preview URLs
          session.files.forEach(file => {
            fileManager.removeFile(file.fileId);
            if (file.preview) {
              URL.revokeObjectURL(file.preview);
            }
          });
          
          delete state.sessions[sessionId];
          if (state.activeSessionId === sessionId) {
            state.activeSessionId = undefined;
          }
        }
      });
    },

    // Retry functionality
    retryFailedUploads: (state, action: PayloadAction<string>) => {
      const sessionId = action.payload;
      const session = state.sessions[sessionId];
      if (session) {
        session.files.forEach(file => {
          if (file.status === 'error') {
            file.status = 'pending';
            file.progress = 0;
            file.error = undefined;
          }
        });
        session.status = 'preparing';
        session.error = undefined;
      }
    },
  },
});

// Helper function to determine file type
function determineFileType(mimeType: string): MediaUploadFile['type'] {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) {
    return 'document';
  }
  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('7z')) {
    return 'archive';
  }
  return 'other';
}

export const {
  showUploadDialog,
  hideUploadDialog,
  showPreview,
  hidePreview,
  createUploadSession,
  storeChunkedUploadResponse,
  storeSessionFiles,
  updateSessionMessageId,
  removeFileFromSession,
  updateFileStatus,
  updateSessionStatus,
  setFilePreview,
  clearSession,
  clearCompletedSessions,
  retryFailedUploads,
} = mediaUploadSlice.actions;

export default mediaUploadSlice.reducer;

// Selectors
export const selectActiveSession = (state: { mediaUpload: MediaUploadState }) => {
  const { activeSessionId, sessions } = state.mediaUpload;
  return activeSessionId ? sessions[activeSessionId] : undefined;
};

export const selectSessionById = (sessionId: string) => (state: { mediaUpload: MediaUploadState }) => {
  return state.mediaUpload.sessions[sessionId];
};

export const selectShowUploadDialog = (state: { mediaUpload: MediaUploadState }) => {
  return state.mediaUpload.showUploadDialog;
};

export const selectShowPreview = (state: { mediaUpload: MediaUploadState }) => {
  return state.mediaUpload.showPreview;
};

// Helper function to get File object from MediaUploadFile
export const getFileFromMediaUploadFile = (mediaFile: MediaUploadFile): File | undefined => {
  return fileManager.getFile(mediaFile.fileId);
};

// Helper function to get all files from a session
export const getFilesFromSession = (session: MediaUploadSession): File[] => {
  return session.files
    .map(mediaFile => fileManager.getFile(mediaFile.fileId))
    .filter((file): file is File => file !== undefined);
};
