// frontend/src/store/slices/callingSlice.ts
import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';

// Types
export interface CallParticipant {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

export interface CurrentCall {
  id: string | null;
  type: 'audio' | 'video' | null;
  status: 'idle' | 'initiating' | 'ringing' | 'active' | 'ending';
  participants: CallParticipant[];
  startTime: number | null;
  duration: number;
  conversationId: string | null;
  isInitiator: boolean;
}

export interface MediaPermissions {
  audio: boolean;
  video: boolean;
  audioOutput: boolean;
}

export interface CallControls {
  isAudioMuted: boolean;
  isVideoMuted: boolean;
  isSpeakerOn: boolean;
  isScreenSharing: boolean;
}

export interface IncomingCallData {
  callId: string;
  caller: CallParticipant;
  callType: 'audio' | 'video';
  conversationId: string;
}

export interface CallingState {
  // Connection State
  isConnected: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected';
  
  // Call State
  currentCall: CurrentCall;
  
  // Media State
  localStream: MediaStream | null;
  remoteStream: MediaStream | null;
  mediaPermissions: MediaPermissions;
  
  // Control State
  controls: CallControls;
  
  // UI State
  showIncomingCall: boolean;
  showActiveCall: boolean;
  incomingCallData: IncomingCallData | null;
  
  // Error State
  error: string | null;
  lastError: string | null;
  
  // WebRTC State
  peerConnection: RTCPeerConnection | null;
}

// Initial state
const initialState: CallingState = {
  // Connection State
  isConnected: false,
  connectionStatus: 'disconnected',
  
  // Call State
  currentCall: {
    id: null,
    type: null,
    status: 'idle',
    participants: [],
    startTime: null,
    duration: 0,
    conversationId: null,
    isInitiator: false,
  },
  
  // Media State
  localStream: null,
  remoteStream: null,
  mediaPermissions: {
    audio: false,
    video: false,
    audioOutput: false,
  },
  
  // Control State
  controls: {
    isAudioMuted: false,
    isVideoMuted: false,
    isSpeakerOn: true,
    isScreenSharing: false,
  },
  
  // UI State
  showIncomingCall: false,
  showActiveCall: false,
  incomingCallData: null,
  
  // Error State
  error: null,
  lastError: null,
  
  // WebRTC State
  peerConnection: null,
};

// Async thunks for call operations
export const initiateCall = createAsyncThunk(
  'calling/initiateCall',
  async (
    { conversationId, callType }: { conversationId: string; callType: 'audio' | 'video' },
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      
      // Check if already in a call
      if (state.calling.currentCall.status !== 'idle') {
        throw new Error('Already in a call');
      }

      return {
        conversationId,
        callType,
        callId: `call-${Date.now()}`, // Temporary ID, will be replaced by server
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to initiate call');
    }
  }
);

export const answerCall = createAsyncThunk(
  'calling/answerCall',
  async (callId: string, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      
      if (!state.calling.incomingCallData) {
        throw new Error('No incoming call to answer');
      }

      return { callId };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to answer call');
    }
  }
);

export const declineCall = createAsyncThunk(
  'calling/declineCall',
  async (callId: string, { rejectWithValue }) => {
    try {
      return { callId };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to decline call');
    }
  }
);

export const endCall = createAsyncThunk(
  'calling/endCall',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { currentCall } = state.calling;

      if (!currentCall.id) {
        throw new Error('No active call to end');
      }

      return { callId: currentCall.id };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to end call');
    }
  }
);

export const toggleAudio = createAsyncThunk(
  'calling/toggleAudio',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { localStream, currentCall } = state.calling;

      if (!localStream) {
        throw new Error('No local stream available');
      }

      const audioTracks = localStream.getAudioTracks();
      if (audioTracks.length === 0) {
        throw new Error('No audio tracks found');
      }

      const audioTrack = audioTracks[0];
      audioTrack.enabled = !audioTrack.enabled;
      const isAudioMuted = !audioTrack.enabled;

      return { isAudioMuted, callId: currentCall.id };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to toggle audio');
    }
  }
);

export const toggleVideo = createAsyncThunk(
  'calling/toggleVideo',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { localStream, currentCall } = state.calling;

      if (!localStream) {
        throw new Error('No local stream available');
      }

      const videoTracks = localStream.getVideoTracks();
      if (videoTracks.length === 0) {
        throw new Error('No video tracks found');
      }

      const videoTrack = videoTracks[0];
      videoTrack.enabled = !videoTrack.enabled;
      const isVideoMuted = !videoTrack.enabled;

      return { isVideoMuted, callId: currentCall.id };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to toggle video');
    }
  }
);

// Calling slice
const callingSlice = createSlice({
  name: 'calling',
  initialState,
  reducers: {
    // Connection actions
    setConnectionStatus: (state, action: PayloadAction<'disconnected' | 'connecting' | 'connected'>) => {
      state.connectionStatus = action.payload;
      state.isConnected = action.payload === 'connected';
    },

    // Call management actions
    receiveIncomingCall: (state, action: PayloadAction<IncomingCallData>) => {
      state.incomingCallData = action.payload;
      state.showIncomingCall = true;
      state.currentCall.status = 'ringing';
    },

    updateCallStatus: (state, action: PayloadAction<{ callId?: string; status: CurrentCall['status'] }>) => {
      if (action.payload.callId) {
        state.currentCall.id = action.payload.callId;
      }
      state.currentCall.status = action.payload.status;
    },

    updateCallDuration: (state, action: PayloadAction<number>) => {
      state.currentCall.duration = action.payload;
    },

    startCallTimer: (state) => {
      state.currentCall.startTime = Date.now();
    },

    // Media actions
    setLocalStream: (state, action: PayloadAction<MediaStream | null>) => {
      state.localStream = action.payload;
    },

    setRemoteStream: (state, action: PayloadAction<MediaStream | null>) => {
      state.remoteStream = action.payload;
    },

    updateMediaPermissions: (state, action: PayloadAction<Partial<MediaPermissions>>) => {
      state.mediaPermissions = { ...state.mediaPermissions, ...action.payload };
    },

    setPeerConnection: (state, action: PayloadAction<RTCPeerConnection | null>) => {
      state.peerConnection = action.payload;
    },

    // UI actions
    showIncomingCallModal: (state, action: PayloadAction<IncomingCallData>) => {
      state.showIncomingCall = true;
      state.incomingCallData = action.payload;
    },

    hideIncomingCallModal: (state) => {
      state.showIncomingCall = false;
      state.incomingCallData = null;
    },

    showActiveCallInterface: (state) => {
      state.showActiveCall = true;
      state.showIncomingCall = false;
    },

    hideActiveCallInterface: (state) => {
      state.showActiveCall = false;
    },

    // Call state actions
    setCurrentCall: (state, action: PayloadAction<Partial<CurrentCall>>) => {
      state.currentCall = { ...state.currentCall, ...action.payload };
    },

    // Error actions
    setCallingError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.lastError = action.payload;
    },

    clearCallingError: (state) => {
      state.error = null;
    },

    // Reset state
    resetCallState: (state) => {
      // Reset all call-related state (following HTML test cleanup pattern)
      state.currentCall = {
        id: null,
        type: null,
        status: 'idle',
        participants: [],
        startTime: null,
        duration: 0,
        conversationId: null,
      };
      state.localStream = null;
      state.remoteStream = null;
      state.showIncomingCall = false;
      state.showActiveCall = false;
      state.incomingCallData = null;
      state.controls = {
        isAudioMuted: false,
        isVideoMuted: false,
        isSpeakerOn: true,
        isScreenSharing: false,
      };
      state.peerConnection = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Handle async thunk results
    builder
      .addCase(initiateCall.pending, (state) => {
        state.currentCall.status = 'initiating';
        state.error = null;
      })
      .addCase(initiateCall.fulfilled, (state, action) => {
        state.currentCall.conversationId = action.payload.conversationId;
        state.currentCall.type = action.payload.callType;
        state.currentCall.id = action.payload.callId;
        state.showActiveCall = true;
      })
      .addCase(initiateCall.rejected, (state, action) => {
        state.currentCall.status = 'idle';
        state.error = action.payload as string;
      })
      .addCase(answerCall.fulfilled, (state, action) => {
        state.currentCall.status = 'active';
        state.showIncomingCall = false;
        state.showActiveCall = true;
      })
      .addCase(answerCall.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      .addCase(declineCall.fulfilled, (state) => {
        callingSlice.caseReducers.resetCallState(state);
      })
      .addCase(endCall.fulfilled, (state) => {
        callingSlice.caseReducers.resetCallState(state);
      })
      .addCase(toggleAudio.fulfilled, (state, action) => {
        state.controls.isAudioMuted = action.payload.isAudioMuted;
      })
      .addCase(toggleVideo.fulfilled, (state, action) => {
        state.controls.isVideoMuted = action.payload.isVideoMuted;
      });
  },
});

// Export actions
export const {
  setConnectionStatus,
  receiveIncomingCall,
  updateCallStatus,
  updateCallDuration,
  startCallTimer,
  setLocalStream,
  setRemoteStream,
  updateMediaPermissions,
  setPeerConnection,
  showIncomingCallModal,
  hideIncomingCallModal,
  showActiveCallInterface,
  hideActiveCallInterface,
  setCurrentCall,
  setCallingError,
  clearCallingError,
  resetCallState,
} = callingSlice.actions;

// Selectors
export const selectCallingState = (state: RootState) => state.calling;
export const selectCurrentCall = (state: RootState) => state.calling.currentCall;
export const selectCallControls = (state: RootState) => state.calling.controls;
export const selectIsInCall = (state: RootState) => state.calling.currentCall.status !== 'idle';
export const selectShowIncomingCall = (state: RootState) => state.calling.showIncomingCall;
export const selectShowActiveCall = (state: RootState) => state.calling.showActiveCall;
export const selectIncomingCallData = (state: RootState) => state.calling.incomingCallData;

export default callingSlice.reducer;
