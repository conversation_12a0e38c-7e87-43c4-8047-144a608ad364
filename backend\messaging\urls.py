# backend/messaging/urls.py
from django.urls import path
from . import views

urlpatterns = [
    # Conversation endpoints
    path('conversations/', views.list_conversations, name='list_conversations'),
    path('conversations/create/', views.create_conversation, name='create_conversation'),
    path('conversations/<uuid:conversation_id>/messages/', views.get_conversation_messages, name='get_conversation_messages'),
    path('conversations/<uuid:conversation_id>/send/', views.send_message, name='send_message'),
    path('conversations/<uuid:conversation_id>/encryption-status/', views.get_conversation_encryption_status, name='get_conversation_encryption_status'),
    
    # User endpoints
    path('users/search/', views.search_users, name='search_users'),
    path('users/<uuid:user_id>/', views.get_user_profile, name='get_user_profile'),
    
    # Group management endpoints
    path('groups/create/', views.create_group, name='create_group'),
    path('groups/<uuid:conversation_id>/add-member/', views.add_group_member, name='add_group_member'),
    path('groups/<uuid:conversation_id>/remove-member/<uuid:user_id>/', views.remove_group_member, name='remove_group_member'),
    path('groups/<uuid:conversation_id>/update/', views.update_group_info, name='update_group_info'),
    path('groups/<uuid:conversation_id>/transfer-admin/', views.transfer_group_admin, name='transfer_group_admin'),
    path('groups/<uuid:conversation_id>/leave/', views.leave_group, name='leave_group'),
    
    # Group encryption endpoints
    path('groups/<uuid:conversation_id>/keys/', views.get_group_keys, name='get_group_keys'),
    path('groups/<uuid:conversation_id>/claim-key/', views.claim_group_key_endpoint, name='claim_group_key'),
    path('groups/<uuid:conversation_id>/send-message/', views.send_group_message, name='send_group_message'),
    path('messages/<uuid:message_id>/verify-signature/', views.verify_message_signature, name='verify_message_signature'),
    path('groups/<uuid:conversation_id>/rotate-keys/', views.rotate_group_keys_manual, name='rotate_group_keys_manual'),
]
