# Phase 6: Frontend Implementation - WebRTC Calling System

## Overview

This document outlines the frontend implementation requirements for integrating the verified WebRTC calling functionality from `calling-test.html` into our React/Redux production environment. The test file has demonstrated 100% working status for audio calls, video calls, proper media access termination, and synchronized media controls.

## HTML Test File Analysis

### Architecture Review

The `calling-test.html` file implements a comprehensive WebRTC calling system with the following key components:

#### 1. **Core Technologies**
- **Socket.IO Client**: Real-time communication for signaling
- **WebRTC APIs**: Peer-to-peer media streaming
- **MediaDevices API**: Camera and microphone access
- **RTCPeerConnection**: WebRTC connection management

#### 2. **Key Features Implemented**
- ✅ Audio/Video calling with full duplex communication
- ✅ Call initiation, answering, and declining
- ✅ Real-time media controls (mute/unmute, video on/off)
- ✅ Screen sharing capabilities
- ✅ Connection state monitoring
- ✅ Call duration tracking
- ✅ Proper media cleanup on call termination
- ✅ ICE candidate handling for NAT traversal
- ✅ API integration for call management

#### 3. **WebRTC Configuration**
```javascript
rtcConfig: {
    iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
    ]
}
```

#### 4. **Socket Events Handled**
- `connect/disconnect` - Connection management
- `call_initiated` - Incoming call notifications
- `call_answered/call_declined` - Call response handling
- `call_ended` - Call termination
- `webrtc_offer/webrtc_answer` - WebRTC signaling
- `webrtc_ice_candidate` - ICE candidate exchange
- `toggle_audio/toggle_video` - Media control synchronization

## Frontend Implementation Requirements

### 1. **Component Architecture**

#### Core Components Structure
```
src/
├── components/
│   ├── calling/
│   │   ├── CallManager.jsx           # Main calling orchestrator
│   │   ├── IncomingCallModal.jsx     # Incoming call UI
│   │   ├── ActiveCallInterface.jsx   # During-call controls
│   │   ├── CallControls.jsx          # Media control buttons
│   │   ├── VideoContainer.jsx        # Local/remote video display
│   │   └── CallStatusIndicator.jsx   # Connection status display
│   └── shared/
│       ├── MediaPermissionModal.jsx  # Permission requests
│       └── NotificationHandler.jsx   # Call notifications
```

#### Service Layer
```
src/
├── services/
│   ├── webrtc/
│   │   ├── WebRTCService.js          # WebRTC connection management
│   │   ├── MediaService.js           # Media stream handling
│   │   └── SignalingService.js       # Socket.IO signaling
│   └── api/
│       └── callingAPI.js             # REST API integration
```

### 2. **Redux State Management**

#### State Structure
```javascript
// Redux Store Structure
const callingState = {
  // Connection State
  isConnected: false,
  connectionStatus: 'disconnected', // 'connecting', 'connected', 'disconnected'
  
  // Call State
  currentCall: {
    id: null,
    type: null, // 'audio' | 'video'
    status: 'idle', // 'idle', 'initiating', 'ringing', 'active', 'ending'
    participants: [],
    startTime: null,
    duration: 0
  },
  
  // Media State
  localStream: null,
  remoteStream: null,
  mediaPermissions: {
    audio: false,
    video: false
  },
  
  // Control State
  controls: {
    isAudioMuted: false,
    isVideoMuted: false,
    isSpeakerOn: true,
    isScreenSharing: false
  },
  
  // UI State
  showIncomingCall: false,
  showActiveCall: false,
  incomingCallData: null,
  
  // Error State
  error: null,
  lastError: null
};
```

#### Required Actions
```javascript
// Connection Actions
CONNECT_SOCKET
DISCONNECT_SOCKET
SET_CONNECTION_STATUS

// Call Management Actions
INITIATE_CALL
RECEIVE_INCOMING_CALL
ANSWER_CALL
DECLINE_CALL
END_CALL
SET_CALL_STATUS
UPDATE_CALL_DURATION

// Media Actions
SET_LOCAL_STREAM
SET_REMOTE_STREAM
UPDATE_MEDIA_PERMISSIONS
TOGGLE_AUDIO
TOGGLE_VIDEO
TOGGLE_SPEAKER
START_SCREEN_SHARE
STOP_SCREEN_SHARE

// UI Actions
SHOW_INCOMING_CALL_MODAL
HIDE_INCOMING_CALL_MODAL
SHOW_ACTIVE_CALL_INTERFACE
HIDE_ACTIVE_CALL_INTERFACE

// Error Actions
SET_CALLING_ERROR
CLEAR_CALLING_ERROR
```

### 3. **WebRTC Service Implementation**

#### WebRTCService.js
```javascript
class WebRTCService {
  constructor() {
    this.peerConnection = null;
    this.localStream = null;
    this.remoteStream = null;
    this.rtcConfig = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    };
  }

  // Core Methods (from test file)
  async initializePeerConnection()
  async getUserMedia(constraints)
  async createOffer()
  async createAnswer()
  async handleRemoteOffer(offer)
  async handleRemoteAnswer(answer)
  async handleICECandidate(candidate)
  toggleAudioTrack()
  toggleVideoTrack()
  async startScreenShare()
  closePeerConnection()
}
```

#### MediaService.js
```javascript
class MediaService {
  // Media permission and stream management
  async requestMediaPermissions(constraints)
  async getLocalStream(type) // 'audio', 'video', 'screen'
  stopAllTracks(stream)
  toggleTrack(stream, trackType, enabled)
  getMediaConstraints(callType)
}
```

#### SignalingService.js
```javascript
class SignalingService {
  constructor(socket, store) {
    this.socket = socket;
    this.store = store;
    this.setupSocketListeners();
  }

  // Socket event handlers (from test file)
  setupSocketListeners()
  emitCallInitiation(data)
  emitCallAnswer(data)
  emitCallDecline(data)
  emitCallEnd(data)
  emitWebRTCOffer(data)
  emitWebRTCAnswer(data)
  emitICECandidate(data)
  emitMediaToggle(data)
}
```

### 4. **Component Implementation Details**

#### CallManager.jsx
```jsx
const CallManager = () => {
  const dispatch = useDispatch();
  const callingState = useSelector(state => state.calling);
  const webrtcService = useRef(new WebRTCService());
  const signalingService = useRef(null);

  // Initialize services
  useEffect(() => {
    signalingService.current = new SignalingService(socket, dispatch);
    return () => {
      webrtcService.current.closePeerConnection();
    };
  }, []);

  // Call management methods
  const initiateCall = async (recipientId, callType) => {
    // Implementation from test file logic
  };

  const answerCall = async () => {
    // Implementation from test file logic
  };

  const endCall = () => {
    // Implementation from test file logic
  };

  return (
    <>
      {callingState.showIncomingCall && <IncomingCallModal />}
      {callingState.showActiveCall && <ActiveCallInterface />}
    </>
  );
};
```

#### IncomingCallModal.jsx
```jsx
const IncomingCallModal = () => {
  const { incomingCallData } = useSelector(state => state.calling);
  const dispatch = useDispatch();

  const handleAnswer = () => {
    dispatch(answerCall());
  };

  const handleDecline = () => {
    dispatch(declineCall());
  };

  return (
    <Modal isOpen={true} className="incoming-call-modal">
      <div className="call-notification">
        <Avatar src={incomingCallData?.caller?.avatar} />
        <h3>{incomingCallData?.caller?.name}</h3>
        <p>Incoming {incomingCallData?.type} call...</p>
        <div className="call-actions">
          <Button onClick={handleDecline} variant="danger">
            Decline
          </Button>
          <Button onClick={handleAnswer} variant="success">
            Answer
          </Button>
        </div>
      </div>
    </Modal>
  );
};
```

#### ActiveCallInterface.jsx
```jsx
const ActiveCallInterface = () => {
  const callingState = useSelector(state => state.calling);
  const dispatch = useDispatch();

  return (
    <div className="active-call-interface">
      <CallStatusIndicator />
      <VideoContainer />
      <CallControls />
      <div className="call-info">
        <span className="call-duration">
          {formatDuration(callingState.currentCall.duration)}
        </span>
      </div>
    </div>
  );
};
```

#### VideoContainer.jsx
```jsx
const VideoContainer = () => {
  const localVideoRef = useRef();
  const remoteVideoRef = useRef();
  const { localStream, remoteStream, currentCall } = useSelector(state => state.calling);

  useEffect(() => {
    if (localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  useEffect(() => {
    if (remoteStream && remoteVideoRef.current) {
      remoteVideoRef.current.srcObject = remoteStream;
    }
  }, [remoteStream]);

  return (
    <div className="video-container">
      <video 
        ref={remoteVideoRef} 
        className="remote-video" 
        autoPlay 
        playsInline 
      />
      {currentCall.type === 'video' && (
        <video 
          ref={localVideoRef} 
          className="local-video" 
          autoPlay 
          playsInline 
          muted 
        />
      )}
    </div>
  );
};
```

#### CallControls.jsx
```jsx
const CallControls = () => {
  const { controls, currentCall } = useSelector(state => state.calling);
  const dispatch = useDispatch();

  return (
    <div className="call-controls">
      <Button 
        onClick={() => dispatch(toggleAudio())} 
        variant={controls.isAudioMuted ? 'danger' : 'primary'}
      >
        {controls.isAudioMuted ? '🔇' : '🎤'}
      </Button>
      
      {currentCall.type === 'video' && (
        <Button 
          onClick={() => dispatch(toggleVideo())} 
          variant={controls.isVideoMuted ? 'danger' : 'primary'}
        >
          {controls.isVideoMuted ? '📹' : '📹'}
        </Button>
      )}
      
      <Button 
        onClick={() => dispatch(toggleSpeaker())} 
        variant={controls.isSpeakerOn ? 'primary' : 'secondary'}
      >
        {controls.isSpeakerOn ? '🔊' : '🔇'}
      </Button>
      
      <Button 
        onClick={() => dispatch(startScreenShare())} 
        variant={controls.isScreenSharing ? 'success' : 'secondary'}
      >
        🖥️
      </Button>
      
      <Button 
        onClick={() => dispatch(endCall())} 
        variant="danger"
      >
        📞
      </Button>
    </div>
  );
};
```

### 5. **Integration Requirements**

#### Socket.IO Integration
```javascript
// In socket middleware or service
const setupCallingSocketEvents = (socket, store) => {
  socket.on('call_initiated', (data) => {
    store.dispatch(receiveIncomingCall(data));
    // Show notification
    showCallNotification(data);
  });

  socket.on('call_answered', (data) => {
    store.dispatch(setCallStatus('active'));
  });

  socket.on('call_declined', (data) => {
    store.dispatch(setCallStatus('idle'));
    store.dispatch(clearCurrentCall());
  });

  socket.on('call_ended', (data) => {
    store.dispatch(endCall());
  });

  // WebRTC signaling events
  socket.on('webrtc_offer', async (data) => {
    await webrtcService.handleRemoteOffer(data.offer);
  });

  socket.on('webrtc_answer', async (data) => {
    await webrtcService.handleRemoteAnswer(data.answer);
  });

  socket.on('webrtc_ice_candidate', async (data) => {
    await webrtcService.handleICECandidate(data.candidate);
  });

  // Media control events
  socket.on('toggle_audio', (data) => {
    store.dispatch(updateRemoteAudioStatus(data.enabled));
  });

  socket.on('toggle_video', (data) => {
    store.dispatch(updateRemoteVideoStatus(data.enabled));
  });
};
```

#### API Integration
```javascript
// callingAPI.js
export const callingAPI = {
  getCallDetail: (callId) => 
    api.get(`/calling/${callId}/`),
  
  getCallHistory: (conversationId) => 
    api.get(`/calling/history/?conversation_id=${conversationId}`),
  
  reportCallQuality: (callId, qualityData) => 
    api.post(`/calling/${callId}/quality/`, qualityData),
  
  updateCallStatus: (callId, status) => 
    api.patch(`/calling/${callId}/`, { status })
};
```

### 6. **Styling Requirements**

#### CSS Structure
```scss
// calling.scss
.incoming-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.active-call-interface {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000;
  display: flex;
  flex-direction: column;
  z-index: 9998;
}

.video-container {
  position: relative;
  flex: 1;
  
  .remote-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .local-video {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 200px;
    height: 150px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid #fff;
  }
}

.call-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.7);
  
  button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .local-video {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }
  
  .call-controls {
    padding: 15px;
    
    button {
      width: 45px;
      height: 45px;
      font-size: 18px;
    }
  }
}
```

### 7. **Error Handling & Edge Cases**

#### Error Scenarios to Handle
1. **Media Permission Denied**
   - Show permission request modal
   - Graceful degradation (audio-only if video denied)
   - Clear error messaging

2. **Network Connection Issues**
   - Connection timeout handling
   - Reconnection attempts
   - ICE connection failure recovery

3. **WebRTC Compatibility**
   - Browser support detection
   - Fallback mechanisms
   - Feature detection

4. **Call State Conflicts**
   - Multiple incoming calls
   - Call interruption handling
   - Proper cleanup on errors

### 8. **Socket Reconnection & Network Resilience**

#### Exponential Backoff Reconnection Strategy
```javascript
class ReconnectionManager {
  constructor(socket, maxRetries = 10) {
    this.socket = socket;
    this.maxRetries = maxRetries;
    this.retryCount = 0;
    this.baseDelay = 1000; // 1 second
    this.maxDelay = 30000; // 30 seconds
    this.reconnectTimer = null;
    this.isReconnecting = false;
  }

  calculateBackoffDelay() {
    const exponentialDelay = this.baseDelay * Math.pow(2, this.retryCount);
    const jitter = Math.random() * 0.1 * exponentialDelay;
    return Math.min(exponentialDelay + jitter, this.maxDelay);
  }

  async attemptReconnection() {
    if (this.isReconnecting || this.retryCount >= this.maxRetries) {
      return false;
    }

    this.isReconnecting = true;
    const delay = this.calculateBackoffDelay();
    
    console.log(`Reconnection attempt ${this.retryCount + 1}/${this.maxRetries} in ${delay}ms`);
    
    return new Promise((resolve) => {
      this.reconnectTimer = setTimeout(async () => {
        try {
          await this.socket.connect();
          this.resetReconnection();
          resolve(true);
        } catch (error) {
          this.retryCount++;
          this.isReconnecting = false;
          resolve(false);
        }
      }, delay);
    });
  }

  resetReconnection() {
    this.retryCount = 0;
    this.isReconnecting = false;
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  stopReconnection() {
    this.resetReconnection();
    this.retryCount = this.maxRetries;
  }
}
```

#### Network State Management
```javascript
class NetworkStateManager {
  constructor(dispatch) {
    this.dispatch = dispatch;
    this.isOnline = navigator.onLine;
    this.setupNetworkListeners();
  }

  setupNetworkListeners() {
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
  }

  handleOnline() {
    this.isOnline = true;
    this.dispatch({ type: 'NETWORK_STATUS_CHANGED', payload: { online: true } });
    // Trigger reconnection if socket is disconnected
    if (!this.socket.connected) {
      this.reconnectionManager.attemptReconnection();
    }
  }

  handleOffline() {
    this.isOnline = false;
    this.dispatch({ type: 'NETWORK_STATUS_CHANGED', payload: { online: false } });
    // Pause call attempts and show offline indicator
    this.dispatch({ type: 'SET_CALLING_ERROR', payload: 'Network connection lost' });
  }
}
```

### 9. **Server-Side Idempotency & Call State Management**

#### Idempotent Call Operations
```javascript
class IdempotentCallManager {
  constructor(apiService) {
    this.apiService = apiService;
    this.pendingOperations = new Map();
    this.operationTimeouts = new Map();
  }

  async initiateCallIdempotent(recipientId, callType, idempotencyKey = null) {
    const key = idempotencyKey || `call-${recipientId}-${Date.now()}`;
    
    // Check if operation is already pending
    if (this.pendingOperations.has(key)) {
      return this.pendingOperations.get(key);
    }

    const operationPromise = this.performCallInitiation(recipientId, callType, key);
    this.pendingOperations.set(key, operationPromise);

    // Set timeout for operation cleanup
    const timeout = setTimeout(() => {
      this.pendingOperations.delete(key);
      this.operationTimeouts.delete(key);
    }, 30000); // 30 second timeout

    this.operationTimeouts.set(key, timeout);

    try {
      const result = await operationPromise;
      return result;
    } finally {
      // Cleanup
      this.pendingOperations.delete(key);
      const timeoutId = this.operationTimeouts.get(key);
      if (timeoutId) {
        clearTimeout(timeoutId);
        this.operationTimeouts.delete(key);
      }
    }
  }

  async performCallInitiation(recipientId, callType, idempotencyKey) {
    const payload = {
      recipient_id: recipientId,
      call_type: callType,
      idempotency_key: idempotencyKey,
      timestamp: new Date().toISOString()
    };

    return await this.apiService.post('/api/calls/initiate/', payload, {
      headers: {
        'Idempotency-Key': idempotencyKey
      }
    });
  }

  // Handle duplicate call responses
  handleCallResponse(callId, response) {
    const existingCall = this.getActiveCall();
    
    if (existingCall && existingCall.id === callId) {
      // Duplicate response for existing call - ignore
      console.log(`Ignoring duplicate response for call ${callId}`);
      return false;
    }

    return true; // Process the response
  }
}
```

#### ICE Candidate Queuing
```javascript
class ICECandidateQueue {
  constructor() {
    this.candidateQueue = [];
    this.peerConnectionReady = false;
  }

  queueCandidate(candidate) {
    if (this.peerConnectionReady) {
      this.addCandidateDirectly(candidate);
    } else {
      this.candidateQueue.push(candidate);
      console.log(`Queued ICE candidate. Queue size: ${this.candidateQueue.length}`);
    }
  }

  setPeerConnectionReady(peerConnection) {
    this.peerConnectionReady = true;
    this.peerConnection = peerConnection;
    this.flushCandidateQueue();
  }

  async flushCandidateQueue() {
    console.log(`Flushing ${this.candidateQueue.length} queued ICE candidates`);
    
    while (this.candidateQueue.length > 0) {
      const candidate = this.candidateQueue.shift();
      await this.addCandidateDirectly(candidate);
    }
  }

  async addCandidateDirectly(candidate) {
    try {
      if (this.peerConnection && this.peerConnection.remoteDescription) {
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        console.log('ICE candidate added successfully');
      } else {
        // Re-queue if remote description not set yet
        this.candidateQueue.unshift(candidate);
      }
    } catch (error) {
      console.error('Failed to add ICE candidate:', error);
    }
  }

  clearQueue() {
    this.candidateQueue = [];
    this.peerConnectionReady = false;
  }
}
```

### 10. **PeerConnection Lifecycle & Track Management**

#### Robust PeerConnection Management
```javascript
class RobustWebRTCService {
  constructor() {
    this.peerConnection = null;
    this.localStream = null;
    this.remoteStream = null;
    this.senders = new Map(); // Track senders by track type
    this.transceivers = new Map(); // Track transceivers
    this.negotiationInProgress = false;
    this.pendingNegotiation = false;
    this.iceGatheringComplete = false;
  }

  async initializePeerConnection() {
    if (this.peerConnection) {
      this.closePeerConnection();
    }

    this.peerConnection = new RTCPeerConnection(this.rtcConfig);
    this.setupPeerConnectionEventHandlers();
    this.iceGatheringComplete = false;
    
    return this.peerConnection;
  }

  setupPeerConnectionEventHandlers() {
    this.peerConnection.addEventListener('negotiationneeded', this.handleNegotiationNeeded.bind(this));
    this.peerConnection.addEventListener('icecandidate', this.handleICECandidate.bind(this));
    this.peerConnection.addEventListener('icegatheringstatechange', this.handleICEGatheringStateChange.bind(this));
    this.peerConnection.addEventListener('connectionstatechange', this.handleConnectionStateChange.bind(this));
    this.peerConnection.addEventListener('track', this.handleRemoteTrack.bind(this));
  }

  async handleNegotiationNeeded() {
    if (this.negotiationInProgress) {
      this.pendingNegotiation = true;
      return;
    }

    this.negotiationInProgress = true;
    
    try {
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);
      
      // Send offer through signaling
      this.signalingService.emitWebRTCOffer({
        call_id: this.currentCallId,
        offer: offer
      });
      
    } catch (error) {
      console.error('Negotiation failed:', error);
    } finally {
      this.negotiationInProgress = false;
      
      if (this.pendingNegotiation) {
        this.pendingNegotiation = false;
        // Retry negotiation
        setTimeout(() => this.handleNegotiationNeeded(), 100);
      }
    }
  }

  async replaceTrackSafely(trackType, newTrack) {
    const sender = this.senders.get(trackType);
    
    if (!sender) {
      console.error(`No sender found for track type: ${trackType}`);
      return false;
    }

    try {
      // Create rollback point
      const currentTrack = sender.track;
      
      await sender.replaceTrack(newTrack);
      
      // Update local tracking
      this.senders.set(trackType, sender);
      
      console.log(`Successfully replaced ${trackType} track`);
      return true;
      
    } catch (error) {
      console.error(`Failed to replace ${trackType} track:`, error);
      
      // Attempt rollback
      try {
        await sender.replaceTrack(currentTrack);
        console.log(`Rolled back ${trackType} track replacement`);
      } catch (rollbackError) {
        console.error(`Rollback failed for ${trackType}:`, rollbackError);
      }
      
      return false;
    }
  }

  async addTrackWithTransceiver(track, stream, trackType) {
    try {
      // Check if transceiver already exists
      let transceiver = this.transceivers.get(trackType);
      
      if (transceiver) {
        // Replace existing track
        await this.replaceTrackSafely(trackType, track);
      } else {
        // Add new transceiver
        transceiver = this.peerConnection.addTransceiver(track, {
          direction: 'sendrecv',
          streams: [stream]
        });
        
        this.transceivers.set(trackType, transceiver);
        this.senders.set(trackType, transceiver.sender);
      }
      
      return transceiver;
      
    } catch (error) {
      console.error(`Failed to add ${trackType} track:`, error);
      throw error;
    }
  }

  handleICEGatheringStateChange() {
    const state = this.peerConnection.iceGatheringState;
    console.log(`ICE gathering state: ${state}`);
    
    if (state === 'complete') {
      this.iceGatheringComplete = true;
    }
  }

  handleConnectionStateChange() {
    const state = this.peerConnection.connectionState;
    console.log(`Connection state: ${state}`);
    
    switch (state) {
      case 'connected':
        this.dispatch({ type: 'CALL_CONNECTED' });
        break;
      case 'disconnected':
        this.handleDisconnection();
        break;
      case 'failed':
        this.handleConnectionFailure();
        break;
    }
  }

  async handleDisconnection() {
    // Attempt ICE restart
    try {
      const offer = await this.peerConnection.createOffer({ iceRestart: true });
      await this.peerConnection.setLocalDescription(offer);
      
      this.signalingService.emitWebRTCOffer({
        call_id: this.currentCallId,
        offer: offer,
        iceRestart: true
      });
      
    } catch (error) {
      console.error('ICE restart failed:', error);
      this.handleConnectionFailure();
    }
  }

  handleConnectionFailure() {
    this.dispatch({ 
      type: 'CALL_CONNECTION_FAILED',
      payload: 'Connection lost - attempting to reconnect'
    });
    
    // Trigger call recovery or termination
    setTimeout(() => {
      if (this.peerConnection.connectionState === 'failed') {
        this.endCall('Connection failed');
      }
    }, 5000);
  }

  closePeerConnection() {
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    
    // Clear tracking maps
    this.senders.clear();
    this.transceivers.clear();
    this.negotiationInProgress = false;
    this.pendingNegotiation = false;
    this.iceGatheringComplete = false;
  }
}
```

### 11. **Performance Considerations**

#### Optimization Strategies
1. **Memory Management**
   - Proper stream cleanup
   - Component unmounting cleanup
   - Event listener removal
   - PeerConnection resource cleanup

2. **Network Optimization**
   - Adaptive bitrate
   - Quality adjustment based on connection
   - Efficient ICE candidate handling
   - Connection state monitoring

3. **UI Performance**
   - Minimize re-renders during calls
   - Efficient video element handling
   - Debounced control updates
   - Optimized reconnection UI feedback

### 12. **Testing Strategy**

#### Unit Tests
- WebRTC service methods
- Redux actions and reducers
- Component rendering and interactions
- Reconnection manager logic
- Idempotent call operations
- ICE candidate queuing
- PeerConnection lifecycle management

#### Integration Tests
- Socket.IO event handling
- API integration
- Media stream management
- Network state transitions
- Exponential backoff behavior
- Track replacement scenarios

#### Resilience Tests
- Network disconnection/reconnection scenarios
- Server restart simulation
- Duplicate call initiation handling
- ICE candidate race conditions
- PeerConnection failure recovery
- Negotiation collision handling

#### E2E Tests
- Complete call flow
- Cross-browser compatibility
- Network condition simulation
- Call recovery after network issues
- Concurrent call handling
- Screen sharing track replacement

### 13. **Security Considerations**

#### Implementation Requirements
1. **Authentication**
   - JWT token validation for calls
   - User permission verification
   - Rate limiting for call attempts

2. **Media Security**
   - HTTPS requirement for getUserMedia
   - Secure WebRTC configuration
   - Media stream encryption

3. **Data Protection**
   - No sensitive data in logs
   - Secure signaling channel
   - Proper session cleanup

## Specific Implementation Steps

Based on the working reference implementation in `calling-test.html`, here are the specific steps to implement calling functionality in the React frontend, following RTK Query patterns and the exact WebRTC flow that works in the test file.

### Step 1: Add Call Action Buttons to ChatHeader

#### 1.1 Update ChatHeader Component
```jsx
// src/components/chat/ChatHeader.jsx
import { useDispatch, useSelector } from 'react-redux';
import { Phone, Video } from 'lucide-react';
import { initiateCall } from '../store/slices/callingSlice';

const ChatHeader = ({ conversation }) => {
  const dispatch = useDispatch();
  const { isConnected } = useSelector(state => state.calling);
  const { user } = useSelector(state => state.auth);

  const handleAudioCall = () => {
    dispatch(initiateCall({
      conversationId: conversation.id,
      callType: 'audio'
    }));
  };

  const handleVideoCall = () => {
    dispatch(initiateCall({
      conversationId: conversation.id,
      callType: 'video'
    }));
  };

  return (
    <div className="chat-header">
      {/* Existing header content */}
      <div className="call-actions">
        <button
          onClick={handleAudioCall}
          disabled={!isConnected}
          className="call-btn audio-call"
          title="Start audio call"
        >
          <Phone size={20} />
        </button>
        <button
          onClick={handleVideoCall}
          disabled={!isConnected}
          className="call-btn video-call"
          title="Start video call"
        >
          <Video size={20} />
        </button>
      </div>
    </div>
  );
};
```

### Step 2: Implement Call Initiation Flow

#### 2.1 Create RTK Query API Endpoints
```javascript
// src/store/api/callingApi.js
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export const callingApi = createApi({
  reducerPath: 'callingApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/calling/',
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Call'],
  endpoints: (builder) => ({
    getCallDetail: builder.query({
      query: (callId) => `${callId}/`,
      providesTags: (result, error, callId) => [{ type: 'Call', id: callId }],
    }),
    getCallHistory: builder.query({
      query: (conversationId) => `history/?conversation_id=${conversationId}`,
      providesTags: ['Call'],
    }),
    reportCallQuality: builder.mutation({
      query: ({ callId, qualityData }) => ({
        url: `${callId}/quality/`,
        method: 'POST',
        body: qualityData,
      }),
    }),
    updateCallStatus: builder.mutation({
      query: ({ callId, status }) => ({
        url: `${callId}/`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { callId }) => [{ type: 'Call', id: callId }],
    }),
  }),
});

export const {
  useGetCallDetailQuery,
  useGetCallHistoryQuery,
  useReportCallQualityMutation,
  useUpdateCallStatusMutation,
} = callingApi;
```

#### 2.2 Implement Call Initiation Redux Action
```javascript
// src/store/slices/callingSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { WebRTCService } from '../../services/webrtc/WebRTCService';
import { MediaService } from '../../services/webrtc/MediaService';

// Async thunk for initiating calls (following HTML test pattern)
export const initiateCall = createAsyncThunk(
  'calling/initiateCall',
  async ({ conversationId, callType }, { getState, dispatch, rejectWithValue }) => {
    try {
      const { socket } = getState().socket;

      if (!socket?.connected) {
        throw new Error('Not connected to socket server');
      }

      // Step 1: Get user media first (following HTML test pattern)
      const mediaService = new MediaService();
      const constraints = {
        audio: true,
        video: callType === 'video'
      };

      const localStream = await mediaService.getUserMedia(constraints);
      dispatch(setLocalStream(localStream));

      // Step 2: Setup WebRTC peer connection
      const webrtcService = new WebRTCService();
      const peerConnection = await webrtcService.initializePeerConnection();

      // Step 3: Add local stream tracks to peer connection
      localStream.getTracks().forEach(track => {
        peerConnection.addTrack(track, localStream);
      });

      // Step 4: Emit socket event (exact pattern from HTML test)
      socket.emit('initiate_call', {
        conversationId,
        callType
      });

      return {
        conversationId,
        callType,
        localStream,
        peerConnection
      };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);
```

### Step 3: Implement Incoming Call Handling

#### 3.1 Socket Event Listener Setup
```javascript
// src/services/socket/callingSocketService.js
export const setupCallingSocketEvents = (socket, dispatch) => {
  // Incoming call handler (following HTML test pattern)
  socket.on('incoming_call', async (data) => {
    console.log(`📞 Incoming call from ${data.caller.username}`);

    dispatch(receiveIncomingCall({
      callId: data.callId,
      caller: data.caller,
      callType: data.callType,
      conversationId: data.conversationId
    }));

    // Prepare media for incoming call (following HTML test pattern)
    try {
      const mediaService = new MediaService();
      const constraints = {
        audio: true,
        video: data.callType === 'video'
      };
      const localStream = await mediaService.getUserMedia(constraints);
      dispatch(setLocalStream(localStream));
    } catch (error) {
      console.error('Could not prepare media for incoming call:', error);
    }

    // Show incoming call modal
    dispatch(showIncomingCallModal(data));
  });

  // Call status events (matching HTML test patterns)
  socket.on('call_initiated', (data) => {
    dispatch(updateCallStatus({ callId: data.callId, status: 'initiated' }));
  });

  socket.on('call_answered', (data) => {
    dispatch(updateCallStatus({ callId: data.callId, status: 'answered' }));
    // Automatically create WebRTC offer when call is answered (HTML test pattern)
    dispatch(createWebRTCOffer(data.callId));
  });

  socket.on('call_declined', (data) => {
    dispatch(resetCallState());
  });

  socket.on('call_ended', (data) => {
    dispatch(resetCallState());
  });
};
```

#### 3.2 Incoming Call Modal Component
```jsx
// src/components/calling/IncomingCallModal.jsx
import { useDispatch, useSelector } from 'react-redux';
import { answerCall, declineCall } from '../../store/slices/callingSlice';

const IncomingCallModal = () => {
  const dispatch = useDispatch();
  const { incomingCallData, showIncomingCall } = useSelector(state => state.calling);

  const handleAnswer = () => {
    dispatch(answerCall(incomingCallData.callId));
  };

  const handleDecline = () => {
    dispatch(declineCall(incomingCallData.callId));
  };

  if (!showIncomingCall || !incomingCallData) return null;

  return (
    <div className="incoming-call-modal">
      <div className="call-notification">
        <div className="caller-info">
          <img
            src={incomingCallData.caller.avatar}
            alt={incomingCallData.caller.username}
            className="caller-avatar"
          />
          <h3>{incomingCallData.caller.firstName} {incomingCallData.caller.lastName}</h3>
          <p>Incoming {incomingCallData.callType} call...</p>
        </div>
        <div className="call-actions">
          <button
            onClick={handleDecline}
            className="btn-decline"
          >
            Decline
          </button>
          <button
            onClick={handleAnswer}
            className="btn-answer"
          >
            Answer
          </button>
        </div>
      </div>
    </div>
  );
};
```

### Step 4: Implement Call Connection (WebRTC Flow)

#### 4.1 Answer Call Implementation
```javascript
// src/store/slices/callingSlice.js (continued)
export const answerCall = createAsyncThunk(
  'calling/answerCall',
  async (callId, { getState, dispatch, rejectWithValue }) => {
    try {
      const { socket } = getState().socket;
      const { localStream } = getState().calling;

      // Step 1: Ensure we have local stream (HTML test pattern)
      let stream = localStream;
      if (!stream) {
        const mediaService = new MediaService();
        const constraints = { audio: true, video: true }; // Adjust based on call type
        stream = await mediaService.getUserMedia(constraints);
        dispatch(setLocalStream(stream));
      }

      // Step 2: Setup WebRTC peer connection
      const webrtcService = new WebRTCService();
      const peerConnection = await webrtcService.initializePeerConnection();

      // Step 3: Add local stream to peer connection
      stream.getTracks().forEach(track => {
        peerConnection.addTrack(track, stream);
      });

      // Step 4: Emit answer call event (exact HTML test pattern)
      socket.emit('answer_call', { callId });

      return { callId, peerConnection };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);
```

#### 4.2 WebRTC Signaling Implementation
```javascript
// src/services/socket/callingSocketService.js (continued)
export const setupWebRTCSignaling = (socket, dispatch) => {
  // WebRTC offer handling (exact HTML test pattern)
  socket.on('webrtc_offer', async (data) => {
    console.log(`📤 Received WebRTC offer for call ${data.callId}`);
    dispatch(handleWebRTCOffer(data));
  });

  // WebRTC answer handling (exact HTML test pattern)
  socket.on('webrtc_answer', async (data) => {
    console.log(`📥 Received WebRTC answer for call ${data.callId}`);
    dispatch(handleWebRTCAnswer(data));
  });

  // ICE candidate handling (exact HTML test pattern)
  socket.on('webrtc_ice_candidate', async (data) => {
    console.log(`🧊 Received ICE candidate for call ${data.callId}`);
    dispatch(handleICECandidate(data));
  });
};

// WebRTC offer handler (following HTML test logic exactly)
export const handleWebRTCOffer = createAsyncThunk(
  'calling/handleWebRTCOffer',
  async (data, { getState, dispatch }) => {
    const { peerConnection, localStream } = getState().calling;

    try {
      // Set remote description
      await peerConnection.setRemoteDescription(new RTCSessionDescription(data.offer));

      // Add local stream if not already added
      if (localStream) {
        const existingSenders = peerConnection.getSenders();
        localStream.getTracks().forEach(track => {
          const existingSender = existingSenders.find(sender => sender.track === track);
          if (!existingSender) {
            peerConnection.addTrack(track, localStream);
          }
        });
      }

      // Create and send answer
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // Send answer via socket (exact HTML test pattern)
      const { socket } = getState().socket;
      socket.emit('webrtc_answer', {
        callId: data.callId,
        answer: answer
      });

    } catch (error) {
      console.error('Failed to handle WebRTC offer:', error);
    }
  }
);
```

### Step 5: Implement Media Controls

#### 5.1 Media Control Component
```jsx
// src/components/calling/CallControls.jsx
import { useDispatch, useSelector } from 'react-redux';
import { toggleAudio, toggleVideo, endCall, startScreenShare } from '../../store/slices/callingSlice';

const CallControls = () => {
  const dispatch = useDispatch();
  const { controls, currentCall } = useSelector(state => state.calling);

  const handleToggleAudio = () => {
    dispatch(toggleAudio());
  };

  const handleToggleVideo = () => {
    dispatch(toggleVideo());
  };

  const handleEndCall = () => {
    dispatch(endCall());
  };

  const handleScreenShare = () => {
    dispatch(startScreenShare());
  };

  return (
    <div className="call-controls">
      <button
        onClick={handleToggleAudio}
        className={`control-btn ${controls.isAudioMuted ? 'muted' : ''}`}
        title={controls.isAudioMuted ? 'Unmute' : 'Mute'}
      >
        {controls.isAudioMuted ? '🔇' : '🎤'}
      </button>

      {currentCall.type === 'video' && (
        <button
          onClick={handleToggleVideo}
          className={`control-btn ${controls.isVideoMuted ? 'muted' : ''}`}
          title={controls.isVideoMuted ? 'Turn on camera' : 'Turn off camera'}
        >
          {controls.isVideoMuted ? '📹' : '📹'}
        </button>
      )}

      <button
        onClick={handleScreenShare}
        className={`control-btn ${controls.isScreenSharing ? 'active' : ''}`}
        title="Share screen"
      >
        🖥️
      </button>

      <button
        onClick={handleEndCall}
        className="control-btn end-call"
        title="End call"
      >
        📞
      </button>
    </div>
  );
};
```

#### 5.2 Media Control Redux Actions (Following HTML Test Patterns)
```javascript
// src/store/slices/callingSlice.js (continued)
export const toggleAudio = createAsyncThunk(
  'calling/toggleAudio',
  async (_, { getState, dispatch }) => {
    const { localStream, currentCall } = getState().calling;
    const { socket } = getState().socket;

    if (!localStream) {
      throw new Error('No local stream available');
    }

    const audioTracks = localStream.getAudioTracks();
    if (audioTracks.length === 0) {
      throw new Error('No audio tracks found');
    }

    // Toggle audio track (exact HTML test pattern)
    const audioTrack = audioTracks[0];
    const previousState = audioTrack.enabled;
    audioTrack.enabled = !audioTrack.enabled;
    const isAudioMuted = !audioTrack.enabled;

    // Emit socket event (exact HTML test pattern)
    if (socket && currentCall.id) {
      socket.emit('toggle_audio', {
        callId: currentCall.id,
        enabled: audioTrack.enabled
      });
    }

    return { isAudioMuted };
  }
);

export const toggleVideo = createAsyncThunk(
  'calling/toggleVideo',
  async (_, { getState, dispatch }) => {
    const { localStream, currentCall } = getState().calling;
    const { socket } = getState().socket;

    if (!localStream) {
      throw new Error('No local stream available');
    }

    const videoTracks = localStream.getVideoTracks();
    if (videoTracks.length === 0) {
      throw new Error('No video tracks found');
    }

    // Toggle video track (exact HTML test pattern)
    const videoTrack = videoTracks[0];
    videoTrack.enabled = !videoTrack.enabled;
    const isVideoMuted = !videoTrack.enabled;

    // Emit socket event (exact HTML test pattern)
    if (socket && currentCall.id) {
      socket.emit('toggle_video', {
        callId: currentCall.id,
        enabled: videoTrack.enabled
      });
    }

    return { isVideoMuted };
  }
);
```

### Step 6: Implement Call Termination

#### 6.1 End Call Implementation
```javascript
// src/store/slices/callingSlice.js (continued)
export const endCall = createAsyncThunk(
  'calling/endCall',
  async (_, { getState, dispatch }) => {
    const { currentCall, localStream, peerConnection } = getState().calling;
    const { socket } = getState().socket;

    if (!currentCall.id) {
      throw new Error('No active call to end');
    }

    // Step 1: Emit socket event (exact HTML test pattern)
    if (socket) {
      socket.emit('end_call', {
        callId: currentCall.id
      });
    }

    // Step 2: Stop all media tracks (exact HTML test pattern)
    if (localStream) {
      localStream.getTracks().forEach(track => {
        track.stop();
      });
    }

    // Step 3: Close peer connection (exact HTML test pattern)
    if (peerConnection) {
      peerConnection.close();
    }

    // Step 4: Reset all state
    dispatch(resetCallState());

    return { callId: currentCall.id };
  }
);

export const declineCall = createAsyncThunk(
  'calling/declineCall',
  async (callId, { getState, dispatch }) => {
    const { socket } = getState().socket;

    // Emit decline event (exact HTML test pattern)
    if (socket) {
      socket.emit('decline_call', { callId });
    }

    // Reset state
    dispatch(resetCallState());

    return { callId };
  }
);
```

#### 6.2 Call State Reset
```javascript
// src/store/slices/callingSlice.js (continued)
const callingSlice = createSlice({
  name: 'calling',
  initialState: {
    // ... initial state as defined in architecture
  },
  reducers: {
    resetCallState: (state) => {
      // Reset all call-related state (following HTML test cleanup pattern)
      state.currentCall = {
        id: null,
        type: null,
        status: 'idle',
        participants: [],
        startTime: null,
        duration: 0
      };
      state.localStream = null;
      state.remoteStream = null;
      state.showIncomingCall = false;
      state.showActiveCall = false;
      state.incomingCallData = null;
      state.controls = {
        isAudioMuted: false,
        isVideoMuted: false,
        isSpeakerOn: true,
        isScreenSharing: false
      };
    },
    // ... other reducers
  },
  extraReducers: (builder) => {
    // Handle async thunk results
    builder
      .addCase(initiateCall.fulfilled, (state, action) => {
        state.currentCall.status = 'initiating';
        state.localStream = action.payload.localStream;
        state.showActiveCall = true;
      })
      .addCase(answerCall.fulfilled, (state, action) => {
        state.currentCall.status = 'answered';
        state.showIncomingCall = false;
        state.showActiveCall = true;
      })
      // ... other cases
  }
});
```

## Implementation Timeline

### Step 7: WebRTC Service Implementation (Following HTML Test Patterns)

#### 7.1 WebRTC Service Class
```javascript
// src/services/webrtc/WebRTCService.js
export class WebRTCService {
  constructor() {
    this.peerConnection = null;
    this.localStream = null;
    this.remoteStream = null;

    // Exact configuration from HTML test
    this.rtcConfig = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    };
  }

  async initializePeerConnection() {
    if (this.peerConnection) {
      this.closePeerConnection();
    }

    this.peerConnection = new RTCPeerConnection(this.rtcConfig);
    this.setupPeerConnectionEventHandlers();
    return this.peerConnection;
  }

  setupPeerConnectionEventHandlers() {
    // Handle remote stream (exact HTML test pattern)
    this.peerConnection.ontrack = (event) => {
      console.log('🎥 Received remote stream');
      const stream = event.streams[0];

      // Dispatch to Redux store
      store.dispatch(setRemoteStream(stream));

      // Log track information (matching HTML test)
      const audioTracks = stream.getAudioTracks();
      const videoTracks = stream.getVideoTracks();
      console.log(`📊 Remote stream has ${audioTracks.length} audio tracks and ${videoTracks.length} video tracks`);
    };

    // Handle ICE candidates (exact HTML test pattern)
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        const { socket } = store.getState().socket;
        const { currentCall } = store.getState().calling;

        if (socket && currentCall.id) {
          console.log('🧊 Sending ICE candidate');
          socket.emit('webrtc_ice_candidate', {
            callId: currentCall.id,
            candidate: event.candidate
          });
        }
      } else {
        console.log('🧊 ICE gathering complete');
      }
    };

    // Handle connection state changes (exact HTML test pattern)
    this.peerConnection.onconnectionstatechange = () => {
      const state = this.peerConnection.connectionState;
      console.log(`🔗 Connection state: ${state}`);

      if (state === 'connected') {
        console.log('✅ WebRTC connection established successfully!');
        store.dispatch(updateCallStatus({ status: 'active' }));
        store.dispatch(startCallTimer());
      } else if (state === 'failed') {
        console.error('❌ WebRTC connection failed');
        store.dispatch(setCallingError('WebRTC connection failed'));
      } else if (state === 'disconnected') {
        console.log('⚠️ WebRTC connection disconnected');
      }
    };

    // Handle ICE connection state changes (HTML test pattern)
    this.peerConnection.oniceconnectionstatechange = () => {
      console.log(`🧊 ICE connection state: ${this.peerConnection.iceConnectionState}`);
    };
  }

  async createOffer() {
    if (!this.peerConnection) {
      throw new Error('No peer connection available');
    }

    // Create offer with exact constraints from HTML test
    const offer = await this.peerConnection.createOffer({
      offerToReceiveAudio: true,
      offerToReceiveVideo: true
    });

    await this.peerConnection.setLocalDescription(offer);
    return offer;
  }

  async createAnswer() {
    if (!this.peerConnection) {
      throw new Error('No peer connection available');
    }

    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);
    return answer;
  }

  async handleRemoteOffer(offer) {
    if (!this.peerConnection) {
      await this.initializePeerConnection();
    }

    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
  }

  async handleRemoteAnswer(answer) {
    if (!this.peerConnection) {
      throw new Error('No peer connection available');
    }

    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
  }

  async handleICECandidate(candidate) {
    if (!this.peerConnection) {
      throw new Error('No peer connection available');
    }

    await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
  }

  closePeerConnection() {
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
  }
}
```

#### 7.2 Media Service Class
```javascript
// src/services/webrtc/MediaService.js
export class MediaService {
  async getUserMedia(constraints) {
    try {
      console.log(`🎥 Requesting user media: ${JSON.stringify(constraints)}`);

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      // Debug track information (matching HTML test)
      const audioTracks = stream.getAudioTracks();
      const videoTracks = stream.getVideoTracks();
      console.log(`📊 Local stream has ${audioTracks.length} audio tracks and ${videoTracks.length} video tracks`);

      // Log track details (matching HTML test)
      audioTracks.forEach((track, index) => {
        console.log(`🎤 Audio track ${index}: enabled=${track.enabled}, muted=${track.muted}, readyState=${track.readyState}`);
      });

      return stream;
    } catch (error) {
      console.error(`❌ Failed to get user media: ${error.message}`);
      throw error;
    }
  }

  async getScreenShare() {
    try {
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      console.log('🖥️ Screen sharing stream obtained');
      return stream;
    } catch (error) {
      console.error(`❌ Failed to get screen share: ${error.message}`);
      throw error;
    }
  }

  stopAllTracks(stream) {
    if (stream) {
      stream.getTracks().forEach(track => {
        track.stop();
        console.log(`🛑 Stopped ${track.kind} track`);
      });
    }
  }

  getMediaConstraints(callType) {
    return {
      audio: true,
      video: callType === 'video'
    };
  }
}
```

### Step 8: Complete Integration with Existing Components

#### 8.1 Update Main App Component
```jsx
// src/App.jsx (add calling components)
import CallManager from './components/calling/CallManager';
import IncomingCallModal from './components/calling/IncomingCallModal';
import ActiveCallInterface from './components/calling/ActiveCallInterface';

function App() {
  return (
    <div className="App">
      {/* Existing app content */}

      {/* Calling components */}
      <CallManager />
      <IncomingCallModal />
      <ActiveCallInterface />
    </div>
  );
}
```

#### 8.2 Update Store Configuration
```javascript
// src/store/index.js
import { configureStore } from '@reduxjs/toolkit';
import { callingApi } from './api/callingApi';
import callingReducer from './slices/callingSlice';

export const store = configureStore({
  reducer: {
    // ... existing reducers
    calling: callingReducer,
    [callingApi.reducerPath]: callingApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['calling/setLocalStream', 'calling/setRemoteStream'],
        ignoredPaths: ['calling.localStream', 'calling.remoteStream', 'calling.peerConnection'],
      },
    }).concat(callingApi.middleware),
});
```

#### 8.3 Socket Integration Setup
```javascript
// src/services/socket/socketService.js
import { setupCallingSocketEvents, setupWebRTCSignaling } from './callingSocketService';

export const initializeSocket = (token, dispatch) => {
  const socket = io('http://localhost:7000', {
    auth: { token },
    transports: ['websocket', 'polling']
  });

  // Setup all socket events
  setupCallingSocketEvents(socket, dispatch);
  setupWebRTCSignaling(socket, dispatch);

  return socket;
};
```

### Step 9: Styling Implementation

#### 9.1 Call Controls Styling
```scss
// src/styles/calling.scss
.call-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 10px;

  .control-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #4facfe;
    color: white;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    }

    &.muted {
      background: #f44336;
    }

    &.active {
      background: #4caf50;
    }

    &.end-call {
      background: #f44336;
    }
  }
}

.incoming-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .call-notification {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);

    .caller-info {
      margin-bottom: 20px;

      .caller-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin-bottom: 15px;
      }

      h3 {
        margin: 10px 0;
        color: #333;
      }

      p {
        color: #666;
        margin: 5px 0;
      }
    }

    .call-actions {
      display: flex;
      gap: 15px;
      justify-content: center;

      .btn-decline {
        background: #f44336;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          background: #d32f2f;
          transform: translateY(-2px);
        }
      }

      .btn-answer {
        background: #4caf50;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          background: #388e3c;
          transform: translateY(-2px);
        }
      }
    }
  }
}

.chat-header .call-actions {
  display: flex;
  gap: 10px;

  .call-btn {
    padding: 8px;
    border: none;
    border-radius: 50%;
    background: #4facfe;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #2196f3;
      transform: scale(1.1);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    &.audio-call {
      background: #4caf50;
    }

    &.video-call {
      background: #2196f3;
    }
  }
}
```

### Phase 6.1: Core Infrastructure (Week 1-2)
- Set up Redux store structure with calling slice
- Implement WebRTC and Media service classes following HTML test patterns
- Create signaling service with exact socket event handling
- Basic component structure (CallManager, IncomingCallModal, CallControls)
- Network state management setup

### Phase 6.2: Call Action Buttons & UI Integration (Week 2)
- Add call buttons to ChatHeader component with proper styling
- Implement call button click handlers with RTK dispatch
- Add call button states (enabled/disabled based on connection)
- Integrate with existing chat interface design
- Add proper icons and tooltips for accessibility

### Phase 6.3: Call Flow Implementation (Week 3)
- Implement initiateCall async thunk following HTML test patterns
- Add answerCall and declineCall actions with proper socket events
- Create IncomingCallModal component with caller information display
- Implement call state management in Redux store
- Add proper error handling for media permissions

### Phase 6.4: WebRTC Connection & Media (Week 4)
- Implement WebRTC service class with exact HTML test configuration
- Add media service for getUserMedia and screen sharing
- Implement WebRTC signaling (offer/answer/ICE candidates) following HTML patterns
- Add ActiveCallInterface component with video containers
- Implement media controls (mute/unmute, video toggle, screen share)

### Phase 6.5: Call Termination & Cleanup (Week 5)
- Implement endCall action with proper cleanup following HTML test patterns
- Add call duration tracking and display
- Implement proper media stream cleanup (stop all tracks)
- Add WebRTC connection cleanup and state reset
- Handle call termination from both parties

### Phase 6.6: Integration & Testing (Week 6)
- Integrate all components with existing app structure
- Add RTK Query endpoints for call history and quality reporting
- Implement comprehensive error handling and user feedback
- Add responsive design for mobile devices
- Test complete call flow end-to-end

### Phase 6.7: Polish & Production (Week 7)
- Add call notifications and sound effects
- Implement accessibility features (keyboard navigation, screen readers)
- Add comprehensive logging for debugging
- Performance optimization and memory leak prevention
- Production deployment and monitoring setup

## Success Criteria

✅ **Core Calling Functionality**
- Call action buttons (audio/video) integrated in ChatHeader
- Initiate call flow works exactly like HTML test (getUserMedia → setupPeerConnection → socket emit)
- Incoming call handling displays proper modal with answer/decline options
- WebRTC connection establishment follows exact HTML test patterns
- Media controls (mute/unmute, video toggle, screen share) function identically to HTML test
- Call termination properly cleans up all resources (streams, connections, state)

✅ **RTK Integration Requirements**
- All API calls use RTK Query endpoints (getCallDetail, getCallHistory, reportCallQuality)
- Redux state management follows RTK patterns with proper async thunks
- Socket events properly dispatch Redux actions
- Media streams handled in Redux with proper serialization exclusions
- Error handling integrated with RTK error patterns

✅ **UI/UX Requirements**
- IncomingCallModal displays caller information and call type
- ActiveCallInterface shows local/remote video with proper aspect ratios
- Call controls match HTML test functionality with proper visual feedback
- Call duration tracking and display during active calls
- Responsive design works on mobile devices
- Proper loading states and error messages

✅ **WebRTC Technical Requirements**
- Exact STUN server configuration from HTML test (Google STUN servers)
- ICE candidate exchange works identically to HTML test
- Offer/answer exchange follows exact HTML test patterns
- Media track management (add/remove/replace) works like HTML test
- Connection state monitoring and error recovery
- Screen sharing track replacement functionality

✅ **Socket Communication Requirements**
- All socket events match HTML test exactly (initiate_call, answer_call, decline_call, end_call)
- WebRTC signaling events identical to HTML test (webrtc_offer, webrtc_answer, webrtc_ice_candidate)
- Media control events (toggle_audio, toggle_video) work like HTML test
- Proper authentication with JWT tokens
- Connection state management and reconnection

✅ **Code Quality Requirements**
- Components follow React best practices with proper hooks usage
- Redux actions and reducers follow RTK patterns
- WebRTC and Media services are properly abstracted
- Comprehensive error handling with user-friendly messages
- Proper cleanup in useEffect hooks and component unmounting
- TypeScript support (if applicable) with proper type definitions

This implementation replicates the exact functionality demonstrated in the working HTML test file while properly integrating with the React/Redux architecture and following modern frontend development best practices.