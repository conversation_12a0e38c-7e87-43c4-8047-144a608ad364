import { test, expect, Page, BrowserContext } from '@playwright/test';

test.describe('Comprehensive Chat Application E2E Tests', () => {
  const TEST_EMAIL = '<EMAIL>';
  const TEST_PASSWORD = 'testpass123';
  const BASE_URL = 'http://localhost:5000';

  let page: Page;
  let context: BrowserContext;

  test.beforeEach(async ({ page: testPage, context: testContext }) => {
    page = testPage;
    context = testContext;

    // Set up console logging for debugging
    page.on('console', (msg) => {
      console.log(`[BROWSER ${msg.type().toUpperCase()}] ${msg.text()}`);
    });

    // Set up error logging
    page.on('pageerror', (error) => {
      console.error(`[PAGE ERROR] ${error.message}`);
    });

    // Navigate to the application
    await page.goto(BASE_URL);
  });

  test('Complete User Workflow: Login → Dashboard → Conversation → Messaging', async () => {
    console.log('🚀 Starting comprehensive E2E test...');

    // Step 1: Login Process
    console.log('📝 Step 1: Testing login process...');
    
    // Wait for login page to load
    await page.waitForLoadState('networkidle');
    
    // Verify we're on the login page
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();

    // Fill in credentials
    await page.fill('[data-testid="email-input"]', TEST_EMAIL);
    await page.fill('[data-testid="password-input"]', TEST_PASSWORD);

    // Submit login form
    await page.click('[data-testid="login-button"]');

    // Wait for navigation to dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('✅ Login successful, navigated to dashboard');

    // Step 2: Dashboard Verification
    console.log('📝 Step 2: Verifying dashboard components...');
    
    // Verify dashboard header is present
    await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
    
    // Verify connection status
    await expect(page.locator('[data-testid="connection-status"]')).toBeVisible();
    
    // Verify conversation list sidebar
    await expect(page.locator('[data-testid="conversation-list"]')).toBeVisible();
    
    // Verify new chat button
    await expect(page.locator('[data-testid="new-chat-button"]')).toBeVisible();
    
    // Verify chat area
    await expect(page.locator('[data-testid="chat-area"]')).toBeVisible();

    console.log('✅ Dashboard components verified');

    // Step 3: Check for existing conversations or create new one
    console.log('📝 Step 3: Managing conversations...');
    
    // Wait for conversations to load
    await page.waitForTimeout(2000);
    
    // Check if there are existing conversations
    const existingConversations = await page.locator('[data-testid="conversation-item"]').count();
    console.log(`Found ${existingConversations} existing conversations`);

    let conversationId: string | null = null;

    if (existingConversations > 0) {
      // Click on the first existing conversation
      console.log('📱 Using existing conversation...');
      await page.locator('[data-testid="conversation-item"]').first().click();
      
      // Wait for conversation to load
      await page.waitForTimeout(1000);
      
      // Get conversation ID from URL or data attribute
      const url = page.url();
      const match = url.match(/conversation\/([^\/]+)/);
      if (match) {
        conversationId = match[1];
      }
    } else {
      // Create a new conversation
      console.log('📱 Creating new conversation...');
      await page.click('[data-testid="new-chat-button"]');
      
      // Wait for user search modal to appear
      await expect(page.locator('[data-testid="user-search-modal"]')).toBeVisible();
      
      // Search for a user (we'll search for any available user)
      const searchInput = page.locator('[data-testid="user-search-input"]');
      await expect(searchInput).toBeVisible();
      await searchInput.fill('test'); // Search for test users
      
      // Wait for search results
      await page.waitForTimeout(1000);
      
      // Click on the first search result
      const firstUser = page.locator('[data-testid="user-search-result"]').first();
      if (await firstUser.isVisible()) {
        await firstUser.click();
        
        // Wait for conversation to be created and loaded
        await page.waitForTimeout(2000);
        
        // Verify we're now in a conversation
        await expect(page.locator('[data-testid="message-input"]')).toBeVisible();
      } else {
        console.log('⚠️ No users found in search, skipping conversation creation');
        return;
      }
    }

    console.log('✅ Conversation ready for messaging');

    // Step 4: Message Sending and Receiving
    console.log('📝 Step 4: Testing message functionality...');
    
    // Verify message input and send button are present
    await expect(page.locator('[data-testid="message-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="send-button"]')).toBeVisible();

    // Get initial message count
    const initialMessageCount = await page.locator('[data-testid="message"]').count();
    console.log(`Initial message count: ${initialMessageCount}`);

    // Send a test message
    const testMessage = `E2E Test Message - ${new Date().toISOString()}`;
    await page.fill('[data-testid="message-input"]', testMessage);
    await page.click('[data-testid="send-button"]');

    console.log(`📤 Sent message: "${testMessage}"`);

    // Wait for message to appear in the UI
    await page.waitForTimeout(3000);

    // Verify message appears in the conversation
    const finalMessageCount = await page.locator('[data-testid="message"]').count();
    console.log(`Final message count: ${finalMessageCount}`);
    
    expect(finalMessageCount).toBeGreaterThan(initialMessageCount);

    // Verify our message content appears
    const messageElements = await page.locator('[data-testid="message"]').all();
    let messageFound = false;
    
    for (const messageEl of messageElements) {
      const messageText = await messageEl.textContent();
      if (messageText && messageText.includes(testMessage)) {
        messageFound = true;
        console.log('✅ Test message found in conversation');
        break;
      }
    }

    expect(messageFound).toBe(true);

    // Step 5: Real-time functionality test
    console.log('📝 Step 5: Testing real-time updates...');
    
    // Send another message to test real-time updates
    const realtimeMessage = `Real-time test - ${Date.now()}`;
    await page.fill('[data-testid="message-input"]', realtimeMessage);
    await page.click('[data-testid="send-button"]');

    // Wait for real-time update
    await page.waitForTimeout(2000);

    // Verify the new message appears
    const updatedMessageElements = await page.locator('[data-testid="message"]').all();
    let realtimeMessageFound = false;
    
    for (const messageEl of updatedMessageElements) {
      const messageText = await messageEl.textContent();
      if (messageText && messageText.includes(realtimeMessage)) {
        realtimeMessageFound = true;
        console.log('✅ Real-time message found in conversation');
        break;
      }
    }

    expect(realtimeMessageFound).toBe(true);

    console.log('🎉 Comprehensive E2E test completed successfully!');
  });

  test('Login Error Handling', async () => {
    console.log('🔐 Testing login error handling...');

    // Try to login with invalid credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');

    // Wait for error message
    await page.waitForTimeout(2000);

    // Verify error message appears
    const errorMessage = page.locator('[data-testid="error-message"]');
    await expect(errorMessage).toBeVisible();

    console.log('✅ Login error handling verified');
  });

  test('Dashboard Navigation and UI Elements', async () => {
    console.log('🏠 Testing dashboard navigation and UI elements...');

    // Login first
    await page.fill('[data-testid="email-input"]', TEST_EMAIL);
    await page.fill('[data-testid="password-input"]', TEST_PASSWORD);
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/dashboard', { timeout: 10000 });

    // Test logout functionality
    await page.click('[data-testid="logout-button"]');
    await page.waitForURL('**/login', { timeout: 5000 });

    // Verify we're back on login page
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();

    console.log('✅ Dashboard navigation and logout verified');
  });

  test('Connection Status Monitoring', async () => {
    console.log('🔌 Testing connection status monitoring...');

    // Login first
    await page.fill('[data-testid="email-input"]', TEST_EMAIL);
    await page.fill('[data-testid="password-input"]', TEST_PASSWORD);
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/dashboard', { timeout: 10000 });

    // Wait for socket connection
    await page.waitForTimeout(3000);

    // Check connection status
    const connectionStatus = page.locator('[data-testid="connection-status"]');
    await expect(connectionStatus).toBeVisible();

    // The status should show "Connected" or "Connecting"
    const statusText = await connectionStatus.textContent();
    expect(statusText).toMatch(/(Connected|Connecting)/);

    console.log(`✅ Connection status: ${statusText}`);
  });
});
