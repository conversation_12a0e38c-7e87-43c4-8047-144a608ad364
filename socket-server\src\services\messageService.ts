// socket-server/src/services/messageService.ts
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { MessageCreateSchema, MessageCreate } from '../schemas';
import {
  SendMessage,
  isEncryptedMessage,
  isPlaintextMessage
} from '../schemas/encryptionSchemas';

export class MessageService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async createMessage(data: MessageCreate | SendMessage, senderId: string) {
    try {
      // Check if this is an encrypted message
      const isEncrypted = isEncryptedMessage(data);

      // Validate input based on message type
      let validatedData: any;
      if (isEncrypted) {
        // For encrypted messages, we don't use the old schema
        validatedData = data as SendMessage;
      } else {
        // For plaintext messages, use the original validation
        validatedData = MessageCreateSchema.parse(data);
      }

      // Verify user has access to conversation
      const participant = await this.verifyConversationAccess(
        senderId,
        validatedData.conversationId
      );

      if (!participant) {
        throw new Error('Access denied to conversation');
      }

      // Prepare message data based on encryption status
      const messageData: any = {
        conversationId: validatedData.conversationId,
        senderId: senderId,
        messageType: validatedData.messageType || 'TEXT',
        has_media: false, // Default to false, will be updated when media is attached
        media_count: 0,   // Default to 0, will be updated when media is attached
      };

      if (isEncrypted) {
        // For encrypted messages, store encrypted fields
        messageData.content = ''; // Empty for encrypted messages
        messageData.encryptedContent = validatedData.encryptedContent;
        messageData.iv = validatedData.iv;
        messageData.senderRatchetKey = validatedData.senderRatchetKey;
        messageData.messageNumber = validatedData.messageNumber;
        messageData.previousChainLength = validatedData.previousChainLength;

        console.log(`Creating encrypted message for conversation ${validatedData.conversationId}`);
      } else {
        // For plaintext messages, store content normally
        messageData.content = validatedData.content;
        messageData.encryptedContent = '';
        messageData.iv = '';
        messageData.senderRatchetKey = '';
        messageData.messageNumber = 0;
        messageData.previousChainLength = 0;

        console.log(`Creating plaintext message for conversation ${validatedData.conversationId}`);
      }

      // Add createdAt timestamp
      messageData.createdAt = new Date();

      // Create message in database
      const message = await this.prisma.message.create({
        data: messageData,
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              profile_picture: true
            }
          }
        }
      });

      // Update conversation timestamp
      await this.prisma.conversation.update({
        where: { id: validatedData.conversationId },
        data: { updatedAt: new Date() }
      });

      return message;
    } catch (error) {
      console.error('Error creating message:', error);
      throw error;
    }
  }

  async getConversationMessages(conversationId: string, userId: string, page: number = 1, limit: number = 50) {
    try {
      // Verify user has access to conversation
      const hasAccess = await this.verifyConversationAccess(userId, conversationId);
      if (!hasAccess) {
        throw new Error('Access denied to conversation');
      }

      const skip = (page - 1) * limit;

      const messages = await this.prisma.message.findMany({
        where: {
          conversationId: conversationId
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              profile_picture: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      });

      return messages.reverse(); // Return in chronological order
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  }

  async verifyConversationAccess(userId: string, conversationId: string) {
    try {
      const participant = await this.prisma.conversationParticipant.findFirst({
        where: {
          conversationId,
          userId
        }
      });
      return participant;
    } catch (error) {
      console.error('Error verifying conversation access:', error);
      return null;
    }
  }

  async updateUserStatus(userId: string, isOnline: boolean) {
    try {
      await this.prisma.users.update({
        where: { id: userId },
        data: { last_seen: new Date() }
      });
      return true;
    } catch (error) {
      console.error('Error updating user status:', error);
      return false;
    }
  }

  async getMessageById(messageId: string) {
    try {
      const message = await this.prisma.message.findUnique({
        where: { id: messageId },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              profile_picture: true
            }
          }
        }
      });

      return message;
    } catch (error) {
      console.error('Error getting message by ID:', error);
      throw error;
    }
  }
}
