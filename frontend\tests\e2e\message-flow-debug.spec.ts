import { test, expect, Page } from '@playwright/test';

test.describe('Message Flow Debug Tests', () => {
  let page: Page;
  let consoleMessages: string[] = [];
  let socketMessages: any[] = [];

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    consoleMessages = [];
    socketMessages = [];

    // Capture console messages for debugging
    page.on('console', (msg) => {
      const text = msg.text();
      consoleMessages.push(`[${msg.type().toUpperCase()}] ${text}`);
      console.log(`[BROWSER ${msg.type().toUpperCase()}] ${text}`);
    });

    // Add debugging script to capture socket messages and Redux state
    await page.addInitScript(() => {
      // Capture socket messages
      const originalWebSocket = window.WebSocket;
      window.WebSocket = class extends originalWebSocket {
        constructor(url: string | URL, protocols?: string | string[]) {
          super(url, protocols);
          
          const originalSend = this.send;
          this.send = function(data: string | ArrayBufferLike | Blob | ArrayBufferView) {
            if (typeof data === 'string') {
              try {
                // Parse socket.io message format
                const match = data.match(/\["([^"]+)",(.+)\]/);
                if (match) {
                  const [, event, payload] = match;
                  const parsedPayload = JSON.parse(payload);
                  console.log(`🔴 [SOCKET OUT] ${event}:`, parsedPayload);
                  (window as any).capturedOutgoing = (window as any).capturedOutgoing || [];
                  (window as any).capturedOutgoing.push({ event, payload: parsedPayload });
                }
              } catch (e) {
                console.log('🔴 [SOCKET OUT] Raw:', data);
              }
            }
            return originalSend.call(this, data);
          };

          this.addEventListener('message', (event) => {
            try {
              const data = event.data;
              if (typeof data === 'string') {
                const match = data.match(/\["([^"]+)",(.+)\]/);
                if (match) {
                  const [, eventName, payload] = match;
                  const parsedPayload = JSON.parse(payload);
                  console.log(`🔵 [SOCKET IN] ${eventName}:`, parsedPayload);
                  (window as any).capturedIncoming = (window as any).capturedIncoming || [];
                  (window as any).capturedIncoming.push({ event: eventName, payload: parsedPayload });
                }
              }
            } catch (e) {
              console.log('🔵 [SOCKET IN] Raw:', event.data);
            }
          });
        }
      };

      // Monitor Redux state changes
      let lastMessageCount = 0;
      setInterval(() => {
        if (window.store) {
          const state = window.store.getState();
          const messages = state.messages?.messages || {};
          const totalMessages = Object.values(messages).reduce((sum: number, msgs: any) => sum + (msgs?.length || 0), 0);
          
          if (totalMessages !== lastMessageCount) {
            console.log(`🟡 [REDUX] Message count changed: ${lastMessageCount} → ${totalMessages}`);
            console.log('🟡 [REDUX] Current messages:', messages);
            lastMessageCount = totalMessages;
          }
        }
      }, 500);
    });

    await page.goto('http://localhost:5001');
  });

  test('should debug message duplication and encryption display', async () => {
    // Wait for app to load
    await page.waitForTimeout(2000);

    // Check if we're on login page or already logged in
    const loginButton = await page.locator('[data-testid="login-button"]').first();
    const isLoginVisible = await loginButton.isVisible().catch(() => false);

    if (isLoginVisible) {
      console.log('🔑 [TEST] Logging in...');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('[data-testid="login-button"]');
      await page.waitForTimeout(3000);
    }

    // Navigate to or create a conversation
    const newChatButton = await page.locator('[data-testid="new-chat-button"]').first();
    const isNewChatVisible = await newChatButton.isVisible().catch(() => false);

    if (isNewChatVisible) {
      console.log('💬 [TEST] Creating new conversation...');
      await page.click('[data-testid="new-chat-button"]');
      await page.waitForTimeout(2000);
    }

    // Find message input
    const messageInput = await page.locator('[data-testid="message-input"]').first();
    const sendButton = await page.locator('[data-testid="send-button"]').first();

    await expect(messageInput).toBeVisible();
    await expect(sendButton).toBeVisible();

    console.log('📝 [TEST] Sending test message...');

    // Get initial message count
    const initialMessages = await page.locator('[data-testid="message"]').count();
    console.log(`📊 [TEST] Initial message count: ${initialMessages}`);

    // Send a test message
    const testMessage = 'Test message for debugging - ' + Date.now();
    await messageInput.fill(testMessage);
    await sendButton.click();

    console.log(`📤 [TEST] Sent message: "${testMessage}"`);

    // Wait for message to appear and server response
    await page.waitForTimeout(5000);

    // Check final message count
    const finalMessages = await page.locator('[data-testid="message"]').count();
    console.log(`📊 [TEST] Final message count: ${finalMessages}`);

    // Get all message contents
    const messageElements = await page.locator('[data-testid="message"]').all();
    const messageContents = await Promise.all(
      messageElements.map(async (el) => {
        const text = await el.textContent();
        return text?.trim() || '';
      })
    );

    console.log('📋 [TEST] All message contents:', messageContents);

    // Check for duplicates
    const duplicateMessages = messageContents.filter((content, index) => 
      messageContents.indexOf(content) !== index
    );

    if (duplicateMessages.length > 0) {
      console.error('❌ [TEST] DUPLICATE MESSAGES FOUND:', duplicateMessages);
    } else {
      console.log('✅ [TEST] No duplicate messages found');
    }

    // Check if any message shows encrypted content
    const encryptedMessages = messageContents.filter(content => 
      content.includes('Encrypted') || 
      content.match(/^[A-Za-z0-9+/]+=*$/) || // Base64 pattern
      content.includes('[Encrypted Message]')
    );

    if (encryptedMessages.length > 0) {
      console.error('❌ [TEST] ENCRYPTED CONTENT DISPLAYED:', encryptedMessages);
    } else {
      console.log('✅ [TEST] All messages show decrypted content');
    }

    // Check if our test message appears correctly
    const ourMessage = messageContents.find(content => content.includes(testMessage));
    if (ourMessage) {
      console.log('✅ [TEST] Our message found in UI:', ourMessage);
    } else {
      console.error('❌ [TEST] Our message not found in UI');
    }

    // Get captured socket messages
    const outgoingMessages = await page.evaluate(() => (window as any).capturedOutgoing || []);
    const incomingMessages = await page.evaluate(() => (window as any).capturedIncoming || []);

    console.log('📤 [TEST] Captured outgoing messages:', outgoingMessages);
    console.log('📥 [TEST] Captured incoming messages:', incomingMessages);

    // Analyze the message flow
    const sendMessageEvents = outgoingMessages.filter((msg: any) => msg.event === 'send_message');
    const newMessageEvents = incomingMessages.filter((msg: any) => msg.event === 'new_message');

    console.log(`📊 [TEST] Send events: ${sendMessageEvents.length}, New message events: ${newMessageEvents.length}`);

    // Check if messages are encrypted
    sendMessageEvents.forEach((msg: any, index: number) => {
      const hasEncryption = msg.payload.encryptedContent && msg.payload.iv && msg.payload.senderRatchetKey;
      console.log(`📤 [TEST] Outgoing message ${index + 1} encrypted:`, hasEncryption);
      if (hasEncryption) {
        console.log(`📤 [TEST] Encryption details:`, {
          encryptedContentLength: msg.payload.encryptedContent.length,
          ivLength: msg.payload.iv.length,
          senderRatchetKeyLength: msg.payload.senderRatchetKey.length
        });
      }
    });

    newMessageEvents.forEach((msg: any, index: number) => {
      const isEncrypted = msg.payload.isEncrypted;
      console.log(`📥 [TEST] Incoming message ${index + 1} encrypted:`, isEncrypted);
      if (isEncrypted) {
        console.log(`📥 [TEST] Has encrypted content:`, !!msg.payload.encryptedContent);
      }
    });

    // Print console messages for debugging
    console.log('\n📋 [TEST] Browser Console Messages:');
    consoleMessages.forEach(msg => console.log(msg));

    // Test assertions
    expect(finalMessages).toBeGreaterThan(initialMessages);
    
    // For now, we'll log the issues rather than fail the test
    // This helps us understand what's happening
    if (duplicateMessages.length > 0) {
      console.log('⚠️ [TEST] Duplicate messages detected - needs fixing');
    }
    
    if (encryptedMessages.length > 0) {
      console.log('⚠️ [TEST] Encrypted content displayed - needs fixing');
    }
  });

  test('should test Redux state during message flow', async () => {
    await page.waitForTimeout(2000);

    // Login if needed
    const loginButton = await page.locator('[data-testid="login-button"]').first();
    const isLoginVisible = await loginButton.isVisible().catch(() => false);

    if (isLoginVisible) {
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      await page.click('[data-testid="login-button"]');
      await page.waitForTimeout(3000);
    }

    // Create conversation if needed
    const newChatButton = await page.locator('[data-testid="new-chat-button"]').first();
    const isNewChatVisible = await newChatButton.isVisible().catch(() => false);

    if (isNewChatVisible) {
      await page.click('[data-testid="new-chat-button"]');
      await page.waitForTimeout(2000);
    }

    // Monitor Redux state before sending message
    const initialState = await page.evaluate(() => {
      if (window.store) {
        const state = window.store.getState();
        return {
          messageCount: Object.values(state.messages?.messages || {}).reduce((sum: number, msgs: any) => sum + (msgs?.length || 0), 0),
          sendingMessages: state.messages?.sendingMessages || {},
          optimisticMessageMap: state.messages?.optimisticMessageMap || {}
        };
      }
      return null;
    });

    console.log('🟡 [REDUX TEST] Initial state:', initialState);

    // Send message
    const messageInput = await page.locator('[data-testid="message-input"]').first();
    const sendButton = await page.locator('[data-testid="send-button"]').first();

    const testMessage = 'Redux state test - ' + Date.now();
    await messageInput.fill(testMessage);
    await sendButton.click();

    // Wait and check state changes
    await page.waitForTimeout(1000);

    const afterSendState = await page.evaluate(() => {
      if (window.store) {
        const state = window.store.getState();
        return {
          messageCount: Object.values(state.messages?.messages || {}).reduce((sum: number, msgs: any) => sum + (msgs?.length || 0), 0),
          sendingMessages: state.messages?.sendingMessages || {},
          optimisticMessageMap: state.messages?.optimisticMessageMap || {},
          messages: state.messages?.messages || {}
        };
      }
      return null;
    });

    console.log('🟡 [REDUX TEST] After send state:', afterSendState);

    // Wait for server response
    await page.waitForTimeout(4000);

    const finalState = await page.evaluate(() => {
      if (window.store) {
        const state = window.store.getState();
        return {
          messageCount: Object.values(state.messages?.messages || {}).reduce((sum: number, msgs: any) => sum + (msgs?.length || 0), 0),
          sendingMessages: state.messages?.sendingMessages || {},
          optimisticMessageMap: state.messages?.optimisticMessageMap || {},
          messages: state.messages?.messages || {}
        };
      }
      return null;
    });

    console.log('🟡 [REDUX TEST] Final state:', finalState);

    // Analyze state changes
    if (initialState && afterSendState && finalState) {
      console.log('📊 [REDUX TEST] Message count progression:', 
        `${initialState.messageCount} → ${afterSendState.messageCount} → ${finalState.messageCount}`);
      
      if (finalState.messageCount > afterSendState.messageCount) {
        console.log('⚠️ [REDUX TEST] Message count increased after server response - possible duplicate');
      }
    }
  });
});
