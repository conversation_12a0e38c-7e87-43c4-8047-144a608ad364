# backend/authentication/views.py
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password
from pydantic import ValidationError
from users.models import User
from users.schemas import UserCreate, UserLogin, UserResponse, AuthResponse, TokenResponse

@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    try:
        # Validate input with Pydantic
        user_data = UserCreate(**request.data)

        # Check if user already exists
        if User.objects.filter(email=user_data.email).exists():
            return Response({
                'success': False,
                'error': 'User with this email already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create user using the custom manager
        user = User.objects.create_user(
            email=user_data.email,
            name=user_data.name,
            password=user_data.password
        )

        # Generate tokens
        refresh = RefreshToken.for_user(user)

        # Prepare response using Pydantic schemas
        user_response = UserResponse.model_validate(user)
        tokens = TokenResponse(
            access=str(refresh.access_token),
            refresh=str(refresh)
        )
        auth_response = AuthResponse(user=user_response, tokens=tokens)

        return Response({
            'success': True,
            'data': auth_response.model_dump(by_alias=True)
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    try:
        # Validate input with Pydantic
        login_data = UserLogin(**request.data)

        # Authenticate user
        user = authenticate(email=login_data.email, password=login_data.password)

        if user:
            # Generate tokens
            refresh = RefreshToken.for_user(user)

            # Prepare response using Pydantic schemas
            user_response = UserResponse.model_validate(user)
            tokens = TokenResponse(
                access=str(refresh.access_token),
                refresh=str(refresh)
            )
            auth_response = AuthResponse(user=user_response, tokens=tokens)

            return Response({
                'success': True,
                'data': auth_response.model_dump(by_alias=True)
            }, status=status.HTTP_200_OK)

        return Response({
            'success': False,
            'error': 'Invalid credentials'
        }, status=status.HTTP_401_UNAUTHORIZED)

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def refresh_token(request):
    try:
        refresh_token = request.data.get('refresh')
        if not refresh_token:
            return Response({
                'success': False,
                'error': 'Refresh token is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate and refresh the token
        refresh = RefreshToken(refresh_token)
        access_token = str(refresh.access_token)

        return Response({
            'access': access_token
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': 'Invalid refresh token'
        }, status=status.HTTP_401_UNAUTHORIZED)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def profile(request):
    try:
        # Return current user profile
        user_response = UserResponse.model_validate(request.user)
        return Response({
            'success': True,
            'data': user_response.model_dump(by_alias=True)
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
