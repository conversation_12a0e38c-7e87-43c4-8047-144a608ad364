#!/usr/bin/env python3
"""
Comprehensive API Testing Script for Chat Application
Tests all endpoints extracted from Postman collection with enhanced error handling and reporting
"""
import requests
import json
import uuid
import time
import base64
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

@dataclass
class TestResult:
    """Data class to store test results"""
    name: str
    status: str  # PASS, FAIL, SKIP, ERROR
    details: str = ""
    response_code: Optional[int] = None
    response_time: Optional[float] = None
    error_message: str = ""

@dataclass
class TestSuite:
    """Data class to store test suite results"""
    name: str
    results: List[TestResult] = field(default_factory=list)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    @property
    def passed(self) -> int:
        return sum(1 for r in self.results if r.status == "PASS")
    
    @property
    def failed(self) -> int:
        return sum(1 for r in self.results if r.status == "FAIL")
    
    @property
    def skipped(self) -> int:
        return sum(1 for r in self.results if r.status == "SKIP")
    
    @property
    def errors(self) -> int:
        return sum(1 for r in self.results if r.status == "ERROR")
    
    @property
    def total(self) -> int:
        return len(self.results)
    
    @property
    def success_rate(self) -> float:
        return (self.passed / self.total * 100) if self.total > 0 else 0

class ComprehensiveAPITester:
    """Enhanced API testing framework with comprehensive endpoint coverage"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url.rstrip('/')
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.user_id: Optional[str] = None
        self.target_user_id: Optional[str] = None
        self.conversation_id: Optional[str] = None
        self.group_id: Optional[str] = None
        self.call_id: Optional[str] = None
        self.media_id: Optional[str] = None
        self.message_id: Optional[str] = None
        
        # Test suites storage
        self.test_suites: List[TestSuite] = []
        self.current_suite: Optional[TestSuite] = None
        
        # Test data
        self.test_email = f"testuser_{uuid.uuid4().hex[:8]}@example.com"
        self.test_password = "TestPass123!"
        self.test_name = "Comprehensive Test User"
        
    def start_suite(self, suite_name: str) -> None:
        """Start a new test suite"""
        self.current_suite = TestSuite(name=suite_name, start_time=datetime.now())
        
    def end_suite(self) -> None:
        """End current test suite"""
        if self.current_suite:
            self.current_suite.end_time = datetime.now()
            self.test_suites.append(self.current_suite)
            self.current_suite = None
    
    def log_test(self, test_name: str, status: str, details: str = "", 
                 response_code: Optional[int] = None, response_time: Optional[float] = None,
                 error_message: str = "") -> None:
        """Log test result with enhanced information"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status_icons = {"PASS": "✅", "FAIL": "❌", "SKIP": "⚠️", "ERROR": "🔥"}
        icon = status_icons.get(status, "❓")
        
        print(f"[{timestamp}] {icon} {test_name}: {status}")
        if details:
            print(f"    {details}")
        if response_code:
            print(f"    HTTP {response_code} ({response_time:.3f}s)" if response_time else f"    HTTP {response_code}")
        if error_message:
            print(f"    Error: {error_message}")
            
        # Store result
        if self.current_suite:
            result = TestResult(
                name=test_name,
                status=status,
                details=details,
                response_code=response_code,
                response_time=response_time,
                error_message=error_message
            )
            self.current_suite.results.append(result)
    
    def make_request(self, method: str, endpoint: str, data: Any = None, 
                    headers: Optional[Dict[str, str]] = None, files: Optional[Dict] = None,
                    params: Optional[Dict[str, str]] = None) -> Tuple[Optional[requests.Response], float]:
        """Enhanced request method with timing and better error handling"""
        url = f"{self.base_url}{endpoint}"
        default_headers = {"Content-Type": "application/json"}
        
        # Add authorization if available
        if self.access_token and not (endpoint.endswith('/login/') or endpoint.endswith('/register/')):
            default_headers["Authorization"] = f"Bearer {self.access_token}"
            
        if headers:
            default_headers.update(headers)
            
        # Remove Content-Type for file uploads
        if files:
            default_headers.pop("Content-Type", None)
            
        start_time = time.time()
        try:
            if method == "GET":
                response = requests.get(url, headers=default_headers, params=params, timeout=30)
            elif method == "POST":
                if files:
                    response = requests.post(url, data=data, files=files, headers=default_headers, timeout=30)
                else:
                    response = requests.post(url, json=data, headers=default_headers, timeout=30)
            elif method == "PUT":
                response = requests.put(url, json=data, headers=default_headers, timeout=30)
            elif method == "PATCH":
                response = requests.patch(url, json=data, headers=default_headers, timeout=30)
            elif method == "DELETE":
                response = requests.delete(url, headers=default_headers, timeout=30)
            else:
                raise ValueError(f"Unsupported method: {method}")
                
            response_time = time.time() - start_time
            return response, response_time
            
        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            print(f"Request failed: {str(e)}")
            return None, response_time
    
    def validate_response_structure(self, response_data: Dict, required_fields: List[str]) -> bool:
        """Validate response contains required fields"""
        for field in required_fields:
            if '.' in field:
                # Handle nested fields like 'data.user.id'
                parts = field.split('.')
                current = response_data
                for part in parts:
                    if not isinstance(current, dict) or part not in current:
                        return False
                    current = current[part]
            else:
                if field not in response_data:
                    return False
        return True
    
    # ==================== AUTHENTICATION TESTS ====================
    
    def test_user_registration(self) -> bool:
        """Test user registration endpoint"""
        data = {
            "email": self.test_email,
            "password": self.test_password,
            "name": self.test_name
        }
        
        response, response_time = self.make_request("POST", "/api/auth/register/", data)
        
        if not response:
            self.log_test("User Registration", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 201:
            try:
                response_data = response.json()
                required_fields = ["success", "data.user.id", "data.user.name", "data.user.email", "data.tokens.access"]
                
                if self.validate_response_structure(response_data, required_fields):
                    self.access_token = response_data['data']['tokens']['access']
                    self.refresh_token = response_data['data']['tokens'].get('refresh')
                    self.user_id = response_data['data']['user']['id']
                    
                    self.log_test("User Registration", "PASS", 
                                f"User created: {response_data['data']['user']['name']}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("User Registration", "FAIL", 
                                "Missing required fields in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("User Registration", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("User Registration", "FAIL", 
                        f"Registration failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_user_login(self) -> bool:
        """Test user login endpoint"""
        data = {
            "email": self.test_email,
            "password": self.test_password
        }
        
        response, response_time = self.make_request("POST", "/api/auth/login/", data)
        
        if not response:
            self.log_test("User Login", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                required_fields = ["success", "data.user.id", "data.user.name", "data.tokens.access"]
                
                if self.validate_response_structure(response_data, required_fields):
                    # Update tokens (in case they changed)
                    self.access_token = response_data['data']['tokens']['access']
                    self.refresh_token = response_data['data']['tokens'].get('refresh')
                    
                    self.log_test("User Login", "PASS", 
                                f"User logged in: {response_data['data']['user']['name']}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("User Login", "FAIL", 
                                "Missing required fields in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("User Login", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("User Login", "FAIL", 
                        f"Login failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_user_profile(self) -> bool:
        """Test user profile retrieval"""
        if not self.access_token:
            self.log_test("User Profile", "SKIP", "No access token available")
            return False
            
        response, response_time = self.make_request("GET", "/api/auth/profile/")
        
        if not response:
            self.log_test("User Profile", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                required_fields = ["success", "data.id", "data.name", "data.email"]
                
                if self.validate_response_structure(response_data, required_fields):
                    self.log_test("User Profile", "PASS", 
                                f"Profile retrieved: {response_data['data']['name']}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("User Profile", "FAIL", 
                                "Missing required fields in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("User Profile", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("User Profile", "FAIL", 
                        f"Profile retrieval failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_token_refresh(self) -> bool:
        """Test token refresh endpoint"""
        if not self.refresh_token:
            self.log_test("Token Refresh", "SKIP", "No refresh token available")
            return False
            
        data = {"refresh": self.refresh_token}
        response, response_time = self.make_request("POST", "/api/auth/token/refresh/", data)
        
        if not response:
            self.log_test("Token Refresh", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                if "access" in response_data:
                    self.access_token = response_data["access"]
                    self.log_test("Token Refresh", "PASS", 
                                "Token refreshed successfully", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Token Refresh", "FAIL", 
                                "No access token in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Token Refresh", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Token Refresh", "FAIL", 
                        f"Token refresh failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    # ==================== MESSAGING TESTS ====================
    
    def test_user_search(self) -> bool:
        """Test user search functionality"""
        if not self.access_token:
            self.log_test("User Search", "SKIP", "No access token available")
            return False
            
        params = {"q": "test"}
        response, response_time = self.make_request("GET", "/api/messaging/users/search/", params=params)
        
        if not response:
            self.log_test("User Search", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                required_fields = ["success", "results"]
                
                if self.validate_response_structure(response_data, required_fields):
                    results = response_data['results']
                    if len(results) > 0:
                        user = results[0]
                        if all(field in user for field in ['id', 'name', 'email']):
                            self.target_user_id = user['id']
                            self.log_test("User Search", "PASS", 
                                        f"Found {len(results)} users, first: {user['name']}", 
                                        response.status_code, response_time)
                            return True
                        else:
                            self.log_test("User Search", "FAIL", 
                                        "Missing required user fields in results", 
                                        response.status_code, response_time)
                    else:
                        self.log_test("User Search", "PASS", 
                                    "Search completed (no results)", 
                                    response.status_code, response_time)
                        return True
                else:
                    self.log_test("User Search", "FAIL", 
                                "Missing required fields in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("User Search", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("User Search", "FAIL", 
                        f"User search failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_get_user_profile_by_id(self) -> bool:
        """Test getting user profile by ID"""
        if not self.access_token:
            self.log_test("Get User Profile by ID", "SKIP", "No access token available")
            return False
            
        if not self.target_user_id:
            self.log_test("Get User Profile by ID", "SKIP", "No target user ID available")
            return False
            
        response, response_time = self.make_request("GET", f"/api/messaging/users/{self.target_user_id}/")
        
        if not response:
            self.log_test("Get User Profile by ID", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                required_fields = ["success", "data.id", "data.name", "data.email"]
                
                if self.validate_response_structure(response_data, required_fields):
                    self.log_test("Get User Profile by ID", "PASS", 
                                f"Profile retrieved: {response_data['data']['name']}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Get User Profile by ID", "FAIL", 
                                "Missing required fields in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Get User Profile by ID", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Get User Profile by ID", "FAIL", 
                        f"Profile retrieval failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_list_conversations(self) -> bool:
        """Test listing conversations"""
        if not self.access_token:
            self.log_test("List Conversations", "SKIP", "No access token available")
            return False
            
        params = {"page": "1", "page_size": "20"}
        response, response_time = self.make_request("GET", "/api/messaging/conversations/", params=params)
        
        if not response:
            self.log_test("List Conversations", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                if "results" in response_data:
                    results = response_data['results']
                    self.log_test("List Conversations", "PASS", 
                                f"Retrieved {len(results)} conversations", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("List Conversations", "FAIL", 
                                "Missing results field in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("List Conversations", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("List Conversations", "FAIL", 
                        f"Conversation listing failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_create_direct_conversation(self) -> bool:
        """Test creating a direct conversation"""
        if not self.access_token:
            self.log_test("Create Direct Conversation", "SKIP", "No access token available")
            return False
            
        if not self.target_user_id:
            self.log_test("Create Direct Conversation", "SKIP", "No target user ID available")
            return False
            
        data = {
            "type": "DIRECT",
            "participant_ids": [self.target_user_id]
        }
        
        response, response_time = self.make_request("POST", "/api/messaging/conversations/create/", data)
        
        if not response:
            self.log_test("Create Direct Conversation", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code in [200, 201]:
            try:
                response_data = response.json()
                if "id" in response_data:
                    self.conversation_id = response_data['id']
                    self.log_test("Create Direct Conversation", "PASS", 
                                f"Conversation created/retrieved: {self.conversation_id}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Create Direct Conversation", "FAIL", 
                                "Missing conversation ID in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Create Direct Conversation", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Create Direct Conversation", "FAIL", 
                        f"Conversation creation failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_create_group_conversation(self) -> bool:
        """Test creating a group conversation"""
        if not self.access_token:
            self.log_test("Create Group Conversation", "SKIP", "No access token available")
            return False
            
        if not self.target_user_id:
            self.log_test("Create Group Conversation", "SKIP", "No target user ID available")
            return False
            
        data = {
            "type": "GROUP",
            "name": "Test Group Chat",
            "participant_ids": [self.target_user_id]
        }
        
        response, response_time = self.make_request("POST", "/api/messaging/conversations/create/", data)
        
        if not response:
            self.log_test("Create Group Conversation", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 201:
            try:
                response_data = response.json()
                if "id" in response_data:
                    self.group_id = response_data['id']
                    self.log_test("Create Group Conversation", "PASS", 
                                f"Group created: {response_data.get('name', 'Unnamed')}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Create Group Conversation", "FAIL", 
                                "Missing group ID in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Create Group Conversation", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Create Group Conversation", "FAIL", 
                        f"Group creation failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_send_plaintext_message(self) -> bool:
        """Test sending a plaintext message"""
        if not self.access_token:
            self.log_test("Send Plaintext Message", "SKIP", "No access token available")
            return False
            
        if not self.conversation_id:
            self.log_test("Send Plaintext Message", "SKIP", "No conversation ID available")
            return False
            
        data = {
            "content": "Hello! This is a test plaintext message.",
            "message_type": "TEXT"
        }
        
        response, response_time = self.make_request("POST", f"/api/messaging/conversations/{self.conversation_id}/send/", data)
        
        if not response:
            self.log_test("Send Plaintext Message", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 201:
            try:
                response_data = response.json()
                if "id" in response_data:
                    self.message_id = response_data['id']
                    self.log_test("Send Plaintext Message", "PASS", 
                                f"Message sent: {response_data.get('content', 'No content')[:50]}...", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Send Plaintext Message", "FAIL", 
                                "Missing message ID in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Send Plaintext Message", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Send Plaintext Message", "FAIL", 
                        f"Message sending failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_send_encrypted_message(self) -> bool:
        """Test sending an encrypted message"""
        if not self.access_token:
            self.log_test("Send Encrypted Message", "SKIP", "No access token available")
            return False
            
        if not self.conversation_id:
            self.log_test("Send Encrypted Message", "SKIP", "No conversation ID available")
            return False
            
        data = {
            "encrypted_content": base64.b64encode(b"This is fake encrypted content").decode(),
            "iv": base64.b64encode(b"randomIV96bit").decode(),
            "sender_ratchet_key": base64.b64encode(b"fakeRatchetKeySPKIFormat").decode(),
            "message_number": 1,
            "previous_chain_length": 0,
            "message_type": "TEXT"
        }
        
        response, response_time = self.make_request("POST", f"/api/messaging/conversations/{self.conversation_id}/send/", data)
        
        if not response:
            self.log_test("Send Encrypted Message", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 201:
            try:
                response_data = response.json()
                if "id" in response_data and response_data.get("is_encrypted"):
                    self.log_test("Send Encrypted Message", "PASS", 
                                "Encrypted message sent successfully", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Send Encrypted Message", "FAIL", 
                                "Message not properly encrypted or missing ID", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Send Encrypted Message", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Send Encrypted Message", "FAIL", 
                        f"Encrypted message sending failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_get_conversation_messages(self) -> bool:
        """Test getting conversation messages"""
        if not self.access_token:
            self.log_test("Get Conversation Messages", "SKIP", "No access token available")
            return False
            
        if not self.conversation_id:
            self.log_test("Get Conversation Messages", "SKIP", "No conversation ID available")
            return False
            
        params = {"page": "1", "page_size": "50"}
        response, response_time = self.make_request("GET", f"/api/messaging/conversations/{self.conversation_id}/messages/", params=params)
        
        if not response:
            self.log_test("Get Conversation Messages", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                if "results" in response_data:
                    messages = response_data['results']
                    self.log_test("Get Conversation Messages", "PASS", 
                                f"Retrieved {len(messages)} messages", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Get Conversation Messages", "FAIL", 
                                "Missing results field in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Get Conversation Messages", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Get Conversation Messages", "FAIL", 
                        f"Message retrieval failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_get_conversation_encryption_status(self) -> bool:
        """Test getting conversation encryption status"""
        if not self.access_token:
            self.log_test("Get Conversation Encryption Status", "SKIP", "No access token available")
            return False
            
        if not self.conversation_id:
            self.log_test("Get Conversation Encryption Status", "SKIP", "No conversation ID available")
            return False
            
        response, response_time = self.make_request("GET", f"/api/messaging/conversations/{self.conversation_id}/encryption-status/")
        
        if not response:
            self.log_test("Get Conversation Encryption Status", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                required_fields = ["conversation_id", "is_encrypted", "participants"]
                
                if self.validate_response_structure(response_data, required_fields):
                    encryption_status = "Encrypted" if response_data['is_encrypted'] else "Not Encrypted"
                    self.log_test("Get Conversation Encryption Status", "PASS", 
                                f"Status: {encryption_status}, Participants: {len(response_data['participants'])}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Get Conversation Encryption Status", "FAIL", 
                                "Missing required fields in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Get Conversation Encryption Status", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Get Conversation Encryption Status", "FAIL", 
                        f"Encryption status retrieval failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    # ==================== GROUP MANAGEMENT TESTS ====================
    
    def test_create_group(self) -> bool:
        """Test creating a group"""
        if not self.access_token:
            self.log_test("Create Group", "SKIP", "No access token available")
            return False
            
        if not self.target_user_id:
            self.log_test("Create Group", "SKIP", "No target user ID available")
            return False
            
        data = {
            "name": "Test Group",
            "description": "A test group for API testing",
            "member_ids": [self.target_user_id],
            "max_participants": 50,
            "is_public": False
        }
        
        response, response_time = self.make_request("POST", "/api/messaging/groups/create/", data)
        
        if not response:
            self.log_test("Create Group", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 201:
            try:
                response_data = response.json()
                if "id" in response_data:
                    self.group_id = response_data['id']
                    self.log_test("Create Group", "PASS", 
                                f"Group created: {response_data.get('name', 'Unnamed')}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Create Group", "FAIL", 
                                "Missing group ID in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Create Group", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Create Group", "FAIL", 
                        f"Group creation failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_add_group_member(self) -> bool:
        """Test adding a member to a group"""
        if not self.access_token:
            self.log_test("Add Group Member", "SKIP", "No access token available")
            return False
            
        if not self.group_id:
            self.log_test("Add Group Member", "SKIP", "No group ID available")
            return False
            
        if not self.target_user_id:
            self.log_test("Add Group Member", "SKIP", "No target user ID available")
            return False
            
        data = {
            "user_id": self.target_user_id,
            "role": "member"
        }
        
        response, response_time = self.make_request("POST", f"/api/messaging/groups/{self.group_id}/add-member/", data)
        
        if not response:
            self.log_test("Add Group Member", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code in [200, 201]:
            self.log_test("Add Group Member", "PASS", 
                        "Member added successfully", 
                        response.status_code, response_time)
            return True
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Add Group Member", "FAIL", 
                        f"Adding member failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_update_group_info(self) -> bool:
        """Test updating group information"""
        if not self.access_token:
            self.log_test("Update Group Info", "SKIP", "No access token available")
            return False
            
        if not self.group_id:
            self.log_test("Update Group Info", "SKIP", "No group ID available")
            return False
            
        data = {
            "name": "Updated Test Group",
            "description": "Updated group description for testing",
            "avatar_url": "https://example.com/avatar.jpg"
        }
        
        response, response_time = self.make_request("PUT", f"/api/messaging/groups/{self.group_id}/update/", data)
        
        if not response:
            self.log_test("Update Group Info", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                if "name" in response_data and response_data["name"] == data["name"]:
                    self.log_test("Update Group Info", "PASS", 
                                f"Group updated: {response_data['name']}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Update Group Info", "FAIL", 
                                "Group name not updated properly", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Update Group Info", "PASS", 
                            "Group updated (no JSON response)", 
                            response.status_code, response_time)
                return True
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Update Group Info", "FAIL", 
                        f"Group update failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_remove_group_member(self) -> bool:
        """Test removing a member from a group"""
        if not self.access_token:
            self.log_test("Remove Group Member", "SKIP", "No access token available")
            return False
            
        if not self.group_id:
            self.log_test("Remove Group Member", "SKIP", "No group ID available")
            return False
            
        if not self.target_user_id:
            self.log_test("Remove Group Member", "SKIP", "No target user ID available")
            return False
            
        response, response_time = self.make_request("DELETE", f"/api/messaging/groups/{self.group_id}/remove-member/{self.target_user_id}/")
        
        if not response:
            self.log_test("Remove Group Member", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code in [200, 204]:
            self.log_test("Remove Group Member", "PASS", 
                        "Member removed successfully", 
                        response.status_code, response_time)
            return True
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Remove Group Member", "FAIL", 
                        f"Removing member failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_leave_group(self) -> bool:
        """Test leaving a group"""
        if not self.access_token:
            self.log_test("Leave Group", "SKIP", "No access token available")
            return False
            
        if not self.group_id:
            self.log_test("Leave Group", "SKIP", "No group ID available")
            return False
            
        response, response_time = self.make_request("POST", f"/api/messaging/groups/{self.group_id}/leave/")
        
        if not response:
            self.log_test("Leave Group", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code in [200, 204]:
            self.log_test("Leave Group", "PASS", 
                        "Left group successfully", 
                        response.status_code, response_time)
            return True
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Leave Group", "FAIL", 
                        f"Leaving group failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    # ==================== ENCRYPTION TESTS ====================
    
    def test_upload_key_bundle(self) -> bool:
        """Test uploading a key bundle"""
        if not self.access_token:
            self.log_test("Upload Key Bundle", "SKIP", "No access token available")
            return False
            
        data = {
            "identity_key": base64.b64encode(b"fake_identity_key_32_bytes_long").decode(),
            "signed_prekey": {
                "key_id": 1,
                "public_key": base64.b64encode(b"fake_signed_prekey_public_key").decode(),
                "signature": base64.b64encode(b"fake_signature_64_bytes_long").decode()
            },
            "prekeys": [
                {
                    "key_id": i,
                    "public_key": base64.b64encode(f"fake_prekey_{i}_public_key".encode()).decode()
                }
                for i in range(1, 6)
            ]
        }
        
        response, response_time = self.make_request("POST", "/api/encryption/key-bundle/", data)
        
        if not response:
            self.log_test("Upload Key Bundle", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 201:
            try:
                response_data = response.json()
                if response_data.get("success"):
                    self.log_test("Upload Key Bundle", "PASS", 
                                "Key bundle uploaded successfully", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Upload Key Bundle", "FAIL", 
                                "Upload not successful", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Upload Key Bundle", "PASS", 
                            "Key bundle uploaded (no JSON response)", 
                            response.status_code, response_time)
                return True
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Upload Key Bundle", "FAIL", 
                        f"Key bundle upload failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_get_key_bundle(self) -> bool:
        """Test getting a user's key bundle"""
        if not self.access_token:
            self.log_test("Get Key Bundle", "SKIP", "No access token available")
            return False
            
        if not self.target_user_id:
            self.log_test("Get Key Bundle", "SKIP", "No target user ID available")
            return False
            
        response, response_time = self.make_request("GET", f"/api/encryption/key-bundle/{self.target_user_id}/")
        
        if not response:
            self.log_test("Get Key Bundle", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                required_fields = ["identity_key", "signed_prekey", "prekey"]
                
                if self.validate_response_structure(response_data, required_fields):
                    self.log_test("Get Key Bundle", "PASS", 
                                "Key bundle retrieved successfully", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Get Key Bundle", "FAIL", 
                                "Missing required fields in key bundle", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Get Key Bundle", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Get Key Bundle", "FAIL", 
                        f"Key bundle retrieval failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_upload_one_time_prekeys(self) -> bool:
        """Test uploading one-time pre-keys"""
        if not self.access_token:
            self.log_test("Upload One-Time Pre-Keys", "SKIP", "No access token available")
            return False
            
        data = {
            "prekeys": [
                {
                    "key_id": i,
                    "public_key": base64.b64encode(f"fake_onetime_prekey_{i}".encode()).decode()
                }
                for i in range(1, 6)
            ]
        }
        
        response, response_time = self.make_request("POST", "/api/encryption/prekeys/", data)
        
        if not response:
            self.log_test("Upload One-Time Pre-Keys", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 201:
            try:
                response_data = response.json()
                if "count" in response_data and response_data["count"] == 5:
                    self.log_test("Upload One-Time Pre-Keys", "PASS", 
                                f"Uploaded {response_data['count']} pre-keys", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Upload One-Time Pre-Keys", "FAIL", 
                                "Incorrect count in response", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Upload One-Time Pre-Keys", "PASS", 
                            "Pre-keys uploaded (no JSON response)", 
                            response.status_code, response_time)
                return True
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Upload One-Time Pre-Keys", "FAIL", 
                        f"Pre-key upload failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    def test_get_prekey_count(self) -> bool:
        """Test getting pre-key count"""
        if not self.access_token:
            self.log_test("Get Pre-Key Count", "SKIP", "No access token available")
            return False
            
        response, response_time = self.make_request("GET", "/api/encryption/prekeys/count/")
        
        if not response:
            self.log_test("Get Pre-Key Count", "ERROR", "No response received", error_message="Network error")
            return False
            
        if response.status_code == 200:
            try:
                response_data = response.json()
                required_fields = ["available_count", "total_count", "used_count"]
                
                if self.validate_response_structure(response_data, required_fields):
                    self.log_test("Get Pre-Key Count", "PASS", 
                                f"Available: {response_data['available_count']}, Total: {response_data['total_count']}", 
                                response.status_code, response_time)
                    return True
                else:
                    self.log_test("Get Pre-Key Count", "FAIL", 
                                "Missing required count fields", 
                                response.status_code, response_time)
            except json.JSONDecodeError:
                self.log_test("Get Pre-Key Count", "FAIL", 
                            "Invalid JSON response", 
                            response.status_code, response_time)
        else:
            error_msg = response.text[:200] if response.text else "No error message"
            self.log_test("Get Pre-Key Count", "FAIL", 
                        f"Pre-key count retrieval failed: {error_msg}", 
                        response.status_code, response_time)
        
        return False
    
    # ==================== ERROR CASE TESTS ====================
    
    def test_authentication_errors(self) -> bool:
        """Test authentication error cases"""
        # Test invalid login
        data = {"email": "<EMAIL>", "password": "wrongpassword"}
        response, response_time = self.make_request("POST", "/api/auth/login/", data)
        
        if response and response.status_code in [400, 401]:
            self.log_test("Authentication Error - Invalid Login", "PASS", 
                        "Correctly rejected invalid credentials", 
                        response.status_code, response_time)
            return True
        else:
            self.log_test("Authentication Error - Invalid Login", "FAIL", 
                        "Should have rejected invalid credentials", 
                        response.status_code if response else None, response_time)
            return False
    
    def test_authorization_errors(self) -> bool:
        """Test authorization error cases"""
        # Temporarily remove token
        original_token = self.access_token
        self.access_token = None
        
        response, response_time = self.make_request("GET", "/api/auth/profile/")
        
        # Restore token
        self.access_token = original_token
        
        if response and response.status_code in [401, 403]:
            self.log_test("Authorization Error - No Token", "PASS", 
                        "Correctly rejected request without token", 
                        response.status_code, response_time)
            return True
        else:
            self.log_test("Authorization Error - No Token", "FAIL", 
                        "Should have rejected request without token", 
                        response.status_code if response else None, response_time)
            return False
    
    def test_validation_errors(self) -> bool:
        """Test validation error cases"""
        # Test registration with invalid email
        data = {
            "email": "invalid-email",
            "password": "short",
            "name": ""
        }
        
        response, response_time = self.make_request("POST", "/api/auth/register/", data)
        
        if response and response.status_code == 400:
            self.log_test("Validation Error - Invalid Data", "PASS", 
                        "Correctly rejected invalid registration data", 
                        response.status_code, response_time)
            return True
        else:
            self.log_test("Validation Error - Invalid Data", "FAIL", 
                        "Should have rejected invalid registration data", 
                        response.status_code if response else None, response_time)
            return False
    
    def test_not_found_errors(self) -> bool:
        """Test not found error cases"""
        fake_user_id = "00000000-0000-0000-0000-000000000000"
        response, response_time = self.make_request("GET", f"/api/messaging/users/{fake_user_id}/")
        
        if response and response.status_code == 404:
            self.log_test("Not Found Error - Invalid User ID", "PASS", 
                        "Correctly returned 404 for non-existent user", 
                        response.status_code, response_time)
            return True
        else:
            self.log_test("Not Found Error - Invalid User ID", "FAIL", 
                        "Should have returned 404 for non-existent user", 
                        response.status_code if response else None, response_time)
            return False
    
    # ==================== TEST SUITE RUNNERS ====================
    
    def run_authentication_tests(self) -> Dict[str, bool]:
        """Run all authentication tests"""
        self.start_suite("Authentication Tests")
        
        results = {
            "user_registration": self.test_user_registration(),
            "user_login": self.test_user_login(),
            "user_profile": self.test_user_profile(),
            "token_refresh": self.test_token_refresh(),
        }
        
        self.end_suite()
        return results
    
    def run_messaging_tests(self) -> Dict[str, bool]:
        """Run all messaging tests"""
        self.start_suite("Messaging Tests")
        
        results = {
            "user_search": self.test_user_search(),
            "get_user_profile_by_id": self.test_get_user_profile_by_id(),
            "list_conversations": self.test_list_conversations(),
            "create_direct_conversation": self.test_create_direct_conversation(),
            "create_group_conversation": self.test_create_group_conversation(),
            "send_plaintext_message": self.test_send_plaintext_message(),
            "send_encrypted_message": self.test_send_encrypted_message(),
            "get_conversation_messages": self.test_get_conversation_messages(),
            "get_conversation_encryption_status": self.test_get_conversation_encryption_status(),
        }
        
        self.end_suite()
        return results
    
    def run_group_management_tests(self) -> Dict[str, bool]:
        """Run all group management tests"""
        self.start_suite("Group Management Tests")
        
        results = {
            "create_group": self.test_create_group(),
            "add_group_member": self.test_add_group_member(),
            "update_group_info": self.test_update_group_info(),
            "remove_group_member": self.test_remove_group_member(),
            "leave_group": self.test_leave_group(),
        }
        
        self.end_suite()
        return results
    
    def run_encryption_tests(self) -> Dict[str, bool]:
        """Run all encryption tests"""
        self.start_suite("Encryption Tests")
        
        results = {
            "upload_key_bundle": self.test_upload_key_bundle(),
            "get_key_bundle": self.test_get_key_bundle(),
            "upload_one_time_prekeys": self.test_upload_one_time_prekeys(),
            "get_prekey_count": self.test_get_prekey_count(),
        }
        
        self.end_suite()
        return results
    
    def run_error_case_tests(self) -> Dict[str, bool]:
        """Run all error case tests"""
        self.start_suite("Error Case Tests")
        
        results = {
            "authentication_errors": self.test_authentication_errors(),
            "authorization_errors": self.test_authorization_errors(),
            "validation_errors": self.test_validation_errors(),
            "not_found_errors": self.test_not_found_errors(),
        }
        
        self.end_suite()
        return results
    
    def generate_detailed_report(self) -> None:
        """Generate a comprehensive test report"""
        print("\n" + "=" * 80)
        print("COMPREHENSIVE API TEST REPORT")
        print("=" * 80)
        print(f"Test Run Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Base URL: {self.base_url}")
        print()
        
        total_tests = 0
        total_passed = 0
        total_failed = 0
        total_skipped = 0
        total_errors = 0
        
        for suite in self.test_suites:
            duration = (suite.end_time - suite.start_time).total_seconds() if suite.end_time and suite.start_time else 0
            
            print(f"📋 {suite.name}")
            print(f"   Duration: {duration:.2f}s")
            print(f"   Results: {suite.passed} passed, {suite.failed} failed, {suite.skipped} skipped, {suite.errors} errors")
            print(f"   Success Rate: {suite.success_rate:.1f}%")
            
            # Show failed tests
            failed_tests = [r for r in suite.results if r.status in ["FAIL", "ERROR"]]
            if failed_tests:
                print("   ❌ Failed Tests:")
                for test in failed_tests:
                    print(f"      - {test.name}: {test.error_message or test.details}")
            
            print()
            
            total_tests += suite.total
            total_passed += suite.passed
            total_failed += suite.failed
            total_skipped += suite.skipped
            total_errors += suite.errors
        
        print("=" * 80)
        print("OVERALL SUMMARY")
        print("=" * 80)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {total_passed}")
        print(f"❌ Failed: {total_failed}")
        print(f"⚠️  Skipped: {total_skipped}")
        print(f"🔥 Errors: {total_errors}")
        print(f"Success Rate: {(total_passed/total_tests)*100:.1f}%" if total_tests > 0 else "No tests run")
        
        if total_failed == 0 and total_errors == 0:
            print("\n🎉 ALL TESTS PASSED! API is working correctly.")
        else:
            print(f"\n⚠️  {total_failed + total_errors} tests failed. Review the issues above.")
        
        print("=" * 80)
    
    def run_all_tests(self) -> None:
        """Run all test suites"""
        print("🚀 Starting Comprehensive API Testing...")
        print(f"Target: {self.base_url}")
        print()
        
        # Run test suites in order
        auth_results = self.run_authentication_tests()
        messaging_results = self.run_messaging_tests()
        group_results = self.run_group_management_tests()
        encryption_results = self.run_encryption_tests()
        error_results = self.run_error_case_tests()
        
        # Generate comprehensive report
        self.generate_detailed_report()

if __name__ == "__main__":
    import sys
    
    # Allow custom base URL
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8000"
    
    tester = ComprehensiveAPITester(base_url)
    tester.run_all_tests()
