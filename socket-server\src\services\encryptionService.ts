// socket-server/src/services/encryptionService.ts
/**
 * Encryption service for handling key bundles, one-time pre-keys, and session management.
 * This service provides encrypted message routing without server-side decryption.
 */

import { PrismaClient } from '@prisma/client';
import {
  UserKeyBundle,
  OneTimePreKey,
  ConversationSession,
  MessageKey,
  KeyBundleResponse,
  validateSocketEvent,
  KeyBundleUploadSchema,
  OneTimePreKeyBatchSchema,
} from '../schemas/encryptionSchemas';

export class EncryptionService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get user's key bundle for key exchange
   * Atomically consumes one unused one-time pre-key
   */
  async getUserKeyBundle(userId: string, requesterId: string): Promise<KeyBundleResponse | null> {
    try {
      // Get user's key bundle
      const keyBundle = await this.prisma.userKeyBundle.findUnique({
        where: { userId },
      });

      if (!keyBundle) {
        return null;
      }

      // Atomically consume one unused one-time pre-key
      let oneTimePrekey: any = null;
      
      await this.prisma.$transaction(async (tx) => {
        // Find an unused one-time pre-key
        const unusedPrekey = await tx.oneTimePreKey.findFirst({
          where: {
            userId,
            isUsed: false,
          },
          orderBy: {
            createdAt: 'asc', // Use oldest first
          },
        });

        if (unusedPrekey) {
          // Mark as used
          oneTimePrekey = await tx.oneTimePreKey.update({
            where: { id: unusedPrekey.id },
            data: {
              isUsed: true,
              usedAt: new Date(),
            },
          });
        }
      });

      // Build response
      const response: KeyBundleResponse = {
        identityPublicKey: keyBundle.identityPublicKey,
        signedPrekey: {
          id: keyBundle.signedPrekeyId,
          publicKey: keyBundle.signedPrekeyPublic,
          signature: keyBundle.signedPrekeySignature,
        },
      };

      if (oneTimePrekey) {
        response.oneTimePrekey = {
          id: oneTimePrekey.id,
          publicKey: oneTimePrekey.publicKey,
        };
      }

      console.log(`Key bundle retrieved for user ${userId} by ${requesterId}, OPK consumed: ${!!oneTimePrekey}`);
      return response;

    } catch (error) {
      console.error('Error getting user key bundle:', error);
      throw new Error('Failed to retrieve key bundle');
    }
  }

  /**
   * Get available one-time pre-key count for a user
   */
  async getOneTimePreKeyCount(userId: string): Promise<{ available: number; total: number; used: number }> {
    try {
      const [total, available] = await Promise.all([
        this.prisma.oneTimePreKey.count({
          where: { userId },
        }),
        this.prisma.oneTimePreKey.count({
          where: { userId, isUsed: false },
        }),
      ]);

      return {
        available,
        total,
        used: total - available,
      };
    } catch (error) {
      console.error('Error getting pre-key count:', error);
      throw new Error('Failed to get pre-key count');
    }
  }

  /**
   * Check if a user has a key bundle (encryption enabled)
   */
  async hasKeyBundle(userId: string): Promise<boolean> {
    try {
      const keyBundle = await this.prisma.userKeyBundle.findUnique({
        where: { userId },
        select: { id: true },
      });
      return !!keyBundle;
    } catch (error) {
      console.error('Error checking key bundle:', error);
      return false;
    }
  }

  /**
   * Get or create conversation session for a participant
   */
  async getConversationSession(
    conversationId: string,
    participantId: string
  ): Promise<ConversationSession | null> {
    try {
      const session = await this.prisma.conversationSession.findUnique({
        where: {
          conversationId_participantId: {
            conversationId,
            participantId,
          },
        },
      });

      return session as ConversationSession;
    } catch (error) {
      console.error('Error getting conversation session:', error);
      return null;
    }
  }

  /**
   * Create or update conversation session
   */
  async upsertConversationSession(
    conversationId: string,
    participantId: string,
    sessionData: Partial<ConversationSession>
  ): Promise<ConversationSession> {
    try {
      const session = await this.prisma.conversationSession.upsert({
        where: {
          conversationId_participantId: {
            conversationId,
            participantId,
          },
        },
        update: {
          sessionState: sessionData.sessionState,
          rootKey: sessionData.rootKey,
          chainKeySend: sessionData.chainKeySend,
          chainKeyReceive: sessionData.chainKeyReceive,
          messageNumberSend: sessionData.messageNumberSend,
          messageNumberReceive: sessionData.messageNumberReceive,
          previousChainLength: sessionData.previousChainLength,
          updatedAt: new Date(),
        },
        create: {
          conversationId,
          participantId,
          sessionState: sessionData.sessionState || {},
          rootKey: sessionData.rootKey || '',
          chainKeySend: sessionData.chainKeySend,
          chainKeyReceive: sessionData.chainKeyReceive,
          messageNumberSend: sessionData.messageNumberSend || 0,
          messageNumberReceive: sessionData.messageNumberReceive || 0,
          previousChainLength: sessionData.previousChainLength || 0,
          createdAt: new Date(),
        },
      });

      return session as ConversationSession;
    } catch (error) {
      console.error('Error upserting conversation session:', error);
      throw new Error('Failed to update conversation session');
    }
  }

  /**
   * Store message key for out-of-order message handling
   */
  async storeMessageKey(
    sessionId: string,
    messageNumber: number,
    messageKey: string,
    senderRatchetKey: string
  ): Promise<void> {
    try {
      await this.prisma.messageKey.create({
        data: {
          sessionId,
          messageNumber,
          messageKey,
          senderRatchetKey,
          createdAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Error storing message key:', error);
      // Don't throw - this is for optimization, not critical
    }
  }

  /**
   * Get message key for decryption
   */
  async getMessageKey(
    sessionId: string,
    messageNumber: number,
    senderRatchetKey: string
  ): Promise<MessageKey | null> {
    try {
      const messageKey = await this.prisma.messageKey.findUnique({
        where: {
          sessionId_senderRatchetKey_messageNumber: {
            sessionId,
            senderRatchetKey,
            messageNumber,
          },
        },
      });

      return messageKey;
    } catch (error) {
      console.error('Error getting message key:', error);
      return null;
    }
  }

  /**
   * Clean up old message keys to prevent memory exhaustion
   */
  async cleanupOldMessageKeys(sessionId: string, keepCount: number = 1000): Promise<void> {
    try {
      // Get count of message keys for this session
      const count = await this.prisma.messageKey.count({
        where: { sessionId },
      });

      if (count > keepCount) {
        // Delete oldest keys, keeping the most recent ones
        const keysToDelete = await this.prisma.messageKey.findMany({
          where: { sessionId },
          orderBy: { createdAt: 'asc' },
          take: count - keepCount,
          select: { id: true },
        });

        if (keysToDelete.length > 0) {
          await this.prisma.messageKey.deleteMany({
            where: {
              id: {
                in: keysToDelete.map(k => k.id),
              },
            },
          });

          console.log(`Cleaned up ${keysToDelete.length} old message keys for session ${sessionId}`);
        }
      }
    } catch (error) {
      console.error('Error cleaning up message keys:', error);
      // Don't throw - this is maintenance, not critical
    }
  }

  /**
   * Check if both users in a conversation have encryption enabled
   */
  async isConversationEncrypted(conversationId: string): Promise<boolean> {
    try {
      const participants = await this.prisma.conversationParticipant.findMany({
        where: { conversationId },
        include: {
          user: {
            include: {
              keyBundle: {
                select: { id: true },
              },
            },
          },
        },
      });

      // All participants must have key bundles for encryption
      return participants.every(p => p.user.keyBundle);
    } catch (error) {
      console.error('Error checking conversation encryption:', error);
      return false;
    }
  }

  /**
   * Get conversation participants with encryption status
   */
  async getConversationParticipants(conversationId: string) {
    try {
      const participants = await this.prisma.conversationParticipant.findMany({
        where: { conversationId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              keyBundle: {
                select: { id: true },
              },
            },
          },
        },
      });

      return participants.map(p => ({
        ...p.user,
        hasEncryption: !!p.user.keyBundle,
      }));
    } catch (error) {
      console.error('Error getting conversation participants:', error);
      throw new Error('Failed to get conversation participants');
    }
  }
}
