# backend/calling/views.py
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.utils import timezone
from django.db import transaction
from django.db.models import Q
from pydantic import ValidationError

from .models import Call, CallEvent, CallQualityMetric
from .serializers import (
    CallSerializer, CallEventSerializer, CallCreateSerializer,
    CallSDPUpdateSerializer, CallQualityMetricSerializer, CallQualityReportSerializer
)
from .schemas import (
    CallInitiateRequest, CallResponse, CallHistoryResponse, CallSDPUpdateRequest,
    CallQualityReportRequest, IncomingCallNotification, CallStatusNotification,
    UserBasic
)
from messaging.models import Conversation
from utils.socket_service import SocketService


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def initiate_call(request):
    """Initiate a new call"""
    serializer = CallCreateSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    conversation_id = serializer.validated_data['conversation_id']
    call_type = serializer.validated_data['call_type']
    
    try:
        conversation = Conversation.objects.get(id=conversation_id, type='DIRECT')
        
        # Check if user is participant
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Not a participant in this conversation'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        # Get the other participant (callee)
        callee_participant = conversation.participants.exclude(user=request.user).first()
        if not callee_participant:
            return Response({'error': 'No other participant found'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        callee = callee_participant.user
        
        # Check if there's already an active call
        active_call = Call.objects.filter(
            conversation=conversation,
            status__in=['initiated', 'ringing', 'answered', 'active']
        ).first()
        
        if active_call:
            # Very aggressive cleanup for testing - cleanup calls older than 10 seconds
            cleanup_threshold = 10  # seconds
            if (active_call.status in ['initiated', 'ringing', 'active'] and 
                (timezone.now() - active_call.initiated_at).total_seconds() > cleanup_threshold):
                # Auto-cleanup stuck call
                print(f"[DEBUG] Cleaning up stuck call {active_call.id} - status: {active_call.status}, age: {(timezone.now() - active_call.initiated_at).total_seconds()}s")
                active_call.status = 'failed'
                active_call.ended_at = timezone.now()
                active_call.save()
                
                # Create call event for the cleanup
                CallEvent.objects.create(
                    call=active_call,
                    event_type='call_failed',
                    user=request.user,
                    event_data={'reason': 'timeout_cleanup'}
                )
            else:
                print(f"[DEBUG] Active call found - ID: {active_call.id}, status: {active_call.status}, age: {(timezone.now() - active_call.initiated_at).total_seconds()}s")
                return Response({'error': 'Call already in progress'}, 
                              status=status.HTTP_409_CONFLICT)
        
        with transaction.atomic():
            # Create call record
            call = Call.objects.create(
                conversation=conversation,
                caller=request.user,
                callee=callee,
                call_type=call_type,
                session_id=f"call_{conversation.id}_{int(timezone.now().timestamp())}"
            )
            
            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_initiated',
                user=request.user,
                event_data={'call_type': call_type}
            )
            
            # Notify callee via WebSocket using Pydantic schema for camelCase
            socket_service = SocketService()
            
            # Use Pydantic schema for camelCase serialization
            caller_data = UserBasic(
                id=request.user.id,
                username=request.user.username,
                first_name=request.user.first_name,
                last_name=request.user.last_name
            )
            
            notification = IncomingCallNotification(
                call_id=call.id,
                conversation_id=conversation.id,
                caller=caller_data,
                call_type=call_type,
                timestamp=call.initiated_at
            )
            
            # Debug logging
            print(f"[DEBUG] Initiating call - Call ID: {call.id} (type: {type(call.id)})")
            print(f"[DEBUG] Conversation ID: {conversation.id} (type: {type(conversation.id)})")
            print(f"[DEBUG] Notification data: {notification.model_dump(by_alias=True)}")
            
            socket_service.emit_to_user(
                user_id=str(callee.id),
                event='incoming_call',
                data=notification.model_dump(by_alias=True, mode='json')
            )
        
        # Return the created call using Pydantic schema for camelCase
        caller_data = UserBasic(
            id=call.caller.id,
            username=call.caller.username,
            first_name=call.caller.first_name,
            last_name=call.caller.last_name
        )
        
        callee_data = UserBasic(
            id=call.callee.id,
            username=call.callee.username,
            first_name=call.callee.first_name,
            last_name=call.callee.last_name
        )
        
        call_response = CallResponse(
            id=call.id,
            conversation=call.conversation.id,
            caller=caller_data,
            callee=callee_data,
            call_type=call.call_type,
            status=call.status,
            initiated_at=call.initiated_at,
            answered_at=call.answered_at,
            ended_at=call.ended_at,
            duration=str(call.duration) if call.duration else None,
            session_id=call.session_id
        )
        
        return Response(call_response.model_dump(by_alias=True), status=status.HTTP_201_CREATED)
        
    except Conversation.DoesNotExist:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def answer_call(request, call_id):
    """Answer an incoming call"""
    try:
        # First check if call exists and user is the callee
        call = Call.objects.get(id=call_id, callee=request.user)
        
        # Check if call can be answered
        if call.status not in ['initiated', 'ringing']:
            return Response(
                {'error': f'Call cannot be answered - current status: {call.status}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        with transaction.atomic():
            call.status = 'answered'
            call.answered_at = timezone.now()
            call.save()
            
            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_answered',
                user=request.user
            )
            
            # Notify caller via WebSocket using camelCase
            socket_service = SocketService()
            
            # Use Pydantic schema for camelCase serialization
            callee_data = UserBasic(
                id=request.user.id,
                username=request.user.username,
                first_name=request.user.first_name,
                last_name=request.user.last_name
            )
            
            notification = CallStatusNotification(
                call_id=call.id,
                status='answered',
                user=callee_data,
                timestamp=call.answered_at
            )
            
            socket_service.emit_to_user(
                user_id=str(call.caller.id),
                event='call_answered',
                data=notification.model_dump(by_alias=True, mode='json')
            )
        
        return Response(CallSerializer(call).data)
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found or you are not authorized to answer this call'},
                       status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def decline_call(request, call_id):
    """Decline an incoming call"""
    try:
        # Allow declining calls in various states (initiated, ringing, or even answered if not yet active)
        call = Call.objects.get(
            id=call_id, 
            callee=request.user, 
            status__in=['initiated', 'ringing', 'answered']
        )

        with transaction.atomic():
            call.status = 'declined'
            call.ended_at = timezone.now()
            call.save()

            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_declined',
                user=request.user
            )

            # Notify caller via WebSocket using camelCase
            socket_service = SocketService()
            
            # Use Pydantic schema for camelCase serialization
            decliner_data = UserBasic(
                id=request.user.id,
                username=request.user.username,
                first_name=request.user.first_name,
                last_name=request.user.last_name
            )
            
            notification = CallStatusNotification(
                call_id=call.id,
                status='declined',
                user=decliner_data,
                timestamp=call.ended_at
            )
            
            socket_service.emit_to_user(
                user_id=str(call.caller.id),
                event='call_declined',
                data=notification.model_dump(by_alias=True, mode='json')
            )

        return Response({'message': 'Call declined'})

    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def end_call(request, call_id):
    """End an active call"""
    try:
        # Allow ending calls in various states (not just answered/active)
        call = Call.objects.get(
            id=call_id,
            status__in=['initiated', 'ringing', 'answered', 'active']
        )

        # Check if user is participant
        if request.user not in [call.caller, call.callee]:
            return Response({'error': 'Not a participant in this call'},
                          status=status.HTTP_403_FORBIDDEN)

        with transaction.atomic():
            call.status = 'ended'
            call.ended_at = timezone.now()
            call.save()

            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_ended',
                user=request.user
            )

            # Notify other participant via WebSocket using camelCase
            other_user = call.callee if request.user == call.caller else call.caller
            socket_service = SocketService()
            
            # Use Pydantic schema for camelCase serialization
            ender_data = UserBasic(
                id=request.user.id,
                username=request.user.username,
                first_name=request.user.first_name,
                last_name=request.user.last_name
            )
            
            notification = CallStatusNotification(
                call_id=call.id,
                status='ended',
                user=ender_data,
                timestamp=call.ended_at
            )
            
            socket_service.emit_to_user(
                user_id=str(other_user.id),
                event='call_ended',
                data=notification.model_dump(by_alias=True, mode='json')
            )

        return Response({'message': 'Call ended'})

    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_call_sdp(request, call_id):
    """Update SDP offer/answer for WebRTC negotiation"""
    try:
        # Debug logging
        print(f"[DEBUG] update_call_sdp - Received call_id: {call_id} (type: {type(call_id)})")
        print(f"[DEBUG] update_call_sdp - Request data: {request.data}")
        
        call = Call.objects.get(id=call_id)

        # Check if user is participant
        if request.user not in [call.caller, call.callee]:
            return Response({'error': 'Not a participant in this call'},
                          status=status.HTTP_403_FORBIDDEN)

        serializer = CallSDPUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        sdp_type = serializer.validated_data['type']
        sdp_data = serializer.validated_data['sdp']

        # Update appropriate SDP field
        if request.user == call.caller:
            call.caller_sdp = sdp_data
        else:
            call.callee_sdp = sdp_data

        call.save()

        # Notify other participant via WebSocket
        other_user = call.callee if request.user == call.caller else call.caller
        socket_service = SocketService()
        socket_service.emit_to_user(
            user_id=str(other_user.id),
            event='webrtc_sdp_update',
            data={
                'call_id': str(call.id),
                'type': sdp_type,
                'sdp': sdp_data,
                'from': str(request.user.id)
            }
        )

        return Response({'message': 'SDP updated'})

    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def report_call_quality(request, call_id):
    """Report call quality metrics"""
    try:
        call = Call.objects.get(id=call_id)

        # Check if user is participant
        if request.user not in [call.caller, call.callee]:
            return Response({'error': 'Not a participant in this call'},
                          status=status.HTTP_403_FORBIDDEN)

        serializer = CallQualityReportSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Create quality metric record
        CallQualityMetric.objects.create(
            call=call,
            user=request.user,
            **serializer.validated_data
        )

        return Response({'message': 'Quality metrics recorded'})

    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_call_history(request, conversation_id):
    """
    Get call history for a conversation
    """
    try:
        conversation = get_object_or_404(Conversation, id=conversation_id)
        
        # Verify user is a participant
        if not conversation.participants.filter(user=request.user).exists():
            return Response(
                {'error': 'You are not a participant in this conversation'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get calls for this conversation
        calls = Call.objects.filter(conversation=conversation).order_by('-initiated_at')
        
        # Serialize using Pydantic schemas for camelCase
        call_responses = []
        for call in calls:
            call_response = CallHistoryResponse(
                id=call.id,
                conversation_id=call.conversation.id,
                caller_id=call.caller.id,
                callee_id=call.callee.id if call.callee else None,
                call_type=call.call_type,
                status=call.status,
                initiated_at=call.initiated_at,
                answered_at=call.answered_at,
                ended_at=call.ended_at,
                duration=call.duration
            )
            call_responses.append(call_response.model_dump(by_alias=True))
        
        return Response(call_responses, status=status.HTTP_200_OK)
        
    except Conversation.DoesNotExist:
        return Response(
            {'error': 'Conversation not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_call_detail(request, call_id):
    """
    Get detailed information about a specific call
    """
    try:
        call = get_object_or_404(Call, id=call_id)
        
        # Verify user is involved in this call
        if call.caller != request.user and call.callee != request.user:
            return Response(
                {'error': 'You are not authorized to view this call'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get call events
        events = CallEvent.objects.filter(call=call).order_by('timestamp')
        
        # Get quality metrics if available
        quality_metrics = CallQualityMetric.objects.filter(call=call)
        
        # Serialize using Pydantic schemas for camelCase
        call_response = CallHistoryResponse(
            id=call.id,
            conversation_id=call.conversation.id,
            caller_id=call.caller.id,
            callee_id=call.callee.id if call.callee else None,
            call_type=call.call_type,
            status=call.status,
            initiated_at=call.initiated_at,
            answered_at=call.answered_at,
            ended_at=call.ended_at,
            duration=call.duration
        )
        
        # For now, keep events and quality metrics as they are (can be updated later if needed)
        events_data = CallEventSerializer(events, many=True).data
        quality_data = CallQualityMetricSerializer(quality_metrics, many=True).data
        
        return Response({
            'call': call_response.model_dump(by_alias=True),
            'events': events_data,
            'qualityMetrics': quality_data  # camelCase field name
        }, status=status.HTTP_200_OK)
        
    except (Call.DoesNotExist, Http404):
        return Response(
            {'error': 'Call not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
