# Message Slice Reducers Documentation

This document provides a comprehensive overview of all reducer methods in the `messageSlice.ts` file, including their usage locations, payload types, and functionality.

## Overview

The message slice contains 15 reducer methods that handle various aspects of message management in the chat application, including optimistic updates, typing indicators, message status tracking, and error handling.

## Reducer Methods Table

| # | Reducer Name | Payload Type | Primary Usage Files | Functionality |
|---|--------------|--------------|-------------------|---------------|
| 1 | `addMessage` | `Message & { tempId?: string }` | `SocketContext.tsx`, `MediaUpload.tsx`, `messageSlice.test.ts` | Adds a new message to the conversation. Handles replacing optimistic messages with real messages when `tempId` is provided. Prevents duplicate messages and maintains chronological order. |
| 2 | `addOptimisticMessage` | `{ tempId: string; message: Message }` | `SocketContext.tsx`, `messageSlice.test.ts` | Creates an optimistic message with a temporary ID for immediate UI feedback while the real message is being sent to the server. Sets sending status to true. |
| 3 | `removeOptimisticMessage` | `string` (tempId) | `messageSlice.test.ts` | Removes an optimistic message by its temporary ID. Cleans up sending status and optimistic message mapping. Used when message sending fails. |
| 4 | `updateOptimisticMessage` | `{ tempId: string; realMessage: Message }` | `messageSlice.test.ts` | Replaces an optimistic message with the real message received from the server. Updates the optimistic message mapping and clears sending status. |
| 5 | `setTypingUsers` | `{ conversationId: string; userIds: string[] }` | `messageSlice.test.ts` | Sets the complete list of users currently typing in a specific conversation. Replaces any existing typing users for that conversation. |
| 6 | `addTypingUser` | `{ conversationId: string; userId: string }` | `SocketContext.tsx`, `messageSlice.test.ts` | Adds a user to the typing indicators for a conversation. Prevents duplicate entries and initializes the array if it doesn't exist. |
| 7 | `removeTypingUser` | `{ conversationId: string; userId: string }` | `SocketContext.tsx`, `messageSlice.test.ts` | Removes a user from the typing indicators for a conversation. Used when a user stops typing or sends a message. |
| 8 | `setSendingMessage` | `string` (tempId) | `messageSlice.test.ts` | Marks a message as currently being sent using its temporary ID. Used to show loading states in the UI. |
| 9 | `removeSendingMessage` | `string` (tempId) | `messageSlice.test.ts` | Removes the sending status for a message by its temporary ID. Called when message sending completes (success or failure). |
| 10 | `clearMessages` | `string` (conversationId) | `messageSlice.test.ts` | Clears all messages for a specific conversation. Used for conversation cleanup or when leaving a conversation. |
| 11 | `setError` | `string \| null` | `SocketContext.tsx` (as `setMessageError`) | Sets or clears the global error state for the message slice. Used for displaying error messages to users. |
| 12 | `updateMessageStatus` | `{ messageId: string; status: MessageStatusType; tempId?: string }` | `SocketContext.tsx`, `messageApi.ts` | Updates the delivery/read status of a message. Handles status transitions (SENT → DELIVERED → READ) and manages failed message tracking. |
| 13 | `markMessageAsFailed` | `{ messageId?: string; tempId?: string }` | `SocketContext.tsx` | Marks a message as failed to send or deliver. Updates both message status and failed messages tracking. Clears sending status if tempId provided. |
| 14 | `retryMessage` | `{ messageId: string; tempId?: string }` | `SocketContext.tsx` | Resets a failed message for retry. Removes from failed messages, resets status to SENT, and optionally sets sending status. |
| 15 | `clearMessageError` | `string` (messageId) | `messageSlice.test.ts` | Clears error state for a specific message. Removes from both failed messages and message statuses tracking. |

## Payload Type Definitions

### Core Types Used

```typescript
// Message status types
type MessageStatusType = "SENT" | "DELIVERED" | "READ" | "FAILED";

// Main message interface
interface Message {
  id: string;
  conversationId: string;
  sender: {
    id: string;
    name: string;
    profilePicture?: string | null;
  };
  content: string;
  messageType: "TEXT" | "IMAGE" | "FILE" | "SYSTEM";
  createdAt: string;
  updatedAt: string;
  tempId?: string;
  isOptimistic?: boolean;
  status?: MessageStatusType;
  statuses?: MessageStatus[];
  // Encryption fields
  encryptedContent?: string;
  iv?: string;
  senderRatchetKey?: string;
  messageNumber?: number;
  previousChainLength?: number;
  isEncrypted?: boolean;
}
```

## Usage Patterns

### Real-time Message Flow
1. **Optimistic Update**: `addOptimisticMessage` → `setSendingMessage`
2. **Success**: `addMessage` (with tempId) → `removeSendingMessage`
3. **Failure**: `markMessageAsFailed` → `removeSendingMessage`
4. **Retry**: `retryMessage` → `setSendingMessage`

### Typing Indicators
1. **Start Typing**: `addTypingUser`
2. **Stop Typing**: `removeTypingUser`
3. **Bulk Update**: `setTypingUsers`

### Message Status Tracking
1. **Status Updates**: `updateMessageStatus` (SENT → DELIVERED → READ)
2. **Failure Handling**: `markMessageAsFailed`
3. **Error Cleanup**: `clearMessageError`

## Key Files Using These Reducers

### Primary Implementation Files
- **`SocketContext.tsx`**: Main real-time message handling, typing indicators, status updates
- **`messageSlice.test.ts`**: Comprehensive test coverage for all reducers
- **`messageApi.ts`**: RTK Query integration for message status updates

### Secondary Usage Files
- **`MediaUpload.tsx`**: File upload message handling
- **`cacheUtils.ts`**: Cache management utilities
- **Various test files**: Unit and integration tests

## State Management Architecture

The message slice follows these patterns:
- **Optimistic Updates**: Immediate UI feedback with server reconciliation
- **Status Tracking**: Comprehensive message delivery status management
- **Error Handling**: Graceful failure recovery with retry mechanisms
- **Real-time Sync**: Socket-based real-time updates with cache synchronization

## Notes

- All reducers use Immer for immutable state updates via Redux Toolkit
- Message ordering is maintained chronologically using `sortMessagesByDate` helper
- Optimistic messages use temporary IDs that get replaced with server-generated IDs
- The slice integrates with RTK Query for server synchronization
- Comprehensive error handling ensures robust message delivery tracking