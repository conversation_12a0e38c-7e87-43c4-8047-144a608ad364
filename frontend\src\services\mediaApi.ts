// frontend/src/services/mediaApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';

// Get Socket Server URL from environment
const getSocketServerUrl = () => {
  return import.meta.env.VITE_SOCKET_URL || 'http://localhost:7000';
};

// Custom base query for Socket Server with authentication
const socketServerBaseQuery: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const baseQuery = fetchBaseQuery({
    baseUrl: `${getSocketServerUrl()}/api/media`,
    prepareHeaders: (headers) => {
      const token = localStorage.getItem('token');
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      
      // Add X-User-Id header as required by the new API
      if (user && user.id) {
        headers.set('X-User-Id', user.id);
      }
      
      // Don't set content-type for FormData - browser will set it with boundary
      return headers;
    },
    timeout: 300000, // 5 minutes for large file uploads
  });

  return await baseQuery(args, api, extraOptions);
};

// Type definitions for Socket Server API

export interface StartChunkedUploadRequest {
  conversationId: string;
  originalFilename: string;
  fileType: 'image' | 'document' | 'audio' | 'video' | 'archive' | 'other';
  mimeType: string;
  fileSize: number;
  wrappedFileKey: string;
  fileNonce: string;
  thumbnailNonce?: string;
}

export interface StartChunkedUploadResponse {
  success: boolean;
  uploadSession: string;
  mediaFileId: string;
}

export interface ChunkUploadRequest {
  chunk: Blob;
  uploadSession: string;
  chunkNumber: number;
  totalChunks: number;
  chunkHash: string;
  mediaFileId?: string;
  conversationId?: string;
  tempId?: string;
}

export interface ChunkUploadResponse {
  success: boolean;
  isComplete: boolean;
  message: string;
}

export interface DownloadTokenRequest {
  maxDownloads?: number;
}

export interface DownloadTokenResponse {
  downloadToken: string;
  expiresAt: string;
  downloadUrl: string;
}

export interface MediaFileInfo {
  id: string;
  originalFilename: string;
  fileType: string;
  mimeType: string;
  fileSize: number;
  processingStatus: string;
  virusScanStatus: string;
  createdAt: string;
  hasThumbnail: boolean;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

// Legacy types for backward compatibility
export interface MediaFile {
  id: string;
  message: string;
  uploader: {
    id: string;
    username: string;
    first_name: string;
    last_name: string;
  };
  original_filename: string;
  file_type: 'image' | 'document' | 'audio' | 'video' | 'archive' | 'other';
  mime_type: string;
  file_size: number;
  wrapped_file_key: string;
  file_nonce: string;
  thumbnail_nonce?: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  virus_scan_status: string;
  created_at: string;
  updated_at: string;
  file_extension: string;
  is_image: boolean;
  is_video: boolean;
  has_thumbnail: boolean;
}

// RTK Query API for Socket Server media endpoints
export const mediaApi = createApi({
  reducerPath: 'mediaApi',
  baseQuery: socketServerBaseQuery,
  tagTypes: ['Media'],
  endpoints: (builder) => ({
    // Start a chunked upload
    startChunkedUpload: builder.mutation<StartChunkedUploadResponse, StartChunkedUploadRequest>({
      query: (uploadData) => ({
        url: '/upload/chunked/start',
        method: 'POST',
        body: uploadData,
      }),
    }),

    // Upload a single chunk
    uploadChunk: builder.mutation<ChunkUploadResponse, ChunkUploadRequest>({
      query: ({ chunk, uploadSession, chunkNumber, totalChunks, chunkHash, mediaFileId, conversationId, tempId }) => {
        const formData = new FormData();
        formData.append('chunk', chunk);
        formData.append('uploadSession', uploadSession);
        formData.append('chunkNumber', chunkNumber.toString());
        formData.append('totalChunks', totalChunks.toString());
        formData.append('chunkHash', chunkHash);
        
        if (mediaFileId) {
          formData.append('mediaFileId', mediaFileId);
        }
        if (conversationId) {
          formData.append('conversationId', conversationId);
        }
        if (tempId) {
          formData.append('tempId', tempId);
        }

        return {
          url: '/upload/chunked/chunk',
          method: 'POST',
          body: formData,
        };
      },
    }),

    // Create download token
    createDownloadToken: builder.mutation<DownloadTokenResponse, { mediaFileId: string; maxDownloads?: number }>({
      query: ({ mediaFileId, maxDownloads = 5 }) => ({
        url: `/download/token/${mediaFileId}`,
        method: 'POST',
        body: { maxDownloads },
      }),
    }),

    // Get media file info
    getMediaInfo: builder.query<MediaFileInfo, string>({
      query: (mediaFileId) => `/info/${mediaFileId}`,
      providesTags: (result, error, mediaFileId) => [{ type: 'Media', id: mediaFileId }],
    }),

    // Health check
    healthCheck: builder.query<{ success: boolean; service: string; timestamp: string }, void>({
      query: () => '/health',
    }),
  }),
});

// Export hooks for usage in components
export const {
  useStartChunkedUploadMutation,
  useUploadChunkMutation,
  useCreateDownloadTokenMutation,
  useGetMediaInfoQuery,
  useHealthCheckQuery,
} = mediaApi;

// Helper class for utility functions (non-API operations)
export class MediaApiService {
  /**
   * Determine if file should use chunked upload
   */
  shouldUseChunkedUpload(file: File): boolean {
    // Always use chunked upload for consistency with new implementation
    return true;
  }

  /**
   * Get MIME type from filename extension
   */
  getMimeTypeFromFileName(filename: string): string {
    const extension = filename.toLowerCase().split('.').pop();

    const mimeTypes: Record<string, string> = {
      // Images
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',

      // Videos
      'mp4': 'video/mp4',
      'webm': 'video/webm',
      'mov': 'video/quicktime',
      'avi': 'video/x-msvideo',

      // Audio
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'ogg': 'audio/ogg',

      // Documents
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain',
      'rtf': 'application/rtf',

      // Archives
      'zip': 'application/zip',
      'rar': 'application/x-rar-compressed',
      '7z': 'application/x-7z-compressed',
    };

    return mimeTypes[extension || ''] || 'application/octet-stream';
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    // Maximum file size is 25MB for Socket Server
    const MAX_FILE_SIZE = 25 * 1024 * 1024;

    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File too large. Maximum size is ${this.formatFileSize(MAX_FILE_SIZE)}`
      };
    }

    // Check dangerous extensions
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.msi'];
    const extension = file.name.toLowerCase().split('.').pop();
    if (extension && dangerousExtensions.includes(`.${extension}`)) {
      return {
        valid: false,
        error: 'File type not allowed for security reasons'
      };
    }

    return { valid: true };
  }

  /**
   * Get file type from MIME type
   */
  getFileType(mimeType: string): 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other' {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('text/') ||
        ['application/pdf', 'application/msword',
         'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(mimeType)) {
      return 'document';
    }
    if (['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'].includes(mimeType)) {
      return 'archive';
    }
    return 'other';
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Export utility service instance
export const mediaApiService = new MediaApiService();
