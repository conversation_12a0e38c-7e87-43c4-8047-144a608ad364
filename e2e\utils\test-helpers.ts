import { Page, expect } from '@playwright/test';

export const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpass123'
};

export const BASE_URL = 'http://localhost:5000';

/**
 * Login helper function
 */
export async function loginUser(page: Page, email: string = TEST_CREDENTIALS.email, password: string = TEST_CREDENTIALS.password) {
  console.log(`🔐 Logging in user: ${email}`);
  
  await page.goto(BASE_URL);
  await page.waitForLoadState('networkidle');
  
  // Fill credentials
  await page.fill('[data-testid="email-input"]', email);
  await page.fill('[data-testid="password-input"]', password);
  
  // Submit login
  await page.click('[data-testid="login-button"]');
  
  // Wait for dashboard
  await page.waitForURL('**/dashboard', { timeout: 10000 });
  
  console.log('✅ Login successful');
}

/**
 * Wait for socket connection
 */
export async function waitForSocketConnection(page: Page, timeout: number = 5000) {
  console.log('🔌 Waiting for socket connection...');
  
  await page.waitForFunction(() => {
    const statusElement = document.querySelector('[data-testid="connection-status"]');
    return statusElement && statusElement.textContent?.includes('Connected');
  }, { timeout });
  
  console.log('✅ Socket connected');
}

/**
 * Create or select a conversation
 */
export async function ensureConversationExists(page: Page): Promise<boolean> {
  console.log('💬 Ensuring conversation exists...');
  
  // Wait for conversations to load
  await page.waitForTimeout(2000);
  
  // Check for existing conversations
  const existingConversations = await page.locator('[data-testid="conversation-item"]').count();
  
  if (existingConversations > 0) {
    console.log(`📱 Found ${existingConversations} existing conversations, selecting first one`);
    await page.locator('[data-testid="conversation-item"]').first().click();
    await page.waitForTimeout(1000);
    return true;
  }
  
  // Try to create a new conversation
  console.log('📱 No existing conversations, attempting to create new one...');
  await page.click('[data-testid="new-chat-button"]');
  
  // Wait for user search modal
  const userSearchModal = page.locator('[data-testid="user-search-modal"]');
  if (await userSearchModal.isVisible({ timeout: 3000 })) {
    // Search for users
    const searchInput = page.locator('[data-testid="user-search-input"]');
    await searchInput.fill('test');
    await page.waitForTimeout(1000);
    
    // Select first user if available
    const firstUser = page.locator('[data-testid="user-search-result"]').first();
    if (await firstUser.isVisible({ timeout: 2000 })) {
      await firstUser.click();
      await page.waitForTimeout(2000);
      console.log('✅ New conversation created');
      return true;
    }
  }
  
  console.log('⚠️ Could not create conversation - no users available');
  return false;
}

/**
 * Send a message and wait for it to appear
 */
export async function sendMessage(page: Page, message: string): Promise<boolean> {
  console.log(`📤 Sending message: "${message}"`);
  
  // Verify message input is available
  const messageInput = page.locator('[data-testid="message-input"]');
  const sendButton = page.locator('[data-testid="send-button"]');
  
  if (!(await messageInput.isVisible()) || !(await sendButton.isVisible())) {
    console.error('❌ Message input or send button not visible');
    return false;
  }
  
  // Get initial message count
  const initialCount = await page.locator('[data-testid="message"]').count();
  
  // Send message
  await messageInput.fill(message);
  await sendButton.click();
  
  // Wait for message to appear
  await page.waitForFunction(
    (expectedCount) => {
      const messages = document.querySelectorAll('[data-testid="message"]');
      return messages.length > expectedCount;
    },
    initialCount,
    { timeout: 5000 }
  );
  
  console.log('✅ Message sent and appeared in UI');
  return true;
}

/**
 * Verify message exists in conversation
 */
export async function verifyMessageExists(page: Page, messageText: string): Promise<boolean> {
  console.log(`🔍 Verifying message exists: "${messageText}"`);
  
  const messageElements = await page.locator('[data-testid="message"]').all();
  
  for (const messageEl of messageElements) {
    const content = await messageEl.textContent();
    if (content && content.includes(messageText)) {
      console.log('✅ Message found in conversation');
      return true;
    }
  }
  
  console.log('❌ Message not found in conversation');
  return false;
}

/**
 * Setup debugging for a page
 */
export function setupPageDebugging(page: Page) {
  // Console logging
  page.on('console', (msg) => {
    console.log(`[BROWSER ${msg.type().toUpperCase()}] ${msg.text()}`);
  });

  // Error logging
  page.on('pageerror', (error) => {
    console.error(`[PAGE ERROR] ${error.message}`);
  });

  // Network request logging (optional)
  page.on('request', (request) => {
    if (request.url().includes('/api/')) {
      console.log(`[REQUEST] ${request.method()} ${request.url()}`);
    }
  });

  page.on('response', (response) => {
    if (response.url().includes('/api/') && !response.ok()) {
      console.error(`[RESPONSE ERROR] ${response.status()} ${response.url()}`);
    }
  });
}

/**
 * Wait for element with retry logic
 */
export async function waitForElementWithRetry(
  page: Page, 
  selector: string, 
  options: { timeout?: number; retries?: number } = {}
): Promise<boolean> {
  const { timeout = 5000, retries = 3 } = options;
  
  for (let i = 0; i < retries; i++) {
    try {
      await page.waitForSelector(selector, { timeout });
      return true;
    } catch (error) {
      console.log(`⚠️ Attempt ${i + 1}/${retries} failed for selector: ${selector}`);
      if (i === retries - 1) {
        console.error(`❌ Failed to find element after ${retries} attempts: ${selector}`);
        return false;
      }
      await page.waitForTimeout(1000);
    }
  }
  
  return false;
}

/**
 * Take screenshot for debugging
 */
export async function takeDebugScreenshot(page: Page, name: string) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `debug-${name}-${timestamp}.png`;
  await page.screenshot({ path: `e2e/debug-screenshots/${filename}`, fullPage: true });
  console.log(`📸 Debug screenshot saved: ${filename}`);
}
