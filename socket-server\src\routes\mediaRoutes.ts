// socket-server/src/routes/mediaRoutes.ts
import { Router, Request, Response } from 'express';
import { Server } from 'socket.io';
import multer from 'multer';
import { MediaService } from '../services/mediaService';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { authenticateHttpRequest, AuthenticatedRequest } from '../middleware/authHttp';

const prisma = new PrismaClient();
const mediaService = new MediaService(prisma);

// Configure multer for chunk uploads
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB per chunk
    },
});

/**
 * Create media routes with Socket.IO instance for real-time events
 */
export const createMediaRoutes = (io: Server): Router => {
    const router: Router = Router();

    // Validation schemas
    // Note: uploaderId is NOT in the schema - it's extracted from JW<PERSON> token by authenticateHttpRequest middleware
    const ChunkedUploadStartSchema = z.object({
        conversationId: z.string().uuid(),
        originalFilename: z.string().min(1).max(255),
        fileType: z.enum(['image', 'document', 'audio', 'video', 'archive', 'other']),
        mimeType: z.string().min(1).max(100),
        fileSize: z.number().positive().max(25 * 1024 * 1024), // 25MB max
        wrappedFileKey: z.string().min(1),
        fileNonce: z.string().min(1),
        thumbnailNonce: z.string().optional(),
    });

    const ChunkUploadSchema = z.object({
        uploadSession: z.string().uuid(),
        chunkNumber: z.string().transform((val) => parseInt(val, 10)).pipe(z.number().int().min(0)),
        totalChunks: z.string().transform((val) => parseInt(val, 10)).pipe(z.number().int().min(1)),
        chunkHash: z.string().length(64), // SHA256 hash
        mediaFileId: z.string().uuid().optional(), // Media file ID to link chunks to the correct file
        conversationId: z.string().uuid().optional(), // For socket emission on completion
        tempId: z.string().optional(), // For frontend correlation
    });

    // Use the JWT authentication middleware from authHttp.ts
    // This middleware validates JWT tokens and attaches userId and user to the request

    /**
     * POST /api/media/upload/chunked/start
     * Start a chunked upload session
     */
    router.post('/upload/chunked/start', authenticateHttpRequest, async (req: Request, res: Response) => {
    try {
        const authReq = req as AuthenticatedRequest;
        const validatedData = ChunkedUploadStartSchema.parse({
            ...req.body,
            uploaderId: authReq.userId,
        });

        const result = await mediaService.startChunkedUpload({
            ...validatedData,
            uploaderId: authReq.userId,
        });

        res.json({
            success: true,
            uploadSession: result.uploadSession,
            mediaFileId: result.mediaFileId,
        });
    } catch (error) {
        console.error('Error starting chunked upload:', error);

        if (error instanceof z.ZodError) {
            // Check if the error is related to file size limit
            const fileSizeError = error.issues.find(issue => 
                issue.path.includes('fileSize') && issue.code === 'too_big'
            );
            
            if (fileSizeError) {
                return res.status(413).json({
                    error: 'Payload Too Large',
                    message: 'File size exceeds the maximum limit of 25MB',
                });
            }
            
            return res.status(400).json({
                error: 'Validation failed',
                details: error.issues.map(issue => ({
                    field: issue.path.join('.'),
                    message: issue.message,
                })),
            });
        }

        res.status(500).json({
            error: error instanceof Error ? error.message : 'Failed to start upload',
        });
    }
});

    /**
     * POST /api/media/upload/chunked/chunk
     * Upload a file chunk
     */
    router.post('/upload/chunked/chunk', authenticateHttpRequest, upload.single('chunk'), async (req: Request, res: Response) => {
        try {
            const authReq = req as AuthenticatedRequest;

            if (!req.file) {
                return res.status(400).json({ error: 'No chunk data provided' });
            }

            const validatedData = ChunkUploadSchema.parse(req.body);

            const result = await mediaService.uploadChunk({
                ...validatedData,
                chunkData: req.file.buffer,
            }, validatedData.mediaFileId);

            // If upload is complete, emit socket event to conversation room
            if (result.isComplete && validatedData.mediaFileId) {
                try {
                    // Get media file to find conversation ID
                    const mediaFile = await prisma.media_files.findUnique({
                        where: { id: validatedData.mediaFileId },
                        select: {
                            id: true,
                            original_filename: true,
                            file_size: true,
                            file_type: true,
                            mime_type: true,
                        }
                    });

                    if (mediaFile) {
                        // Get conversationId from validated data (dynamically provided by frontend)
                        const conversationId = validatedData.conversationId;

                        if (conversationId) {
                            const room = `conversation_${conversationId}`;

                            // Emit media upload completed event
                            io.to(room).emit('media_upload_completed', {
                                conversationId,
                                mediaFileId: validatedData.mediaFileId,
                                uploaderId: authReq.userId,
                                fileName: mediaFile.original_filename,
                                fileSize: Number(mediaFile.file_size),
                                fileType: mediaFile.file_type,
                                mimeType: mediaFile.mime_type,
                                tempId: validatedData.tempId, // For frontend correlation
                                timestamp: new Date().toISOString()
                            });

                            console.log(`✅ Media upload completed - emitted to room: ${room}`);
                        }
                    }
                } catch (socketError) {
                    // Log socket emission error but don't fail the upload
                    console.error('Error emitting socket event:', socketError);
                }
            }

            res.json({
                success: true,
                isComplete: result.isComplete,
                message: result.isComplete ? 'Upload completed' : 'Chunk uploaded successfully',
            });
        } catch (error) {
            console.error('Error uploading chunk:', error);

            if (error instanceof z.ZodError) {
                return res.status(400).json({
                    error: 'Validation failed',
                    details: error.issues.map(issue => ({
                        field: issue.path.join('.'),
                        message: issue.message,
                    })),
                });
            }

            res.status(500).json({
                error: error instanceof Error ? error.message : 'Failed to upload chunk',
            });
        }
    });

    /**
     * POST /api/media/download/token/:mediaFileId
     * Create a download token for a media file
     */
    router.post('/download/token/:mediaFileId', authenticateHttpRequest, async (req: Request, res: Response) => {
    try {
        const authReq = req as AuthenticatedRequest;
        const { mediaFileId } = req.params;
        const { maxDownloads = 5 } = req.body;
        const userId = authReq.userId;

        if (!mediaFileId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(mediaFileId)) {
            return res.status(400).json({ error: 'Invalid media file ID' });
        }

        const token = await mediaService.createDownloadToken(mediaFileId, userId, maxDownloads);

        res.json({
            success: true,
            downloadToken: token.downloadToken,
            expiresAt: token.expiresAt,
            maxDownloads: token.maxDownloads,
        });
    } catch (error) {
        console.error('Error creating download token:', error);
        res.status(500).json({
            error: error instanceof Error ? error.message : 'Failed to create download token',
        });
    }
});

    /**
     * GET /api/media/stream/:downloadToken
     * Stream media file using download token
     */
    router.get('/stream/:downloadToken', async (req: Request, res: Response) => {
    try {
        const { downloadToken } = req.params;
        const range = req.headers.range;
        const ipAddress = req.ip || req.connection.remoteAddress || '0.0.0.0';
        const userAgent = req.headers['user-agent'] || '';

        if (!downloadToken) {
            return res.status(400).json({ error: 'Missing download token' });
        }

        const mediaInfo = await mediaService.getMediaFileForStreaming(
            downloadToken,
            ipAddress,
            userAgent
        );

        const streamInfo = mediaService.createStreamingResponse(mediaInfo.filePath, range);

        // Set headers for streaming
        const headers: Record<string, string> = {
            'Content-Type': mediaInfo.mimeType,
            'Content-Length': streamInfo.contentLength.toString(),
            'Accept-Ranges': 'bytes',
            'Content-Disposition': `attachment; filename="${mediaInfo.fileName}"`,
        };

        if (range) {
            headers['Content-Range'] = `bytes ${streamInfo.start}-${streamInfo.end}/${streamInfo.total}`;
            res.status(206); // Partial Content
        } else {
            res.status(200);
        }

        res.set(headers);
        streamInfo.stream.pipe(res);

    } catch (error) {
        console.error('Error streaming media:', error);
        res.status(500).json({
            error: error instanceof Error ? error.message : 'Failed to stream media',
        });
    }
});

    /**
     * GET /api/media/info/:mediaFileId
     * Get media file information
     */
    router.get('/info/:mediaFileId', authenticateHttpRequest, async (req: Request, res: Response) => {
    try {
        const authReq = req as AuthenticatedRequest;
        const { mediaFileId } = req.params;
        const userId = authReq.userId;

        if (!mediaFileId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(mediaFileId)) {
            return res.status(400).json({ error: 'Invalid media file ID' });
        }

        const mediaInfo = await mediaService.getMediaFileInfo(mediaFileId, userId);

        res.json({
            success: true,
            mediaFile: mediaInfo,
        });
    } catch (error) {
        console.error('Error getting media info:', error);
        res.status(500).json({
            error: error instanceof Error ? error.message : 'Failed to get media info',
        });
    }
});

    /**
     * GET /api/media/health
     * Health check endpoint
     */
    router.get('/health', (req: Request, res: Response) => {
        res.json({
            success: true,
            service: 'Media Streaming API',
            timestamp: new Date().toISOString(),
        });
    });

    return router;
};

// Export default router without io for backward compatibility
export default createMediaRoutes;