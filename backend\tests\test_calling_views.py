# backend/tests/test_calling_views.py
import pytest
import json
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, MagicMock

from calling.models import Call, CallEvent, CallQualityMetric
from messaging.models import Conversation, ConversationParticipant
from tests.factories import (
    UserFactory, ConversationFactory, ConversationParticipantFactory,
    CallFactory, CallEventFactory, CallQualityMetricFactory
)

User = get_user_model()


@pytest.mark.django_db
class TestCallingViews:
    """Test calling endpoints"""

    def setup_method(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.other_user = UserFactory()
        
        # Create a direct conversation between users
        self.conversation = ConversationFactory(type='DIRECT')
        ConversationParticipantFactory(conversation=self.conversation, user=self.user)
        ConversationParticipantFactory(conversation=self.conversation, user=self.other_user)
        
        # Authenticate client
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    @patch('calling.views.SocketService')
    def test_initiate_call_success(self, mock_socket_service):
        """Test successful call initiation"""
        mock_socket_service.return_value.emit_to_user = MagicMock()
        
        data = {
            'conversation_id': str(self.conversation.id),
            'call_type': 'audio'
        }
        
        url = reverse('initiate_call')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['callType'] == 'audio'
        assert response.data['status'] == 'initiated'
        assert str(response.data['caller']['id']) == str(self.user.id)
        assert str(response.data['callee']['id']) == str(self.other_user.id)
        
        # Verify call was created in database
        call = Call.objects.get(id=response.data['id'])
        assert call.call_type == 'audio'
        assert call.caller == self.user
        assert call.callee == self.other_user
        
        # Verify socket notification was sent
        mock_socket_service.assert_called()
        mock_socket_service.return_value.emit_to_user.assert_called()

    def test_initiate_call_not_participant(self):
        """Test call initiation when user is not a participant"""
        other_conversation = ConversationFactory(type='DIRECT')
        # Don't add user as participant
        
        data = {
            'conversation_id': str(other_conversation.id),
            'call_type': 'audio'
        }
        
        url = reverse('initiate_call')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'Not a participant in this conversation' in response.data['error']

    def test_initiate_call_no_other_participant(self):
        """Test call initiation when no other participant exists"""
        solo_conversation = ConversationFactory(type='DIRECT')
        ConversationParticipantFactory(conversation=solo_conversation, user=self.user)
        # No other participant
        
        data = {
            'conversation_id': str(solo_conversation.id),
            'call_type': 'audio'
        }
        
        url = reverse('initiate_call')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'No other participant found' in response.data['error']

    def test_initiate_call_already_active(self):
        """Test call initiation when call is already active"""
        # Create an active call
        CallFactory(
            conversation=self.conversation,
            caller=self.user,
            callee=self.other_user,
            status='active'
        )
        
        data = {
            'conversation_id': str(self.conversation.id),
            'call_type': 'audio'
        }
        
        url = reverse('initiate_call')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_409_CONFLICT
        assert 'Call already in progress' in response.data['error']

    def test_initiate_call_invalid_data(self):
        """Test call initiation with invalid data"""
        data = {
            'conversation_id': 'invalid-uuid',
            'call_type': 'invalid_type'
        }
        
        url = reverse('initiate_call')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_initiate_call_unauthenticated(self):
        """Test call initiation without authentication"""
        self.client.credentials()  # Remove authentication
        
        data = {
            'conversation_id': str(self.conversation.id),
            'call_type': 'audio'
        }
        
        url = reverse('initiate_call')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @patch('calling.views.SocketService')
    def test_answer_call_success(self, mock_socket_service):
        """Test successful call answering"""
        mock_socket_service.return_value.emit_to_user = MagicMock()

        call = CallFactory(
            conversation=self.conversation,
            caller=self.other_user,
            callee=self.user,
            status='initiated'
        )

        url = reverse('answer_call', kwargs={'call_id': call.id})
        response = self.client.post(url)

        assert response.status_code == status.HTTP_200_OK

        # Verify call status was updated
        call.refresh_from_db()
        assert call.status == 'answered'
        assert call.answered_at is not None

        # Verify call event was created
        event = CallEvent.objects.filter(call=call, event_type='call_answered').first()
        assert event is not None
        assert event.user == self.user

        # Verify socket notification was sent
        mock_socket_service.assert_called()
        mock_socket_service.return_value.emit_to_user.assert_called()

    def test_answer_call_not_callee(self):
        """Test answering call when user is not the callee"""
        call = CallFactory(
            conversation=self.conversation,
            caller=self.user,  # User is caller, not callee
            callee=self.other_user,
            status='initiated'
        )

        url = reverse('answer_call', kwargs={'call_id': call.id})
        response = self.client.post(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert 'Call not found or you are not authorized' in response.data['error']

    def test_answer_call_wrong_status(self):
        """Test answering call with wrong status"""
        call = CallFactory(
            conversation=self.conversation,
            caller=self.other_user,
            callee=self.user,
            status='ended'  # Wrong status
        )

        url = reverse('answer_call', kwargs={'call_id': call.id})
        response = self.client.post(url)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'Call cannot be answered' in response.data['error']

    @patch('calling.views.SocketService')
    def test_decline_call_success(self, mock_socket_service):
        """Test successful call declining"""
        mock_socket_service.return_value.emit_to_user = MagicMock()
        
        call = CallFactory(
            conversation=self.conversation,
            caller=self.other_user,
            callee=self.user,
            status='initiated'
        )
        
        url = reverse('decline_call', kwargs={'call_id': call.id})
        response = self.client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'Call declined' in response.data['message']
        
        # Verify call status was updated
        call.refresh_from_db()
        assert call.status == 'declined'
        assert call.ended_at is not None
        
        # Verify call event was created
        event = CallEvent.objects.filter(call=call, event_type='call_declined').first()
        assert event is not None

    @patch('calling.views.SocketService')
    def test_end_call_success(self, mock_socket_service):
        """Test successful call ending"""
        mock_socket_service.return_value.emit_to_user = MagicMock()
        
        call = CallFactory(
            conversation=self.conversation,
            caller=self.user,
            callee=self.other_user,
            status='active'
        )
        
        url = reverse('end_call', kwargs={'call_id': call.id})
        response = self.client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'Call ended' in response.data['message']
        
        # Verify call status was updated
        call.refresh_from_db()
        assert call.status == 'ended'
        assert call.ended_at is not None

    def test_end_call_not_participant(self):
        """Test ending call when user is not a participant"""
        third_user = UserFactory()
        call = CallFactory(
            conversation=self.conversation,
            caller=self.other_user,
            callee=third_user,  # User is not involved
            status='active'
        )
        
        url = reverse('end_call', kwargs={'call_id': call.id})
        response = self.client.post(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'Not a participant in this call' in response.data['error']

    @patch('calling.views.SocketService')
    def test_update_call_sdp_success(self, mock_socket_service):
        """Test successful SDP update"""
        mock_socket_service.return_value.emit_to_user = MagicMock()
        
        call = CallFactory(
            conversation=self.conversation,
            caller=self.user,
            callee=self.other_user,
            status='answered'
        )
        
        data = {
            'type': 'offer',
            'sdp': 'sdp_offer_data'
        }
        
        url = reverse('update_call_sdp', kwargs={'call_id': call.id})
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'SDP updated' in response.data['message']
        
        # Verify SDP was stored
        call.refresh_from_db()
        assert call.caller_sdp == 'sdp_offer_data'
        
        # Verify socket notification was sent
        mock_socket_service.assert_called()
        mock_socket_service.return_value.emit_to_user.assert_called()

    def test_update_call_sdp_invalid_data(self):
        """Test SDP update with invalid data"""
        call = CallFactory(
            conversation=self.conversation,
            caller=self.user,
            callee=self.other_user,
            status='answered'
        )
        
        data = {
            'type': 'invalid_type',
            'sdp': ''
        }
        
        url = reverse('update_call_sdp', kwargs={'call_id': call.id})
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_report_call_quality_success(self):
        """Test successful call quality reporting"""
        call = CallFactory(
            conversation=self.conversation,
            caller=self.user,
            callee=self.other_user,
            status='active'
        )
        
        data = {
            'packet_loss': 2.5,
            'jitter': 15.0,
            'round_trip_time': 120.0,
            'audio_quality_score': 4.2
        }
        
        url = reverse('report_call_quality', kwargs={'call_id': call.id})
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'Quality metrics recorded' in response.data['message']
        
        # Verify quality metric was created
        metric = CallQualityMetric.objects.filter(call=call, user=self.user).first()
        assert metric is not None
        assert metric.packet_loss == 2.5
        assert metric.jitter == 15.0

    def test_report_call_quality_invalid_data(self):
        """Test call quality reporting with invalid data"""
        call = CallFactory(
            conversation=self.conversation,
            caller=self.user,
            callee=self.other_user,
            status='active'
        )
        
        data = {
            'packet_loss': 'invalid',
            'jitter': -1.0  # Invalid negative value
        }
        
        url = reverse('report_call_quality', kwargs={'call_id': call.id})
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_get_call_history_success(self):
        """Test successful call history retrieval"""
        # Create some calls
        call1 = CallFactory(conversation=self.conversation, caller=self.user, callee=self.other_user)
        call2 = CallFactory(conversation=self.conversation, caller=self.other_user, callee=self.user)
        
        url = reverse('get_call_history', kwargs={'conversation_id': self.conversation.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 2
        # Should be ordered by most recent first
        assert str(response.data[0]['id']) == str(call2.id)
        assert str(response.data[1]['id']) == str(call1.id)

    def test_get_call_history_not_participant(self):
        """Test call history when user is not a participant"""
        other_conversation = ConversationFactory()
        # Don't add user as participant
        
        url = reverse('get_call_history', kwargs={'conversation_id': other_conversation.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'You are not a participant in this conversation' in response.data['error']

    def test_get_call_detail_success(self):
        """Test successful call detail retrieval"""
        call = CallFactory(
            conversation=self.conversation,
            caller=self.user,
            callee=self.other_user
        )
        
        # Create some events and quality metrics
        CallEventFactory(call=call, user=self.user)
        CallQualityMetricFactory(call=call, user=self.user)
        
        url = reverse('get_call_detail', kwargs={'call_id': call.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'call' in response.data
        assert 'events' in response.data
        assert 'qualityMetrics' in response.data
        assert str(response.data['call']['id']) == str(call.id)

    def test_get_call_detail_not_authorized(self):
        """Test call detail when user is not authorized"""
        third_user = UserFactory()
        call = CallFactory(
            conversation=self.conversation,
            caller=self.other_user,
            callee=third_user  # User is not involved
        )
        
        url = reverse('get_call_detail', kwargs={'call_id': call.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'You are not authorized to view this call' in response.data['error']

    def test_get_call_detail_not_found(self):
        """Test call detail for non-existent call"""
        url = reverse('get_call_detail', kwargs={'call_id': '00000000-0000-0000-0000-000000000000'})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert 'Call not found' in response.data['error']

    @patch('utils.socket_service.SocketService')
    def test_call_cleanup_mechanism(self, mock_socket_service):
        """Test automatic cleanup of stuck calls"""
        from django.utils import timezone
        from datetime import timedelta
        
        # Create an old stuck call
        old_call = CallFactory(
            conversation=self.conversation,
            caller=self.other_user,
            callee=self.user,
            status='initiated'
        )
        # Manually set old timestamp
        old_call.initiated_at = timezone.now() - timedelta(seconds=15)
        old_call.save()
        
        data = {
            'conversation_id': str(self.conversation.id),
            'call_type': 'audio'
        }
        
        url = reverse('initiate_call')
        response = self.client.post(url, data, format='json')
        
        # Should succeed after cleaning up the old call
        assert response.status_code == status.HTTP_201_CREATED
        
        # Verify old call was cleaned up
        old_call.refresh_from_db()
        assert old_call.status == 'failed'

    def test_response_format_camel_case(self):
        """Test that responses use camelCase format"""
        call = CallFactory(
            conversation=self.conversation,
            caller=self.user,
            callee=self.other_user
        )
        
        url = reverse('get_call_detail', kwargs={'call_id': call.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        # Check camelCase fields
        call_data = response.data['call']
        assert 'callType' in call_data
        assert 'conversationId' in call_data
        assert 'callerId' in call_data
        assert 'calleeId' in call_data
