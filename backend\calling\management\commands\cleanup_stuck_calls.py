from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from calling.models import Call, CallEvent
from utils.socket_service import SocketService
from calling.schemas import CallStatusNotification


class Command(BaseCommand):
    help = 'Clean up stuck calls that have been in transitional states for too long'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleaned up without making changes',
        )
        parser.add_argument(
            '--initiating-timeout',
            type=int,
            default=30,
            help='Timeout in seconds for calls stuck in "initiated" state (default: 30)',
        )
        parser.add_argument(
            '--ringing-timeout',
            type=int,
            default=60,
            help='Timeout in seconds for calls stuck in "ringing" state (default: 60)',
        )
        parser.add_argument(
            '--answered-timeout',
            type=int,
            default=300,
            help='Timeout in seconds for calls stuck in "answered" state (default: 300)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        initiating_timeout = options['initiating_timeout']
        ringing_timeout = options['ringing_timeout']
        answered_timeout = options['answered_timeout']
        
        now = timezone.now()
        socket_service = SocketService()
        
        # Define timeout conditions for different states
        timeout_conditions = [
            {
                'status': 'initiated',
                'timeout_seconds': initiating_timeout,
                'reason': 'initiation_timeout',
                'message': 'Call initiation timeout - unable to reach recipient'
            },
            {
                'status': 'ringing',
                'timeout_seconds': ringing_timeout,
                'reason': 'ringing_timeout',
                'message': 'Call timeout - no response from recipient'
            },
            {
                'status': 'answered',
                'timeout_seconds': answered_timeout,
                'reason': 'connection_timeout',
                'message': 'Call connection timeout - failed to establish media connection'
            }
        ]
        
        total_cleaned = 0
        
        for condition in timeout_conditions:
            status_name = condition['status']
            timeout_seconds = condition['timeout_seconds']
            reason = condition['reason']
            message = condition['message']
            
            # Find stuck calls
            cutoff_time = now - timedelta(seconds=timeout_seconds)
            
            if status_name == 'initiated':
                stuck_calls = Call.objects.filter(
                    status=status_name,
                    initiated_at__lt=cutoff_time
                )
            elif status_name == 'ringing':
                # For ringing calls, check from when they started ringing
                # We'll use initiated_at as approximation since we don't have ringing_at field
                stuck_calls = Call.objects.filter(
                    status=status_name,
                    initiated_at__lt=cutoff_time
                )
            elif status_name == 'answered':
                stuck_calls = Call.objects.filter(
                    status=status_name,
                    answered_at__lt=cutoff_time
                )
            else:
                continue
            
            count = stuck_calls.count()
            if count > 0:
                self.stdout.write(
                    f"Found {count} calls stuck in '{status_name}' state for more than {timeout_seconds}s"
                )
                
                if not dry_run:
                    # Process each stuck call
                    for call in stuck_calls:
                        # Update call status
                        call.status = 'failed'
                        call.ended_at = now
                        call.save()
                        
                        # Create call event
                        CallEvent.objects.create(
                            call=call,
                            event_type='call_failed',
                            user=call.caller,  # Use caller as the event user
                            event_data={
                                'reason': reason,
                                'message': message,
                                'cleanup_type': 'automatic',
                                'original_status': status_name
                            }
                        )
                        
                        # Notify both participants via WebSocket
                        notification = CallStatusNotification(
                            call_id=str(call.id),  # Convert UUID to string
                            status='failed',
                            reason=message,
                            timestamp=now
                        )
                        
                        # Notify caller
                        socket_service.emit_to_user(
                            user_id=str(call.caller.id),
                            event='call_ended',
                            data=notification.model_dump(by_alias=True, mode='json')
                        )
                        
                        # Notify callee
                        socket_service.emit_to_user(
                            user_id=str(call.callee.id),
                            event='call_ended',
                            data=notification.model_dump(by_alias=True, mode='json')
                        )
                        
                        self.stdout.write(
                            f"  ✓ Cleaned up call {call.id} (was {status_name} since {call.initiated_at})"
                        )
                    
                    total_cleaned += count
                else:
                    self.stdout.write(f"  [DRY RUN] Would clean up {count} calls")
                    for call in stuck_calls[:5]:  # Show first 5 as examples
                        self.stdout.write(
                            f"    - Call {call.id} (since {call.initiated_at})"
                        )
                    if count > 5:
                        self.stdout.write(f"    ... and {count - 5} more")
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN completed. Use without --dry-run to actually clean up calls.")
            )
        else:
            if total_cleaned > 0:
                self.stdout.write(
                    self.style.SUCCESS(f"Successfully cleaned up {total_cleaned} stuck calls.")
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS("No stuck calls found. All calls are in proper states.")
                )