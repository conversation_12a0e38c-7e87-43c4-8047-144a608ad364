// frontend/src/components/calling/IncomingCallModal.tsx
import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Phone, PhoneOff, User } from 'lucide-react';
import { useAnswerCallMutation, useDeclineCallMutation } from '../../services/callingApi';
import {
  selectShowIncomingCall,
  selectCallingState,
  hideIncomingCallModal,
  showActiveCallInterface,
  updateCallStatus,
  resetCallState,
  setCurrentCall
} from '../../store/slices/callingSlice';

export const IncomingCallModal: React.FC = () => {
  const dispatch = useDispatch();
  const [answerCall, { isLoading: isAnswering }] = useAnswerCallMutation();
  const [declineCall, { isLoading: isDeclining }] = useDeclineCallMutation();

  const showIncomingCall = useSelector(selectShowIncomingCall);
  const { incomingCallData } = useSelector(selectCallingState);

  const handleAnswer = async () => {
    if (!incomingCallData?.callId) {
      console.error('No call ID available to answer');
      return;
    }

    if (isAnswering) {
      console.warn('Already answering call');
      return;
    }

    try {
      console.log('✅ Answering call:', incomingCallData.callId);

      // Immediately hide the modal and show active call interface
      dispatch(hideIncomingCallModal());
      dispatch(showActiveCallInterface());
      dispatch(setCurrentCall({
        id: incomingCallData.callId,
        conversationId: incomingCallData.conversationId,
        type: incomingCallData.callType,
        status: 'ringing',
        startTime: Date.now(),
        duration: 0,
        isInitiator: false
      }));

      const result = await answerCall({ callId: incomingCallData.callId }).unwrap();
      console.log('✅ Call answered successfully:', result);
    } catch (error) {
      console.error('❌ Failed to answer call:', error);
      // If API call fails, reset the call state
      dispatch(resetCallState());
    }
  };

  const handleDecline = async () => {
    if (!incomingCallData?.callId) {
      console.error('No call ID available to decline');
      return;
    }

    if (isDeclining) {
      console.warn('Already declining call');
      return;
    }

    try {
      console.log('❌ Declining call:', incomingCallData.callId);

      // Immediately hide the modal and reset call state
      dispatch(hideIncomingCallModal());
      dispatch(resetCallState());

      const result = await declineCall({ callId: incomingCallData.callId }).unwrap();
      console.log('✅ Call declined successfully:', result);
    } catch (error) {
      console.error('❌ Failed to decline call:', error);
      // Still hide the modal even if the API call fails
      dispatch(hideIncomingCallModal());
      dispatch(resetCallState());
    }
  };

  // Don't render if not showing or no call data
  if (!showIncomingCall || !incomingCallData) {
    return null;
  }

  const { caller, callType } = incomingCallData;

  return (
    <div data-testid="incoming-call-modal" className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80">
      <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-sm w-full mx-4 text-center">
        {/* Caller Avatar */}
        <div className="mb-6">
          {caller.avatar ? (
            <img
              src={caller.avatar}
              alt={`${caller.firstName} ${caller.lastName}`}
              className="w-24 h-24 rounded-full mx-auto object-cover border-4 border-gray-200"
            />
          ) : (
            <div className="w-24 h-24 rounded-full mx-auto bg-gray-300 flex items-center justify-center border-4 border-gray-200">
              <User className="w-12 h-12 text-gray-600" />
            </div>
          )}
        </div>

        {/* Caller Info */}
        <div className="mb-2">
          <h3 className="text-xl font-semibold text-gray-900">
            {caller.firstName} {caller.lastName}
          </h3>
          <p className="text-sm text-gray-500 mb-1">@{caller.username}</p>
        </div>

        {/* Call Type */}
        <div className="mb-8">
          <p className="text-lg text-gray-700">
            Incoming {callType} call...
          </p>
          <div className="flex items-center justify-center mt-2">
            {callType === 'video' ? (
              <div className="flex items-center text-blue-600">
                <svg className="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                </svg>
                <span className="text-sm">Video Call</span>
              </div>
            ) : (
              <div className="flex items-center text-green-600">
                <Phone className="w-5 h-5 mr-1" />
                <span className="text-sm">Audio Call</span>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center space-x-6">
          {/* Decline Button */}
          <button
            data-testid="decline-call-btn"
            onClick={handleDecline}
            disabled={isDeclining || isAnswering}
            className={`w-16 h-16 rounded-full flex items-center justify-center text-white shadow-lg transition-all duration-200 ${
              isDeclining || isAnswering
                ? 'bg-red-400 cursor-not-allowed'
                : 'bg-red-500 hover:bg-red-600 hover:scale-105 active:scale-95'
            }`}
            title={isDeclining ? 'Declining call...' : 'Decline call'}
          >
            <PhoneOff className="w-8 h-8" />
          </button>

          {/* Answer Button */}
          <button
            data-testid="answer-call-btn"
            onClick={handleAnswer}
            disabled={isAnswering || isDeclining}
            className={`w-16 h-16 rounded-full flex items-center justify-center text-white shadow-lg transition-all duration-200 ${
              isAnswering || isDeclining
                ? 'bg-green-400 cursor-not-allowed'
                : 'bg-green-500 hover:bg-green-600 hover:scale-105 active:scale-95'
            }`}
            title={isAnswering ? 'Answering call...' : 'Answer call'}
          >
            <Phone className="w-8 h-8" />
          </button>
        </div>

        {/* Ringing Animation */}
        <div className="mt-6">
          <div className="flex justify-center">
            <div className="animate-pulse">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">Ringing...</p>
        </div>
      </div>

      {/* Background overlay click handler */}
      <div 
        className="absolute inset-0 -z-10" 
        onClick={handleDecline}
        aria-label="Decline call by clicking outside"
      />
    </div>
  );
};

export default IncomingCallModal;
