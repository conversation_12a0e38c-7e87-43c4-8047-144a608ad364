// socket-server/src/middleware/authHttp.ts
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface JWTPayload {
  user_id: string;
  token_type: string;
  iat: number;
  exp: number;
  jti: string;
}

export interface AuthenticatedRequest extends Request {
  userId: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
}

/**
 * Express middleware to authenticate HTTP requests using JWT tokens
 * Similar to socket authentication but for Express routes
 */
export const authenticateHttpRequest = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ 
        error: 'Authentication required',
        message: 'Missing or invalid authorization header' 
      });
      return;
    }

    const token = authHeader.replace('Bearer ', '');

    // Check if JWT_SECRET is configured
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET not configured');
      res.status(500).json({ 
        error: 'Server configuration error',
        message: 'JWT secret not configured' 
      });
      return;
    }

    // Verify JWT token
    let decoded: JWTPayload;
    try {
      decoded = jwt.verify(token, jwtSecret) as JWTPayload;
    } catch (jwtError) {
      console.error('JWT verification failed:', jwtError);
      res.status(401).json({ 
        error: 'Invalid token',
        message: 'Token verification failed' 
      });
      return;
    }

    // Fetch user from database
    const user = await prisma.users.findUnique({
      where: { id: decoded.user_id },
      select: {
        id: true,
        email: true,
        name: true,
      },
    });

    if (!user) {
      console.error('User not found for ID:', decoded.user_id);
      res.status(401).json({ 
        error: 'User not found',
        message: 'The user associated with this token does not exist' 
      });
      return;
    }

    // Attach user info to request
    (req as AuthenticatedRequest).userId = user.id;
    (req as AuthenticatedRequest).user = user;

    // Continue to next middleware
    next();
  } catch (error) {
    console.error('HTTP authentication error:', error);
    res.status(500).json({ 
      error: 'Authentication error',
      message: error instanceof Error ? error.message : 'An unexpected error occurred' 
    });
  }
};

