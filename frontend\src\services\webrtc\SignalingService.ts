// frontend/src/services/webrtc/SignalingService.ts
import { Socket } from 'socket.io-client';
import type { AppDispatch } from '../../store';

export interface CallInitiationData {
  conversationId: string;
  callType: 'audio' | 'video';
}

export interface CallResponseData {
  callId: string;
}

export interface WebRTCOfferData {
  callId: string;
  offer: RTCSessionDescriptionInit;
}

export interface WebRTCAnswerData {
  callId: string;
  answer: RTCSessionDescriptionInit;
}

export interface ICECandidateData {
  callId: string;
  candidate: RTCIceCandidateInit;
}

export interface MediaToggleData {
  callId: string;
  enabled: boolean;
}

export class SignalingService {
  private socket: Socket;
  private webrtcService: any = null; // Will be injected

  constructor(socket: Socket, dispatch: AppDispatch) {
    this.socket = socket;
    console.log('📡 SignalingService initialized');
  }

  /**
   * Set WebRTC service reference for handling signaling events
   */
  setWebRTCService(webrtcService: any): void {
    this.webrtcService = webrtcService;
    console.log('📡 WebRTC service reference set in SignalingService');
  }

  /**
   * Setup socket listeners for calling events
   * Following exact patterns from HTML test implementation
   */
  setupSocketListeners(): void {
    if (!this.socket) {
      console.error('❌ No socket available for setting up listeners');
      return;
    }

    console.log('📡 Setting up calling socket listeners');

    // Call management events (exact HTML test patterns)
    this.socket.on('call_initiated', this.handleCallInitiated.bind(this));
    this.socket.on('incoming_call', this.handleIncomingCall.bind(this));
    this.socket.on('call_answered', this.handleCallAnswered.bind(this));
    this.socket.on('call_declined', this.handleCallDeclined.bind(this));
    this.socket.on('call_ended', this.handleCallEnded.bind(this));

    // WebRTC signaling events (exact HTML test patterns)
    this.socket.on('webrtc_offer', this.handleWebRTCOffer.bind(this));
    this.socket.on('webrtc_answer', this.handleWebRTCAnswer.bind(this));
    this.socket.on('webrtc_ice_candidate', this.handleICECandidate.bind(this));

    // Media control events (exact HTML test patterns)
    this.socket.on('toggle_audio', this.handleToggleAudio.bind(this));
    this.socket.on('toggle_video', this.handleToggleVideo.bind(this));

    console.log('📡 Calling socket listeners setup complete');
  }

  /**
   * Remove socket listeners for calling events
   */
  removeSocketListeners(): void {
    if (!this.socket) return;

    console.log('📡 Removing calling socket listeners');

    this.socket.off('call_initiated');
    this.socket.off('incoming_call');
    this.socket.off('call_answered');
    this.socket.off('call_declined');
    this.socket.off('call_ended');
    this.socket.off('webrtc_offer');
    this.socket.off('webrtc_answer');
    this.socket.off('webrtc_ice_candidate');
    this.socket.off('toggle_audio');
    this.socket.off('toggle_video');
  }

  // WebRTC signaling methods (call management now uses REST API endpoints)
  // Note: Call management methods (emitCallInitiation, emitCallAnswer, emitCallDecline, emitCallEnd)
  // have been removed. These operations now use REST API endpoints via RTK Query hooks.

  /**
   * Emit WebRTC offer
   */
  emitWebRTCOffer(data: WebRTCOfferData): void {
    if (!this.socket?.connected) {
      console.error('❌ Socket not connected, cannot send WebRTC offer');
      return;
    }

    console.log('📤 Emitting webrtc_offer for call:', data.callId);
    this.socket.emit('webrtc_offer', data);
  }

  /**
   * Emit WebRTC answer
   */
  emitWebRTCAnswer(data: WebRTCAnswerData): void {
    if (!this.socket?.connected) {
      console.error('❌ Socket not connected, cannot send WebRTC answer');
      return;
    }

    console.log('📥 Emitting webrtc_answer for call:', data.callId);
    this.socket.emit('webrtc_answer', data);
  }

  /**
   * Emit ICE candidate
   */
  emitICECandidate(data: ICECandidateData): void {
    if (!this.socket?.connected) {
      console.error('❌ Socket not connected, cannot send ICE candidate');
      return;
    }

    console.log('🧊 Emitting webrtc_ice_candidate for call:', data.callId);
    this.socket.emit('webrtc_ice_candidate', data);
  }

  /**
   * Emit audio toggle
   */
  emitAudioToggle(data: MediaToggleData): void {
    if (!this.socket?.connected) {
      console.error('❌ Socket not connected, cannot toggle audio');
      return;
    }

    console.log('🎤 Emitting toggle_audio:', data);
    this.socket.emit('toggle_audio', data);
  }

  /**
   * Emit video toggle
   */
  emitVideoToggle(data: MediaToggleData): void {
    if (!this.socket?.connected) {
      console.error('❌ Socket not connected, cannot toggle video');
      return;
    }

    console.log('📹 Emitting toggle_video:', data);
    this.socket.emit('toggle_video', data);
  }

  // Event handlers (to be implemented by the calling manager)
  private handleCallInitiated(data: any): void {
    console.log('📞 Received call_initiated:', data);
    // This will be handled by the CallManager
  }

  private handleIncomingCall(data: any): void {
    console.log('🔔 Received incoming_call:', data);
    // This will be handled by the CallManager
  }

  private handleCallAnswered(data: any): void {
    console.log('✅ Received call_answered:', data);
    // This will be handled by the CallManager
  }

  private handleCallDeclined(data: any): void {
    console.log('❌ Received call_declined:', data);
    // This will be handled by the CallManager
  }

  private handleCallEnded(data: any): void {
    console.log('📞 Received call_ended:', data);
    // This will be handled by the CallManager
  }

  private handleWebRTCOffer(data: any): void {
    console.log('📤 Received webrtc_offer:', data);
    if (this.webrtcService) {
      this.webrtcService.handleWebRTCOffer(data);
    } else {
      console.error('❌ No WebRTC service available to handle offer');
    }
  }

  private handleWebRTCAnswer(data: any): void {
    console.log('📥 Received webrtc_answer:', data);
    if (this.webrtcService) {
      this.webrtcService.handleWebRTCAnswer(data);
    } else {
      console.error('❌ No WebRTC service available to handle answer');
    }
  }

  private handleICECandidate(data: any): void {
    console.log('🧊 Received webrtc_ice_candidate:', data);
    if (this.webrtcService) {
      this.webrtcService.handleICECandidate(data);
    } else {
      console.error('❌ No WebRTC service available to handle ICE candidate');
    }
  }

  private handleToggleAudio(data: any): void {
    console.log('🎤 Received toggle_audio:', data);
    // This will be handled by the CallManager
  }

  private handleToggleVideo(data: any): void {
    console.log('📹 Received toggle_video:', data);
    // This will be handled by the CallManager
  }

  /**
   * Check if socket is connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Get socket instance
   */
  getSocket(): Socket {
    return this.socket;
  }

  /**
   * Update socket instance
   */
  updateSocket(socket: Socket): void {
    // Remove listeners from old socket
    this.removeSocketListeners();
    
    // Update socket reference
    this.socket = socket;
    
    // Setup listeners on new socket
    this.setupSocketListeners();
  }
}
