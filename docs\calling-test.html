<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laqaa Calling Test Page</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .auth-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }

        .auth-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .input-group {
            flex: 1;
            min-width: 300px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        input[type="text"], input[type="password"], select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, input[type="password"]:focus, select:focus {
            outline: none;
            border-color: #4facfe;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .section h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 5px;
        }

        .call-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .media-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }

        .video-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .video-wrapper {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            aspect-ratio: 16/9;
        }

        video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-label {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
        }

        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.calling {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            align-items: end;
            margin-bottom: 15px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .auth-controls {
                flex-direction: column;
            }
            
            .input-group {
                min-width: auto;
            }
            
            .video-container {
                grid-template-columns: 1fr;
            }
        }

        .call-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .call-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e1f5fe;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge.active {
            background: #4caf50;
            color: white;
        }

        .badge.inactive {
            background: #f44336;
            color: white;
        }

        .badge.ringing {
            background: #ff9800;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 Laqaa Calling Test Interface</h1>
            <p>Comprehensive testing for WebRTC calling functionality</p>
        </div>

        <!-- Authentication Section -->
        <div class="auth-section">
            <div class="auth-controls">
                <div class="input-group">
                    <label for="tokenInput">JWT Authentication Token:</label>
                    <input type="password" id="tokenInput" placeholder="Enter your JWT token...">
                </div>
                <button id="connectBtn" class="btn-primary">Connect</button>
                <button id="disconnectBtn" class="btn-danger" disabled>Disconnect</button>
            </div>
            <div id="connectionStatus" class="status disconnected">Disconnected</div>
        </div>

        <div class="main-content">
            <!-- Call Management Section -->
            <div class="section">
                <h3>📞 Call Management</h3>
                
                <div class="form-row">
                    <div class="input-group">
                        <label for="conversationId">Conversation ID:</label>
                        <input type="text" id="conversationId" placeholder="Enter conversation ID...">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="input-group">
                        <label for="callType">Call Type:</label>
                        <select id="callType">
                            <option value="audio">Audio Only</option>
                            <option value="video">Video Call</option>
                        </select>
                    </div>
                </div>

                <div class="call-controls">
                    <button id="initiateCallBtn" class="btn-success">📞 Initiate Call</button>
                    <button id="answerCallBtn" class="btn-success" disabled>✅ Answer Call</button>
                    <button id="declineCallBtn" class="btn-danger" disabled>❌ Decline Call</button>
                    <button id="endCallBtn" class="btn-danger" disabled>📴 End Call</button>
                </div>

                <div id="currentCallInfo" class="call-info" style="display: none;">
                    <h4>Current Call Information</h4>
                    <div class="info-item">
                        <span>Call ID:</span>
                        <span id="callIdDisplay">-</span>
                    </div>
                    <div class="info-item">
                        <span>Status:</span>
                        <span id="callStatusDisplay" class="badge inactive">-</span>
                    </div>
                    <div class="info-item">
                        <span>Type:</span>
                        <span id="callTypeDisplay">-</span>
                    </div>
                    <div class="info-item">
                        <span>Duration:</span>
                        <span id="callDurationDisplay">00:00</span>
                    </div>
                </div>

                <!-- Media Controls -->
                <div class="media-controls">
                    <button id="toggleAudioBtn" class="btn-warning" disabled>🎤 Mute Audio</button>
                    <button id="toggleVideoBtn" class="btn-warning" disabled>📹 Stop Video</button>
                    <button id="shareScreenBtn" class="btn-primary" disabled>🖥️ Share Screen</button>
                </div>
            </div>

            <!-- WebRTC & Media Section -->
            <div class="section">
                <h3>🎥 WebRTC Media</h3>
                
                <div class="video-container">
                    <div class="video-wrapper">
                        <video id="localVideo" autoplay muted playsinline></video>
                        <div class="video-label">Local Video</div>
                    </div>
                    <div class="video-wrapper">
                        <video id="remoteVideo" autoplay playsinline controls></video>
                        <div class="video-label">Remote Video</div>
                    </div>
                </div>

                <div class="call-controls">
                    <button id="getMediaBtn" class="btn-primary">🎥 Get User Media</button>
                    <button id="createOfferBtn" class="btn-primary" disabled>📤 Create Offer</button>
                    <button id="createAnswerBtn" class="btn-primary" disabled>📥 Create Answer</button>
                </div>
            </div>

            <!-- API Testing Section -->
            <div class="section">
                <h3>🔧 API Testing</h3>
                
                <div class="form-row">
                    <div class="input-group">
                        <label for="testCallId">Call ID for Testing:</label>
                        <input type="text" id="testCallId" placeholder="Enter call ID...">
                    </div>
                </div>

                <div class="call-controls">
                    <button id="getCallDetailBtn" class="btn-primary">📋 Get Call Detail</button>
                    <button id="getCallHistoryBtn" class="btn-primary">📚 Get Call History</button>
                    <button id="reportQualityBtn" class="btn-warning">📊 Report Quality</button>
                </div>
            </div>

            <!-- Debug Section -->
            <div class="section">
                <h3>🐛 Debug & Logs</h3>
                
                <div class="call-controls">
                    <button id="debugRoomBtn" class="btn-warning">🔍 Debug Room</button>
                    <button id="clearLogsBtn" class="btn-danger">🗑️ Clear Logs</button>
                </div>
                
                <div id="logOutput" class="log">Waiting for events...</div>
            </div>
        </div>
    </div>

    <script>
        class CallingTestApp {
            constructor() {
                this.socket = null;
                this.localStream = null;
                this.remoteStream = null;
                this.peerConnection = null;
                this.currentCallId = null;
                this.callStartTime = null;
                this.durationInterval = null;
                this.isAudioMuted = false;
                this.isVideoMuted = false;
                
                // WebRTC configuration
                this.rtcConfig = {
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' },
                        { urls: 'stun:stun1.l.google.com:19302' }
                    ]
                };
                
                this.initializeElements();
                this.setupEventListeners();
                this.loadSavedToken();
                this.log('Application initialized');
            }

            initializeElements() {
                // Authentication elements
                this.tokenInput = document.getElementById('tokenInput');
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.connectionStatus = document.getElementById('connectionStatus');
                
                // Call management elements
                this.conversationIdInput = document.getElementById('conversationId');
                this.callTypeSelect = document.getElementById('callType');
                this.initiateCallBtn = document.getElementById('initiateCallBtn');
                this.answerCallBtn = document.getElementById('answerCallBtn');
                this.declineCallBtn = document.getElementById('declineCallBtn');
                this.endCallBtn = document.getElementById('endCallBtn');
                
                // Call info elements
                this.currentCallInfo = document.getElementById('currentCallInfo');
                this.callIdDisplay = document.getElementById('callIdDisplay');
                this.callStatusDisplay = document.getElementById('callStatusDisplay');
                this.callTypeDisplay = document.getElementById('callTypeDisplay');
                this.callDurationDisplay = document.getElementById('callDurationDisplay');
                
                // Media control elements
                this.toggleAudioBtn = document.getElementById('toggleAudioBtn');
                this.toggleVideoBtn = document.getElementById('toggleVideoBtn');
                this.shareScreenBtn = document.getElementById('shareScreenBtn');
                
                // Video elements
                this.localVideo = document.getElementById('localVideo');
                this.remoteVideo = document.getElementById('remoteVideo');
                
                // WebRTC control elements
                this.getMediaBtn = document.getElementById('getMediaBtn');
                this.createOfferBtn = document.getElementById('createOfferBtn');
                this.createAnswerBtn = document.getElementById('createAnswerBtn');
                
                // API testing elements
                this.testCallIdInput = document.getElementById('testCallId');
                this.getCallDetailBtn = document.getElementById('getCallDetailBtn');
                this.getCallHistoryBtn = document.getElementById('getCallHistoryBtn');
                this.reportQualityBtn = document.getElementById('reportQualityBtn');
                
                // Debug elements
                this.debugRoomBtn = document.getElementById('debugRoomBtn');
                this.clearLogsBtn = document.getElementById('clearLogsBtn');
                this.logOutput = document.getElementById('logOutput');
            }

            setupEventListeners() {
                // Authentication events
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                
                // Call management events
                this.initiateCallBtn.addEventListener('click', () => this.initiateCall());
                this.answerCallBtn.addEventListener('click', () => this.answerCall());
                this.declineCallBtn.addEventListener('click', () => this.declineCall());
                this.endCallBtn.addEventListener('click', () => this.endCall());
                
                // Media control events
                this.toggleAudioBtn.addEventListener('click', () => this.toggleAudio());
                this.toggleVideoBtn.addEventListener('click', () => this.toggleVideo());
                this.shareScreenBtn.addEventListener('click', () => this.shareScreen());
                
                // WebRTC events
                this.getMediaBtn.addEventListener('click', () => this.getUserMedia());
                this.createOfferBtn.addEventListener('click', () => this.createOffer());
                this.createAnswerBtn.addEventListener('click', () => this.createAnswer());
                
                // API testing events
                this.getCallDetailBtn.addEventListener('click', () => this.getCallDetail());
                this.getCallHistoryBtn.addEventListener('click', () => this.getCallHistory());
                this.reportQualityBtn.addEventListener('click', () => this.reportCallQuality());
                
                // Debug events
                this.debugRoomBtn.addEventListener('click', () => this.debugRoom());
                this.clearLogsBtn.addEventListener('click', () => this.clearLogs());
                
                // Token input events
                this.tokenInput.addEventListener('input', () => this.saveToken());
                this.tokenInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.connect();
                });
            }

            loadSavedToken() {
                const savedToken = localStorage.getItem('laqaa_jwt_token');
                if (savedToken) {
                    this.tokenInput.value = savedToken;
                    this.log('Loaded saved token from localStorage');
                }
            }

            saveToken() {
                const token = this.tokenInput.value.trim();
                if (token) {
                    localStorage.setItem('laqaa_jwt_token', token);
                    this.log('Token saved to localStorage');
                } else {
                    localStorage.removeItem('laqaa_jwt_token');
                }
            }

            async connect() {
                const token = this.tokenInput.value.trim();
                if (!token) {
                    this.log('❌ Please enter a JWT token', 'error');
                    return;
                }

                try {
                    this.log('🔄 Connecting to socket server...');
                    
                    this.socket = io('http://localhost:7000', {
                        auth: {
                            token: token
                        },
                        transports: ['websocket', 'polling']
                    });

                    this.setupSocketEvents();
                    
                } catch (error) {
                    this.log(`❌ Connection failed: ${error.message}`, 'error');
                }
            }

            setupSocketEvents() {
                // Connection events
                this.socket.on('connect', () => {
                    this.log('✅ Connected to socket server');
                    this.updateConnectionStatus(true);
                });

                this.socket.on('disconnect', (reason) => {
                    this.log(`❌ Disconnected: ${reason}`, 'error');
                    this.updateConnectionStatus(false);
                });

                this.socket.on('connect_error', (error) => {
                    this.log(`❌ Connection error: ${error.message}`, 'error');
                    this.updateConnectionStatus(false);
                });

                // Call events
                this.socket.on('incoming_call', (data) => {
                    this.log(`📞 Incoming call from ${data.caller.username}`);
                    this.handleIncomingCall(data);
                });

                this.socket.on('call_initiated', (data) => {
                    this.log(`📞 Call initiated: ${data.callId}`);
                    this.currentCallId = data.callId;
                    this.updateCallInfo(data.callId, 'initiated');
                });

                this.socket.on('call_ringing', (data) => {
                    this.log(`📞 Call ringing: ${data.callId}`);
                    this.updateCallStatus('ringing');
                });

                this.socket.on('call_answered', (data) => {
                    this.log(`✅ Call answered: ${data.callId}`);
                    this.updateCallStatus('answered');
                    
                    // Automatically create WebRTC offer when call is answered
                    if (this.peerConnection && this.localStream) {
                        this.createOffer();
                    }
                });

                this.socket.on('call_active', (data) => {
                    this.log(`🔥 Call active: ${data.callId}`);
                    this.updateCallStatus('active');
                });

                this.socket.on('call_declined', (data) => {
                    this.log(`❌ Call declined: ${data.callId}`);
                    this.resetCallState();
                });

                this.socket.on('call_ended', (data) => {
                    this.log(`📴 Call ended: ${data.callId}`);
                    this.resetCallState();
                });

                this.socket.on('call_error', (error) => {
                    this.log(`❌ Call error: ${error.message}`, 'error');
                });

                // WebRTC signaling events
                this.socket.on('webrtc_offer', (data) => {
                    this.log(`📤 Received WebRTC offer from ${data.from || 'caller'} for call ${data.callId}`);
                    this.handleWebRTCOffer(data);
                });

                this.socket.on('webrtc_answer', (data) => {
                    this.log(`📥 Received WebRTC answer from ${data.from || 'callee'} for call ${data.callId}`);
                    this.handleWebRTCAnswer(data);
                });

                this.socket.on('webrtc_ice_candidate', (data) => {
                    this.log(`🧊 Received ICE candidate from ${data.from || 'peer'} for call ${data.callId}`);
                    this.handleICECandidate(data);
                });

                // Additional WebRTC debugging events
                this.socket.on('webrtc_error', (data) => {
                    this.log(`❌ WebRTC Error: ${data.error}`, 'error');
                });

                this.socket.on('webrtc_connection_state', (data) => {
                    this.log(`🔗 WebRTC Connection State: ${data.state}`);
                });

                // Media control events
                this.socket.on('media_toggle', (data) => {
                    this.log(`🎛️ Media toggle: ${data.mediaType} ${data.enabled ? 'enabled' : 'disabled'}`);
                });
            }

            disconnect() {
                if (this.socket) {
                    this.socket.disconnect();
                    this.socket = null;
                    this.log('🔌 Disconnected from socket server');
                }
                this.updateConnectionStatus(false);
                this.resetCallState();
            }

            updateConnectionStatus(connected) {
                if (connected) {
                    this.connectionStatus.textContent = 'Connected';
                    this.connectionStatus.className = 'status connected';
                    this.connectBtn.disabled = true;
                    this.disconnectBtn.disabled = false;
                    this.initiateCallBtn.disabled = false;
                } else {
                    this.connectionStatus.textContent = 'Disconnected';
                    this.connectionStatus.className = 'status disconnected';
                    this.connectBtn.disabled = false;
                    this.disconnectBtn.disabled = true;
                    this.initiateCallBtn.disabled = true;
                }
            }

            async initiateCall() {
                const conversationId = this.conversationIdInput.value.trim();
                const callType = this.callTypeSelect.value;

                if (!conversationId) {
                    this.log('❌ Please enter a conversation ID', 'error');
                    return;
                }

                if (!this.socket || !this.socket.connected) {
                    this.log('❌ Not connected to socket server', 'error');
                    return;
                }

                try {
                    this.log(`📞 Initiating ${callType} call for conversation ${conversationId}`);
                    
                    // Get user media first if not already available
                    if (!this.localStream) {
                        await this.getUserMedia();
                    }
                    
                    // Setup WebRTC peer connection
                    this.setupPeerConnection();
                    
                    // Add local stream to peer connection
                    if (this.localStream) {
                        this.localStream.getTracks().forEach(track => {
                            this.peerConnection.addTrack(track, this.localStream);
                        });
                    }
                    
                    this.socket.emit('initiate_call', {
                        conversationId: conversationId,
                        callType: callType
                    });

                    this.initiateCallBtn.disabled = true;
                    
                    // Enable media controls
                    this.toggleAudioBtn.disabled = false;
                    this.toggleVideoBtn.disabled = false;
                    this.shareScreenBtn.disabled = false;
                    
                } catch (error) {
                    this.log(`❌ Failed to initiate call: ${error.message}`, 'error');
                }
            }

            async handleIncomingCall(data) {
                this.currentCallId = data.callId;
                this.updateCallInfo(data.callId, 'incoming', data.callType);
                
                // Prepare media for the incoming call
                if (!this.localStream) {
                    try {
                        await this.getUserMedia();
                        this.log('🎥 Media prepared for incoming call');
                    } catch (error) {
                        this.log(`⚠️ Could not prepare media: ${error.message}`, 'error');
                    }
                }
                
                // Enable answer/decline buttons
                this.answerCallBtn.disabled = false;
                this.declineCallBtn.disabled = false;
                
                // Show notification
                if (Notification.permission === 'granted') {
                    new Notification('Incoming Call', {
                        body: `${data.caller.firstName} ${data.caller.lastName} is calling`,
                        icon: '/favicon.ico'
                    });
                }
            }

            async answerCall() {
                if (!this.currentCallId) {
                    this.log('❌ No active call to answer', 'error');
                    return;
                }

                this.log(`✅ Answering call ${this.currentCallId}`);
                
                // First get user media if not already available
                if (!this.localStream) {
                    await this.getUserMedia();
                }
                
                // Setup WebRTC peer connection
                this.setupPeerConnection();
                
                // Add local stream to peer connection
                if (this.localStream) {
                    this.localStream.getTracks().forEach(track => {
                        this.peerConnection.addTrack(track, this.localStream);
                    });
                }
                
                // Emit answer call event
                this.socket.emit('answer_call', {
                    callId: this.currentCallId
                });

                this.answerCallBtn.disabled = true;
                this.declineCallBtn.disabled = true;
                this.endCallBtn.disabled = false;
                
                // Enable media controls
                this.toggleAudioBtn.disabled = false;
                this.toggleVideoBtn.disabled = false;
                this.shareScreenBtn.disabled = false;
            }

            declineCall() {
                if (!this.currentCallId) {
                    this.log('❌ No active call to decline', 'error');
                    return;
                }

                this.log(`❌ Declining call ${this.currentCallId}`);
                this.socket.emit('decline_call', {
                    callId: this.currentCallId
                });

                this.resetCallState();
            }

            endCall() {
                if (!this.currentCallId) {
                    this.log('❌ No active call to end', 'error');
                    return;
                }

                this.log(`📴 Ending call ${this.currentCallId}`);
                this.socket.emit('end_call', {
                    callId: this.currentCallId
                });

                this.resetCallState();
            }

            updateCallInfo(callId, status, type = null) {
                this.currentCallId = callId;
                this.callIdDisplay.textContent = callId;
                this.updateCallStatus(status);
                
                if (type) {
                    this.callTypeDisplay.textContent = type;
                }
                
                this.currentCallInfo.style.display = 'block';
                this.endCallBtn.disabled = false;
            }

            updateCallStatus(status) {
                this.callStatusDisplay.textContent = status;
                this.callStatusDisplay.className = `badge ${status === 'active' ? 'active' : status === 'ringing' ? 'ringing' : 'inactive'}`;
            }

            startCallTimer() {
                this.callStartTime = Date.now();
                this.durationInterval = setInterval(() => {
                    const elapsed = Date.now() - this.callStartTime;
                    const minutes = Math.floor(elapsed / 60000);
                    const seconds = Math.floor((elapsed % 60000) / 1000);
                    this.callDurationDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }, 1000);
            }

            resetCallState() {
                this.currentCallId = null;
                this.currentCallInfo.style.display = 'none';
                
                // Reset buttons
                this.initiateCallBtn.disabled = false;
                this.answerCallBtn.disabled = true;
                this.declineCallBtn.disabled = true;
                this.endCallBtn.disabled = true;
                
                // Reset media controls
                this.toggleAudioBtn.disabled = true;
                this.toggleVideoBtn.disabled = true;
                this.shareScreenBtn.disabled = true;
                
                // Clear timer
                if (this.durationInterval) {
                    clearInterval(this.durationInterval);
                    this.durationInterval = null;
                }
                
                // Reset WebRTC
                this.closePeerConnection();
            }

            async getUserMedia() {
                try {
                    const constraints = {
                        audio: true,
                        video: this.callTypeSelect.value === 'video'
                    };

                    this.log(`🎥 Requesting user media: ${JSON.stringify(constraints)}`);
                    
                    this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
                    this.localVideo.srcObject = this.localStream;
                    
                    // Debug audio tracks
                    const audioTracks = this.localStream.getAudioTracks();
                    const videoTracks = this.localStream.getVideoTracks();
                    this.log(`📊 Local stream has ${audioTracks.length} audio tracks and ${videoTracks.length} video tracks`);
                    
                    // Log audio track details
                    audioTracks.forEach((track, index) => {
                        this.log(`🎤 Audio track ${index}: enabled=${track.enabled}, muted=${track.muted}, readyState=${track.readyState}`);
                    });
                    
                    this.log('✅ User media obtained successfully');
                    
                    // Enable media controls
                    this.toggleAudioBtn.disabled = false;
                    if (constraints.video) {
                        this.toggleVideoBtn.disabled = false;
                    }
                    this.shareScreenBtn.disabled = false;
                    this.createOfferBtn.disabled = false;
                    
                } catch (error) {
                    this.log(`❌ Failed to get user media: ${error.message}`, 'error');
                }
            }

            async createOffer() {
                if (!this.localStream) {
                    this.log('❌ No local stream available', 'error');
                    return;
                }

                try {
                    this.log('📤 Starting WebRTC offer creation...');
                    this.setupPeerConnection();
                    
                    // Add local stream to peer connection (check if not already added)
                    const existingSenders = this.peerConnection.getSenders();
                    this.localStream.getTracks().forEach(track => {
                        const existingSender = existingSenders.find(sender => sender.track === track);
                        if (!existingSender) {
                            this.peerConnection.addTrack(track, this.localStream);
                            this.log(`📤 Added ${track.kind} track to peer connection (enabled: ${track.enabled}, muted: ${track.muted})`);
                        } else {
                            this.log(`📤 ${track.kind} track already exists in peer connection`);
                        }
                    });
                    
                    // Log current senders for debugging
                    const senders = this.peerConnection.getSenders();
                    this.log(`📊 Peer connection has ${senders.length} senders`);
                    senders.forEach((sender, index) => {
                        if (sender.track) {
                            this.log(`📊 Sender ${index}: ${sender.track.kind} track (enabled: ${sender.track.enabled})`);
                        }
                    });

                    this.log('📤 Creating offer with constraints...');
                    const offer = await this.peerConnection.createOffer({
                        offerToReceiveAudio: true,
                        offerToReceiveVideo: true
                    });
                    
                    this.log('📤 Setting local description...');
                    await this.peerConnection.setLocalDescription(offer);

                    this.log(`📤 Sending WebRTC offer for call ${this.currentCallId}`);
                    
                    if (this.currentCallId && this.socket) {
                        this.socket.emit('webrtc_offer', {
                            callId: this.currentCallId,
                            offer: offer
                        });
                        this.log('✅ WebRTC offer sent successfully');
                    } else {
                        this.log('❌ Cannot send offer: missing callId or socket', 'error');
                    }
                    
                } catch (error) {
                    this.log(`❌ Failed to create offer: ${error.message}`, 'error');
                    console.error('WebRTC offer creation error:', error);
                }
            }

            async createAnswer() {
                if (!this.peerConnection) {
                    this.log('❌ No peer connection available', 'error');
                    return;
                }

                try {
                    const answer = await this.peerConnection.createAnswer();
                    await this.peerConnection.setLocalDescription(answer);

                    this.log('📥 Created WebRTC answer');
                    
                    if (this.currentCallId && this.socket) {
                        this.socket.emit('webrtc_answer', {
                            callId: this.currentCallId,
                            answer: answer
                        });
                    }
                    
                } catch (error) {
                    this.log(`❌ Failed to create answer: ${error.message}`, 'error');
                }
            }

            setupPeerConnection() {
                if (this.peerConnection) {
                    return;
                }

                this.peerConnection = new RTCPeerConnection(this.rtcConfig);
                
                // Handle remote stream
                this.peerConnection.ontrack = (event) => {
                    this.log('🎥 Received remote stream');
                    const stream = event.streams[0];
                    
                    // Log track information
                    const audioTracks = stream.getAudioTracks();
                    const videoTracks = stream.getVideoTracks();
                    this.log(`📊 Remote stream has ${audioTracks.length} audio tracks and ${videoTracks.length} video tracks`);
                    
                    // Set the stream to remote video element
                    this.remoteVideo.srcObject = stream;
                    this.remoteStream = stream;
                    
                    // Ensure audio is enabled and not muted
                    this.remoteVideo.muted = false;
                    this.remoteVideo.volume = 1.0;
                    
                    // Force play if needed
                    this.remoteVideo.play().catch(error => {
                        this.log(`⚠️ Could not auto-play remote video: ${error.message}`);
                    });
                    
                    this.log('✅ Remote stream configured successfully');
                };

                // Handle ICE candidates
                this.peerConnection.onicecandidate = (event) => {
                    if (event.candidate && this.currentCallId && this.socket) {
                        this.log(`🧊 Sending ICE candidate`);
                        this.socket.emit('webrtc_ice_candidate', {
                            callId: this.currentCallId,
                            candidate: event.candidate
                        });
                    } else if (!event.candidate) {
                        this.log('🧊 ICE gathering complete');
                    }
                };

                // Handle connection state changes
                this.peerConnection.onconnectionstatechange = () => {
                    this.log(`🔗 Connection state: ${this.peerConnection.connectionState}`);
                    
                    if (this.peerConnection.connectionState === 'connected') {
                        this.log('✅ WebRTC connection established successfully!');
                        this.updateCallStatus('active');
                        
                        // Start call timer when WebRTC connection is established
                        if (!this.durationInterval) {
                            this.startCallTimer();
                            this.log('⏱️ Call timer started');
                        }
                    } else if (this.peerConnection.connectionState === 'failed') {
                        this.log('❌ WebRTC connection failed', 'error');
                    } else if (this.peerConnection.connectionState === 'disconnected') {
                        this.log('⚠️ WebRTC connection disconnected');
                    }
                };
                
                // Handle ICE connection state changes
                this.peerConnection.oniceconnectionstatechange = () => {
                    this.log(`🧊 ICE connection state: ${this.peerConnection.iceConnectionState}`);
                };

                this.log('🔗 Peer connection setup complete');
            }

            async handleWebRTCOffer(data) {
                try {
                    this.log('📥 Processing incoming WebRTC offer...');
                    
                    // Setup peer connection if not already done
                    if (!this.peerConnection) {
                        this.log('📥 Setting up peer connection for incoming offer');
                        this.setupPeerConnection();
                    }
                    
                    // Add local stream if available (check if not already added)
                    if (this.localStream) {
                        const existingSenders = this.peerConnection.getSenders();
                        this.localStream.getTracks().forEach(track => {
                            const existingSender = existingSenders.find(sender => sender.track === track);
                            if (!existingSender) {
                                this.peerConnection.addTrack(track, this.localStream);
                                this.log(`📥 Added ${track.kind} track to peer connection (enabled: ${track.enabled}, muted: ${track.muted})`);
                            } else {
                                this.log(`📥 ${track.kind} track already exists in peer connection`);
                            }
                        });
                        
                        // Log current senders for debugging
                        const senders = this.peerConnection.getSenders();
                        this.log(`📊 Peer connection has ${senders.length} senders`);
                        senders.forEach((sender, index) => {
                            if (sender.track) {
                                this.log(`📊 Sender ${index}: ${sender.track.kind} track (enabled: ${sender.track.enabled})`);
                            }
                        });
                    } else {
                        this.log('⚠️ No local stream available when processing offer');
                    }

                    this.log('📥 Setting remote description...');
                    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(data.offer));
                    this.log('📥 Remote description set successfully');
                    
                    // Automatically create and send answer
                    this.log('📥 Creating WebRTC answer...');
                    const answer = await this.peerConnection.createAnswer();
                    
                    this.log('📥 Setting local description with answer...');
                    await this.peerConnection.setLocalDescription(answer);
                    
                    this.log(`📥 Sending WebRTC answer for call ${data.callId || this.currentCallId}`);
                    
                    if (this.currentCallId && this.socket) {
                        this.socket.emit('webrtc_answer', {
                            callId: this.currentCallId,
                            answer: answer
                        });
                        this.log('✅ WebRTC answer sent successfully');
                    } else {
                        this.log('❌ Cannot send answer: missing callId or socket', 'error');
                    }
                    
                } catch (error) {
                    this.log(`❌ Failed to handle WebRTC offer: ${error.message}`, 'error');
                    console.error('WebRTC offer handling error:', error);
                }
            }

            async handleWebRTCAnswer(data) {
                try {
                    this.log('📥 Processing WebRTC answer...');
                    if (this.peerConnection) {
                        await this.peerConnection.setRemoteDescription(new RTCSessionDescription(data.answer));
                        this.log('✅ WebRTC answer processed successfully');
                    } else {
                        this.log('❌ No peer connection available for answer', 'error');
                    }
                } catch (error) {
                    this.log(`❌ Failed to handle WebRTC answer: ${error.message}`, 'error');
                    console.error('WebRTC answer handling error:', error);
                }
            }

            async handleICECandidate(data) {
                try {
                    this.log('🧊 Processing ICE candidate...');
                    if (this.peerConnection) {
                        await this.peerConnection.addIceCandidate(new RTCIceCandidate(data.candidate));
                        this.log('✅ ICE candidate added successfully');
                    } else {
                        this.log('❌ No peer connection available for ICE candidate', 'error');
                    }
                } catch (error) {
                    this.log(`❌ Failed to handle ICE candidate: ${error.message}`, 'error');
                    console.error('ICE candidate error:', error);
                }
            }

            toggleAudio() {
                this.log('🎤 Toggle audio button clicked');
                
                if (!this.localStream) {
                    this.log('❌ No local stream available for audio toggle', 'error');
                    return;
                }
                
                const audioTracks = this.localStream.getAudioTracks();
                this.log(`📊 Found ${audioTracks.length} audio tracks`);
                
                if (audioTracks.length === 0) {
                    this.log('❌ No audio tracks found in local stream', 'error');
                    return;
                }
                
                const audioTrack = audioTracks[0];
                this.log(`🎤 Current audio track state: enabled=${audioTrack.enabled}, muted=${audioTrack.muted}, readyState=${audioTrack.readyState}`);
                
                // Toggle the audio track
                const previousState = audioTrack.enabled;
                audioTrack.enabled = !audioTrack.enabled;
                this.isAudioMuted = !audioTrack.enabled;
                
                this.log(`🎤 Audio track toggled: ${previousState} → ${audioTrack.enabled}`);
                
                // Update button text
                const newButtonText = this.isAudioMuted ? '🔇 Unmute Audio' : '🎤 Mute Audio';
                this.toggleAudioBtn.textContent = newButtonText;
                this.log(`🎤 Button text updated to: ${newButtonText}`);
                
                // Emit socket event if connected
                if (this.socket && this.currentCallId) {
                    this.socket.emit('toggle_audio', {
                        callId: this.currentCallId,
                        enabled: audioTrack.enabled
                    });
                    this.log(`📡 Sent toggle_audio event: enabled=${audioTrack.enabled}`);
                } else {
                    this.log('⚠️ No socket connection or call ID for audio toggle event');
                }
                
                // Verify the change took effect
                setTimeout(() => {
                    this.log(`🔍 Audio track verification: enabled=${audioTrack.enabled}, isAudioMuted=${this.isAudioMuted}`);
                }, 100);
            }

            toggleVideo() {
                if (this.localStream) {
                    const videoTrack = this.localStream.getVideoTracks()[0];
                    if (videoTrack) {
                        videoTrack.enabled = !videoTrack.enabled;
                        this.isVideoMuted = !videoTrack.enabled;
                        this.toggleVideoBtn.textContent = this.isVideoMuted ? '📹 Start Video' : '📹 Stop Video';
                        
                        if (this.socket && this.currentCallId) {
                            this.socket.emit('toggle_video', {
                                callId: this.currentCallId,
                                enabled: videoTrack.enabled
                            });
                        }
                        
                        this.log(`📹 Video ${videoTrack.enabled ? 'enabled' : 'disabled'}`);
                    }
                }
            }

            async shareScreen() {
                try {
                    const screenStream = await navigator.mediaDevices.getDisplayMedia({
                        video: true,
                        audio: true
                    });
                    
                    this.localVideo.srcObject = screenStream;
                    
                    // Replace video track in peer connection
                    if (this.peerConnection) {
                        const videoTrack = screenStream.getVideoTracks()[0];
                        const sender = this.peerConnection.getSenders().find(s => 
                            s.track && s.track.kind === 'video'
                        );
                        
                        if (sender) {
                            await sender.replaceTrack(videoTrack);
                        }
                    }
                    
                    this.log('🖥️ Screen sharing started');
                    
                } catch (error) {
                    this.log(`❌ Failed to share screen: ${error.message}`, 'error');
                }
            }

            closePeerConnection() {
                if (this.peerConnection) {
                    this.peerConnection.close();
                    this.peerConnection = null;
                }
                
                if (this.localStream) {
                    this.localStream.getTracks().forEach(track => track.stop());
                    this.localStream = null;
                    this.localVideo.srcObject = null;
                }
                
                if (this.remoteStream) {
                    this.remoteStream = null;
                    this.remoteVideo.srcObject = null;
                }
            }

            async makeAPICall(endpoint, method = 'GET', data = null) {
                const token = this.tokenInput.value.trim();
                if (!token) {
                    this.log('❌ No token available for API call', 'error');
                    return null;
                }

                try {
                    const options = {
                        method: method,
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    };

                    if (data && method !== 'GET') {
                        options.body = JSON.stringify(data);
                    }

                    const response = await fetch(`http://localhost:8000/api/calling/${endpoint}`, options);
                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || `HTTP ${response.status}`);
                    }

                    return result;
                } catch (error) {
                    this.log(`❌ API call failed: ${error.message}`, 'error');
                    return null;
                }
            }

            async getCallDetail() {
                const callId = this.testCallIdInput.value.trim() || this.currentCallId;
                if (!callId) {
                    this.log('❌ Please enter a call ID', 'error');
                    return;
                }

                this.log(`📋 Getting call detail for ${callId}`);
                const result = await this.makeAPICall(`${callId}/`);
                
                if (result) {
                    this.log(`📋 Call detail: ${JSON.stringify(result, null, 2)}`);
                }
            }

            async getCallHistory() {
                const conversationId = this.conversationIdInput.value.trim();
                if (!conversationId) {
                    this.log('❌ Please enter a conversation ID', 'error');
                    return;
                }

                this.log(`📚 Getting call history for conversation ${conversationId}`);
                const result = await this.makeAPICall(`history/?conversation_id=${conversationId}`);
                
                if (result) {
                    this.log(`📚 Call history: ${JSON.stringify(result, null, 2)}`);
                }
            }

            async reportCallQuality() {
                const callId = this.testCallIdInput.value.trim() || this.currentCallId;
                if (!callId) {
                    this.log('❌ Please enter a call ID', 'error');
                    return;
                }

                const qualityData = {
                    audioQuality: Math.floor(Math.random() * 5) + 1,
                    videoQuality: Math.floor(Math.random() * 5) + 1,
                    connectionQuality: Math.floor(Math.random() * 5) + 1,
                    latency: Math.floor(Math.random() * 200) + 50,
                    packetLoss: Math.random() * 5,
                    jitter: Math.random() * 20
                };

                this.log(`📊 Reporting call quality for ${callId}`);
                const result = await this.makeAPICall(`${callId}/quality/`, 'POST', qualityData);
                
                if (result) {
                    this.log(`📊 Quality report submitted: ${JSON.stringify(qualityData, null, 2)}`);
                }
            }

            debugRoom() {
                if (!this.socket || !this.currentCallId) {
                    this.log('❌ No active call to debug', 'error');
                    return;
                }

                const room = `call:${this.currentCallId}`;
                this.log(`🔍 Debugging room: ${room}`);
                
                this.socket.emit('debug_room_members', { room }, (response) => {
                    if (response.success) {
                        this.log(`🔍 Room ${room} members: ${JSON.stringify(response.members)}`);
                    } else {
                        this.log(`❌ Debug failed: ${response.error}`, 'error');
                    }
                });
            }

            clearLogs() {
                this.logOutput.textContent = 'Logs cleared...';
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${message}\n`;
                
                this.logOutput.textContent += logEntry;
                this.logOutput.scrollTop = this.logOutput.scrollHeight;
                
                // Also log to console
                if (type === 'error') {
                    console.error(message);
                } else {
                    console.log(message);
                }
            }
        }

        // Initialize the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Request notification permission
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }
            
            // Initialize the calling test app
            window.callingApp = new CallingTestApp();
        });
    </script>
</body>
</html>