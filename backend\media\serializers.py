# backend/media/serializers.py
from rest_framework import serializers
from .models import MediaFile, MediaDownload, MediaProcessingJob
from django.contrib.auth import get_user_model

User = get_user_model()

class UserBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'name', 'profile_picture']

class MediaFileSerializer(serializers.ModelSerializer):
    uploader = UserBasicSerializer(read_only=True)
    file_extension = serializers.ReadOnlyField()
    is_image = serializers.ReadOnlyField()
    is_video = serializers.ReadOnlyField()
    has_thumbnail = serializers.ReadOnlyField()
    
    class Meta:
        model = MediaFile
        fields = [
            'id', 'message', 'uploader', 'original_filename', 'file_type',
            'mime_type', 'file_size', 'wrapped_file_key', 'file_nonce',
            'thumbnail_nonce', 'processing_status', 'virus_scan_status',
            'created_at', 'updated_at', 'file_extension', 'is_image',
            'is_video', 'has_thumbnail'
        ]
        read_only_fields = [
            'id', 'uploader', 'processing_status', 'virus_scan_status',
            'created_at', 'updated_at'
        ]


class MediaDownloadSerializer(serializers.ModelSerializer):
    media_file = MediaFileSerializer(read_only=True)
    is_expired = serializers.ReadOnlyField()
    can_download = serializers.ReadOnlyField()
    
    class Meta:
        model = MediaDownload
        fields = [
            'id', 'media_file', 'download_token', 'expires_at',
            'download_count', 'max_downloads', 'last_downloaded_at',
            'created_at', 'is_expired', 'can_download'
        ]
        read_only_fields = [
            'id', 'download_token', 'download_count', 'last_downloaded_at',
            'created_at'
        ]


class MediaProcessingJobSerializer(serializers.ModelSerializer):
    class Meta:
        model = MediaProcessingJob
        fields = [
            'id', 'media_file', 'job_type', 'status', 'priority',
            'started_at', 'completed_at', 'error_message', 'result_data',
            'retry_count', 'max_retries', 'created_at'
        ]
        read_only_fields = [
            'id', 'started_at', 'completed_at', 'error_message',
            'result_data', 'retry_count', 'created_at'
        ]
