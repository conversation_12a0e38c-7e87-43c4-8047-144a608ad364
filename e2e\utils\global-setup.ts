import { chromium, FullConfig } from '@playwright/test';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';

const execAsync = promisify(exec);

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global E2E test setup...');

  try {
    // 1. Create test users in the database
    console.log('👥 Creating test users...');
    const setupScript = path.join(__dirname, '..', 'setup', 'create-test-user.py');
    
    try {
      const { stdout, stderr } = await execAsync(`python "${setupScript}"`);
      console.log(stdout);
      if (stderr) {
        console.warn('Setup warnings:', stderr);
      }
    } catch (error) {
      console.error('❌ Failed to create test users:', error);
      // Don't fail the entire setup if user creation fails
      // The tests will handle missing users gracefully
    }

    // 2. Verify services are running
    console.log('🔍 Checking if services are running...');
    
    const services = [
      { name: 'Frontend', url: 'http://localhost:5000', required: true },
      { name: 'Backend API', url: 'http://localhost:6000/api/auth/', required: true },
      { name: 'Socket Server', url: 'http://localhost:7000', required: true }
    ];

    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();

    for (const service of services) {
      try {
        console.log(`🔌 Checking ${service.name} at ${service.url}...`);
        
        const response = await page.goto(service.url, { 
          waitUntil: 'networkidle',
          timeout: 10000 
        });
        
        if (response && response.ok()) {
          console.log(`✅ ${service.name} is running`);
        } else {
          console.log(`⚠️ ${service.name} returned status: ${response?.status()}`);
          if (service.required) {
            throw new Error(`${service.name} is not responding properly`);
          }
        }
      } catch (error) {
        console.error(`❌ ${service.name} check failed:`, error);
        if (service.required) {
          console.error(`\n🚨 SETUP FAILED: ${service.name} is required but not running`);
          console.error('Please ensure all services are started before running E2E tests:');
          console.error('1. Backend: cd backend && python manage.py runserver 6000');
          console.error('2. Socket Server: cd socket-server && npm run dev');
          console.error('3. Frontend: cd frontend && npm run dev -- --port 5000');
          
          await browser.close();
          process.exit(1);
        }
      }
    }

    await browser.close();

    // 3. Create debug directories
    console.log('📁 Creating debug directories...');
    const fs = require('fs');
    const debugDirs = [
      'e2e/debug-screenshots',
      'e2e/reports',
      'e2e/test-results'
    ];

    for (const dir of debugDirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`📁 Created directory: ${dir}`);
      }
    }

    console.log('✅ Global setup completed successfully!');
    console.log('\n🎯 Ready to run E2E tests with the following configuration:');
    console.log('- Test user: <EMAIL> (testpass123)');
    console.log('- Frontend: http://localhost:5000');
    console.log('- Backend: http://localhost:6000');
    console.log('- Socket Server: http://localhost:7000');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    process.exit(1);
  }
}

export default globalSetup;
