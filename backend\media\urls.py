# backend/media/urls.py
from django.urls import path
from . import views

urlpatterns = [
    # Upload endpoints
    path('upload/chunked/', views.upload_media_chunked, name='upload_media_chunked'),
    path('upload/simple/', views.upload_media_simple, name='upload_media_simple'),

    # Download endpoints
    path('download/<uuid:media_id>/', views.download_media, name='download_media'),
    path('download/<str:download_token>/', views.secure_download, name='secure_download'),
    path('thumbnail/<uuid:media_id>/', views.get_thumbnail, name='get_thumbnail'),
]
