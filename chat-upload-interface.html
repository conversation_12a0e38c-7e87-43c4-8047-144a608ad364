<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Interface with File Upload</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <style>
        .chat-container {
            height: calc(100vh - 120px);
        }
        .progress-bar {
            transition: width 0.3s ease;
        }
        .file-upload-area {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }
        .file-upload-area:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        .file-upload-area.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="max-w-4xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- Header -->
        <div class="bg-blue-600 text-white p-4">
            <h1 class="text-xl font-semibold">Chat Interface</h1>
            <p class="text-blue-100 text-sm">Upload and share files with chunked upload</p>
        </div>

        <!-- Chat Messages Area -->
        <div id="chatContainer" class="chat-container overflow-y-auto p-4 space-y-4">
            <!-- Welcome Message -->
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                    S
                </div>
                <div class="bg-gray-200 rounded-lg p-3 max-w-xs">
                    <p class="text-sm">Welcome! You can upload files by selecting them below or dragging and dropping.</p>
                </div>
            </div>
        </div>

        <!-- Upload Progress Area -->
        <div id="uploadProgress" class="hidden p-4 bg-gray-50 border-t">
            <div class="flex items-center justify-between mb-2">
                <span id="uploadFileName" class="text-sm font-medium text-gray-700"></span>
                <span id="uploadPercentage" class="text-sm text-gray-500">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div id="progressBar" class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
            </div>
            <div id="uploadStatus" class="text-xs text-gray-500 mt-1">Preparing upload...</div>
        </div>

        <!-- File Upload Area -->
        <div class="p-4 border-t bg-gray-50">
            <!-- Drag and Drop Area -->
            <div id="dropArea" class="file-upload-area rounded-lg p-6 text-center mb-4 cursor-pointer">
                <div class="space-y-2">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <div class="text-sm text-gray-600">
                        <span class="font-medium text-blue-600 hover:text-blue-500">Click to upload</span>
                        or drag and drop
                    </div>
                    <p class="text-xs text-gray-500">PNG, JPG, PDF, DOC up to 100MB</p>
                </div>
            </div>

            <!-- File Input -->
            <input type="file" id="fileInput" class="hidden" multiple>
            
            <!-- Upload Button -->
            <div class="flex items-center justify-between">
                <button id="selectFileBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    Select Files
                </button>
                <div class="text-xs text-gray-500">
                    Max file size: 100MB
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:7000/api/media';
        const USER_ID = '5f95972a-8feb-46d8-a707-be39c84bc1ca'; // Valid user ID from database
        const CONVERSATION_ID = '987fcdeb-51a2-43d1-9f12-123456789abc'; // Example conversation ID
        const AUTH_TOKEN = 'test-token';
        const CHUNK_SIZE = 1024 * 1024; // 1MB chunks

        // DOM Elements
        const chatContainer = document.getElementById('chatContainer');
        const fileInput = document.getElementById('fileInput');
        const selectFileBtn = document.getElementById('selectFileBtn');
        const dropArea = document.getElementById('dropArea');
        const uploadProgress = document.getElementById('uploadProgress');
        const uploadFileName = document.getElementById('uploadFileName');
        const uploadPercentage = document.getElementById('uploadPercentage');
        const progressBar = document.getElementById('progressBar');
        const uploadStatus = document.getElementById('uploadStatus');

        // Event Listeners
        selectFileBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileSelect);
        dropArea.addEventListener('click', () => fileInput.click());
        
        // Drag and Drop Events
        dropArea.addEventListener('dragover', handleDragOver);
        dropArea.addEventListener('dragleave', handleDragLeave);
        dropArea.addEventListener('drop', handleDrop);

        function handleDragOver(e) {
            e.preventDefault();
            dropArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dropArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dropArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFiles(files);
            }
        }

        function handleFileSelect(e) {
            const files = e.target.files;
            if (files.length > 0) {
                handleFiles(files);
            }
        }

        function handleFiles(files) {
            Array.from(files).forEach(file => {
                if (file.size > 25 * 1024 * 1024) { // 25MB limit
                    addChatMessage('system', `File "${file.name}" is too large. Maximum size is 25MB.`, 'error');
                    return;
                }
                uploadFile(file);
            });
        }

        async function uploadFile(file) {
            try {
                // Show upload progress
                showUploadProgress(file.name);
                addChatMessage('user', `Uploading: ${file.name} (${formatFileSize(file.size)})`);

                // Step 1: Start chunked upload
                updateUploadStatus('Starting upload...');
                const uploadSession = await startChunkedUpload(file);
                
                // Step 2: Upload chunks with dynamic chunk size
                await uploadChunks(file, uploadSession.uploadSession, uploadSession.mediaFileId);
                
                // Upload completed
                hideUploadProgress();
                const fileData = {
                    mediaFileId: uploadSession.mediaFileId,
                    fileName: file.name
                };
                addChatMessage('system', `✅ File "${file.name}" uploaded successfully!`, 'success', fileData);
                
            } catch (error) {
                console.error('Upload error:', error);
                hideUploadProgress();
                addChatMessage('system', `❌ Failed to upload "${file.name}": ${error.message}`, 'error');
            }
        }

        async function startChunkedUpload(file) {
            const payload = {
                conversationId: CONVERSATION_ID,
                originalFilename: file.name,
                fileType: getFileType(file.type),
                mimeType: file.type,
                fileSize: file.size,
                wrappedFileKey: btoa('encrypted_file_key_placeholder'), // In real app, this would be encrypted
                fileNonce: btoa('nonce_placeholder'), // In real app, this would be a real nonce
                thumbnailNonce: btoa('thumbnail_nonce_placeholder')
            };

            const response = await fetch(`${API_BASE_URL}/upload/chunked/start`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${AUTH_TOKEN}`,
                    'X-User-Id': USER_ID
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Start upload error response:', errorText);
                
                // Check if response is JSON
                try {
                    const errorData = JSON.parse(errorText);
                    throw new Error(errorData.error || 'Failed to start upload');
                } catch (parseError) {
                    // If not JSON, it might be HTML error page
                    if (errorText.includes('<!DOCTYPE')) {
                        throw new Error('Server returned HTML instead of JSON. Check if the API endpoint is correct and the server is running properly.');
                    }
                    throw new Error(`Server error: ${response.status} ${response.statusText}`);
                }
            }

            return await response.json();
        }

        async function uploadChunks(file, uploadSession, mediaFileId) {
            // Calculate chunk size as file size / 100 to create 100 chunks
            const chunkSize = Math.ceil(file.size / 100);
            const totalChunks = Math.ceil(file.size / chunkSize);
            
            addChatMessage('system', `File will be uploaded in ${totalChunks} chunks (${formatFileSize(chunkSize)} per chunk)`);
            
            for (let chunkNumber = 0; chunkNumber < totalChunks; chunkNumber++) {
                const start = chunkNumber * chunkSize;
                const end = Math.min(start + chunkSize, file.size);
                const chunk = file.slice(start, end);
                
                // Calculate chunk hash
                const chunkHash = await calculateSHA256(chunk);
                
                // Update progress
                const progress = ((chunkNumber + 1) / totalChunks) * 100;
                updateUploadProgress(progress);
                updateUploadStatus(`Uploading chunk ${chunkNumber + 1} of ${totalChunks}...`);
                
                // Upload chunk
                const formData = new FormData();
                formData.append('chunk', chunk);
                formData.append('uploadSession', uploadSession);
                formData.append('chunkNumber', chunkNumber.toString());
                formData.append('totalChunks', totalChunks.toString());
                formData.append('chunkHash', chunkHash);
                formData.append('mediaFileId', mediaFileId);

                const response = await fetch(`${API_BASE_URL}/upload/chunked/chunk`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        'X-User-Id': USER_ID
                    },
                    body: formData
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Chunk upload error response:', errorText);
                    
                    // Check if response is JSON
                    try {
                        const errorData = JSON.parse(errorText);
                        throw new Error(errorData.error || `Failed to upload chunk ${chunkNumber + 1}`);
                    } catch (parseError) {
                        // If not JSON, it might be HTML error page
                        if (errorText.includes('<!DOCTYPE')) {
                            throw new Error('Server returned HTML instead of JSON. Check if the API endpoint is correct and the server is running properly.');
                        }
                        throw new Error(`Server error: ${response.status} ${response.statusText}`);
                    }
                }

                const result = await response.json();
                if (result.isComplete) {
                    updateUploadStatus('Upload completed!');
                    break;
                }
            }
        }

        async function calculateSHA256(data) {
            const buffer = await data.arrayBuffer();
            const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        function getFileType(mimeType) {
            if (mimeType.startsWith('image/')) return 'image';
            if (mimeType.startsWith('video/')) return 'video';
            if (mimeType.startsWith('audio/')) return 'audio';
            if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) return 'document';
            if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) return 'archive';
            return 'other';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showUploadProgress(fileName) {
            uploadFileName.textContent = fileName;
            uploadProgress.classList.remove('hidden');
            updateUploadProgress(0);
        }

        function hideUploadProgress() {
            uploadProgress.classList.add('hidden');
        }

        function updateUploadProgress(percentage) {
            const roundedPercentage = Math.round(percentage);
            uploadPercentage.textContent = `${roundedPercentage}%`;
            progressBar.style.width = `${percentage}%`;
        }

        function updateUploadStatus(status) {
            uploadStatus.textContent = status;
        }

        function addChatMessage(sender, message, type = 'normal', fileData = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3';

            const avatar = document.createElement('div');
            avatar.className = 'w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'rounded-lg p-3 max-w-xs';

            if (sender === 'user') {
                messageDiv.className += ' justify-end';
                avatar.className += ' bg-green-500';
                avatar.textContent = 'U';
                messageContent.className += ' bg-blue-600 text-white';
            } else {
                avatar.className += type === 'error' ? ' bg-red-500' : type === 'success' ? ' bg-green-500' : ' bg-gray-500';
                avatar.textContent = 'S';
                messageContent.className += type === 'error' ? ' bg-red-100 text-red-800' : 
                                          type === 'success' ? ' bg-green-100 text-green-800' : ' bg-gray-200';
            }

            const messageText = document.createElement('p');
            messageText.className = 'text-sm';
            messageText.textContent = message;

            messageContent.appendChild(messageText);

            // Add download button for successful file uploads
            if (type === 'success' && fileData && fileData.mediaFileId) {
                const downloadButton = document.createElement('button');
                downloadButton.className = 'mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors';
                downloadButton.textContent = 'Download';
                downloadButton.onclick = () => downloadFile(fileData.mediaFileId, fileData.fileName);
                messageContent.appendChild(downloadButton);
            }
            
            if (sender === 'user') {
                messageDiv.appendChild(messageContent);
                messageDiv.appendChild(avatar);
            } else {
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(messageContent);
            }

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        async function downloadFile(mediaFileId, fileName) {
            try {
                updateUploadStatus('Creating download token...');
                
                // Step 3: Create download token
                const tokenResponse = await fetch(`${API_BASE_URL}/download/token/${mediaFileId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        'x-user-id': USER_ID
                    },
                    body: JSON.stringify({
                        maxDownloads: 5
                    })
                });

                if (!tokenResponse.ok) {
                    const errorText = await tokenResponse.text();
                    console.error('Token creation error response:', errorText);
                    
                    try {
                        const errorData = JSON.parse(errorText);
                        throw new Error(errorData.error || `Failed to create download token: ${tokenResponse.status}`);
                    } catch (parseError) {
                        if (errorText.includes('<!DOCTYPE')) {
                            throw new Error('Server returned HTML instead of JSON. Check if the API endpoint is correct and the server is running properly.');
                        }
                        throw new Error(`Server error: ${tokenResponse.status} ${tokenResponse.statusText}`);
                    }
                }

                const tokenData = await tokenResponse.json();
                const downloadToken = tokenData.downloadToken;
                
                updateUploadStatus('Downloading file...');
                
                // Step 4: Download file using token
                const downloadResponse = await fetch(`${API_BASE_URL}/stream/${downloadToken}`, {
                    method: 'GET'
                });

                if (!downloadResponse.ok) {
                    const errorText = await downloadResponse.text();
                    console.error('Download error response:', errorText);
                    throw new Error(`Failed to download file: ${downloadResponse.status} ${downloadResponse.statusText}`);
                }

                // Create blob and download
                const blob = await downloadResponse.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                updateUploadStatus('File downloaded successfully!');
                setTimeout(() => {
                    hideUploadProgress();
                }, 2000);
                
                addChatMessage('system', `📥 File "${fileName}" downloaded successfully!`, 'success');
                
            } catch (error) {
                console.error('Download error:', error);
                updateUploadStatus('Download failed!');
                setTimeout(() => {
                    hideUploadProgress();
                }, 2000);
                addChatMessage('system', `❌ Failed to download "${fileName}": ${error.message}`, 'error');
            }
        }

        // Initialize
        console.log('Chat interface initialized');
        console.log('API Base URL:', API_BASE_URL);
    </script>
</body>
</html>