// socket-server/src/tests/services/conversationService.test.ts
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { mockDeep, mockReset, DeepMockProxy } from 'vitest-mock-extended'
import { ConversationService } from '../../services/conversationService'
import { ConversationCreate } from '../../schemas'

describe('ConversationService', () => {
  let conversationService: ConversationService
  let prismaMock: DeepMockProxy<PrismaClient>

  const mockUser1 = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    username: 'user1',
    email: '<EMAIL>',
    password: 'hashedpassword1',
    last_login: null,
    is_superuser: false,
    is_staff: false,
    is_active: true,
    date_joined: new Date('2023-01-01T00:00:00Z'),
    firstName: 'User',
    lastName: 'One',
    profilePicture: null,
    isVerified: true,
    lastSeen: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  }

  const mockUser2 = {
    id: '123e4567-e89b-12d3-a456-426614174004',
    username: 'user2',
    email: '<EMAIL>',
    password: 'hashedpassword2',
    last_login: null,
    is_superuser: false,
    is_staff: false,
    is_active: true,
    date_joined: new Date('2023-01-01T00:00:00Z'),
    firstName: 'User',
    lastName: 'Two',
    profilePicture: 'https://example.com/avatar.jpg',
    isVerified: true,
    lastSeen: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  }

  const mockConversation = {
    id: '123e4567-e89b-12d3-a456-426614174001',
    type: 'DIRECT',
    name: null,
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
    participants: [
      {
        id: 'participant-1',
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        userId: '123e4567-e89b-12d3-a456-426614174000',
        role: 'MEMBER',
        joinedAt: new Date('2023-01-01T00:00:00Z'),
        user: mockUser1,
      },
      {
        id: 'participant-2',
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        userId: '123e4567-e89b-12d3-a456-426614174004',
        role: 'MEMBER',
        joinedAt: new Date('2023-01-01T00:00:00Z'),
        user: mockUser2,
      },
    ],
    messages: [
      {
        id: 'msg-1',
        content: 'Hello!',
        createdAt: new Date('2023-01-01T01:00:00Z'),
        sender: {
          id: '123e4567-e89b-12d3-a456-426614174000',
          username: 'user1',
          firstName: 'User',
          lastName: 'One',
        },
      },
    ],
  }

  const mockGroupConversation = {
    id: '123e4567-e89b-12d3-a456-426614174006',
    type: 'GROUP',
    name: 'Test Group',
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
    participants: [
      {
        id: 'participant-3',
        conversationId: '123e4567-e89b-12d3-a456-426614174006',
        userId: '123e4567-e89b-12d3-a456-426614174000',
        role: 'ADMIN',
        joinedAt: new Date('2023-01-01T00:00:00Z'),
        user: mockUser1,
      },
    ],
    messages: [],
  }

  beforeEach(() => {
    prismaMock = mockDeep<PrismaClient>()
    conversationService = new ConversationService(prismaMock)
  })

  afterEach(() => {
    mockReset(prismaMock)
  })

  describe('getUserConversations', () => {
    it('should fetch user conversations with last messages', async () => {
      prismaMock.conversation.findMany.mockResolvedValue([mockConversation, mockGroupConversation])

      const result = await conversationService.getUserConversations('123e4567-e89b-12d3-a456-426614174000')

      expect(result).toHaveLength(2)
      expect(result[0]).toEqual({
        ...mockConversation,
        lastMessage: mockConversation.messages[0],
      })
      expect(result[1]).toEqual({
        ...mockGroupConversation,
        lastMessage: null,
      })

      expect(prismaMock.conversation.findMany).toHaveBeenCalledWith({
        where: {
          participants: {
            some: {
              userId: '123e4567-e89b-12d3-a456-426614174000',
            },
          },
        },
        include: {
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                  profilePicture: true,
                },
              },
            },
          },
          messages: {
            take: 1,
            orderBy: {
              createdAt: 'desc',
            },
            include: {
              sender: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
      })
    })

    it('should handle empty conversations list', async () => {
      prismaMock.conversation.findMany.mockResolvedValue([])

      const result = await conversationService.getUserConversations('123e4567-e89b-12d3-a456-426614174000')

      expect(result).toEqual([])
    })

    it('should handle database errors', async () => {
      prismaMock.conversation.findMany.mockRejectedValue(new Error('Database error'))

      await expect(
        conversationService.getUserConversations('123e4567-e89b-12d3-a456-426614174000')
      ).rejects.toThrow('Database error')
    })

    it('should handle conversations without messages', async () => {
      const conversationWithoutMessages = {
        ...mockConversation,
        messages: [],
      }

      prismaMock.conversation.findMany.mockResolvedValue([conversationWithoutMessages])

      const result = await conversationService.getUserConversations('123e4567-e89b-12d3-a456-426614174000')

      expect(result[0].lastMessage).toBeNull()
    })
  })

  describe('createConversation', () => {
    const validDirectConversationData: ConversationCreate = {
      type: 'DIRECT',
      participantIds: ['123e4567-e89b-12d3-a456-426614174004'],
    }

    const validGroupConversationData: ConversationCreate = {
      type: 'GROUP',
      name: 'Test Group',
      participantIds: ['123e4567-e89b-12d3-a456-426614174004', '123e4567-e89b-12d3-a456-426614174005'],
    }

    it('should create a direct conversation successfully', async () => {
      // Mock that no existing conversation exists
      prismaMock.conversation.findFirst.mockResolvedValue(null)
      
      // Mock conversation creation
      prismaMock.conversation.create.mockResolvedValue(mockConversation)
      
      // Mock participant creation
      prismaMock.conversationParticipant.create
        .mockResolvedValueOnce({
          id: 'participant-1',
          conversationId: '123e4567-e89b-12d3-a456-426614174001',
          userId: '123e4567-e89b-12d3-a456-426614174000',
          role: 'MEMBER',
          joinedAt: new Date(),
        })
        .mockResolvedValueOnce({
          id: 'participant-2',
          conversationId: '123e4567-e89b-12d3-a456-426614174001',
          userId: '123e4567-e89b-12d3-a456-426614174004',
          role: 'MEMBER',
          joinedAt: new Date(),
        })

      const result = await conversationService.createConversation(
        validDirectConversationData,
        '123e4567-e89b-12d3-a456-426614174000'
      )

      expect(result).toEqual(mockConversation)
      expect(prismaMock.conversation.create).toHaveBeenCalledWith({
        data: {
          type: 'DIRECT',
          name: null,
        },
        include: {
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                  profilePicture: true,
                },
              },
            },
          },
        },
      })
      expect(prismaMock.conversationParticipant.create).toHaveBeenCalledTimes(2)
    })

    it('should return existing direct conversation if it exists', async () => {
      prismaMock.conversation.findFirst.mockResolvedValue(mockConversation)

      const result = await conversationService.createConversation(
        validDirectConversationData,
        '123e4567-e89b-12d3-a456-426614174000'
      )

      expect(result).toEqual(mockConversation)
      expect(prismaMock.conversation.create).not.toHaveBeenCalled()
      expect(prismaMock.conversationParticipant.create).not.toHaveBeenCalled()
    })

    it('should create a group conversation successfully', async () => {
      prismaMock.conversation.create.mockResolvedValue(mockGroupConversation)
      prismaMock.conversationParticipant.create
        .mockResolvedValueOnce({
          id: 'participant-1',
          conversationId: '123e4567-e89b-12d3-a456-426614174006',
          userId: '123e4567-e89b-12d3-a456-426614174000',
          role: 'ADMIN',
          joinedAt: new Date(),
        })
        .mockResolvedValueOnce({
          id: 'participant-2',
          conversationId: '123e4567-e89b-12d3-a456-426614174006',
          userId: '123e4567-e89b-12d3-a456-426614174004',
          role: 'MEMBER',
          joinedAt: new Date(),
        })
        .mockResolvedValueOnce({
          id: 'participant-3',
          conversationId: '123e4567-e89b-12d3-a456-426614174006',
          userId: '123e4567-e89b-12d3-a456-426614174005',
          role: 'MEMBER',
          joinedAt: new Date(),
        })

      const result = await conversationService.createConversation(
        validGroupConversationData,
        '123e4567-e89b-12d3-a456-426614174000'
      )

      expect(result).toEqual(mockGroupConversation)
      expect(prismaMock.conversation.create).toHaveBeenCalledWith({
        data: {
          type: 'GROUP',
          name: 'Test Group',
        },
        include: expect.any(Object),
      })
      expect(prismaMock.conversationParticipant.create).toHaveBeenCalledTimes(3)
      
      // Verify creator gets ADMIN role for group
      expect(prismaMock.conversationParticipant.create).toHaveBeenCalledWith({
        data: {
          conversationId: '123e4567-e89b-12d3-a456-426614174006',
          userId: '123e4567-e89b-12d3-a456-426614174000',
          role: 'ADMIN',
        },
      })
    })

    it('should validate conversation data', async () => {
      const invalidData = {
        type: 'INVALID_TYPE',
        participantIds: [],
      } as unknown as ConversationCreate

      await expect(
        conversationService.createConversation(invalidData, '123e4567-e89b-12d3-a456-426614174000')
      ).rejects.toThrow()

      expect(prismaMock.conversation.create).not.toHaveBeenCalled()
    })

    it('should handle database errors during conversation creation', async () => {
      prismaMock.conversation.findFirst.mockResolvedValue(null)
      prismaMock.conversation.create.mockRejectedValue(new Error('Database error'))

      await expect(
        conversationService.createConversation(validDirectConversationData, '123e4567-e89b-12d3-a456-426614174000')
      ).rejects.toThrow('Database error')
    })

    it('should handle database errors during participant creation', async () => {
      prismaMock.conversation.findFirst.mockResolvedValue(null)
      prismaMock.conversation.create.mockResolvedValue(mockConversation)
      prismaMock.conversationParticipant.create.mockRejectedValue(new Error('Participant error'))

      await expect(
        conversationService.createConversation(validDirectConversationData, '123e4567-e89b-12d3-a456-426614174000')
      ).rejects.toThrow('Participant error')
    })

    it('should not add creator twice if they are in participant list', async () => {
      const dataWithCreatorInList: ConversationCreate = {
        type: 'DIRECT',
        participantIds: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174004'], // Creator is in the list
      }

      prismaMock.conversation.findFirst.mockResolvedValue(null)
      prismaMock.conversation.create.mockResolvedValue(mockConversation)
      prismaMock.conversationParticipant.create
        .mockResolvedValueOnce({
          id: 'participant-1',
          conversationId: '123e4567-e89b-12d3-a456-426614174001',
          userId: '123e4567-e89b-12d3-a456-426614174000',
          role: 'MEMBER',
          joinedAt: new Date(),
        })
        .mockResolvedValueOnce({
          id: 'participant-2',
          conversationId: '123e4567-e89b-12d3-a456-426614174001',
          userId: '123e4567-e89b-12d3-a456-426614174004',
          role: 'MEMBER',
          joinedAt: new Date(),
        })

      await conversationService.createConversation(dataWithCreatorInList, '123e4567-e89b-12d3-a456-426614174000')

      // Should only create 2 participants, not 3
      expect(prismaMock.conversationParticipant.create).toHaveBeenCalledTimes(2)
    })

    it('should handle empty participant list', async () => {
      const invalidData: ConversationCreate = {
        type: 'DIRECT',
        participantIds: [],
      }

      await expect(
        conversationService.createConversation(invalidData, '123e4567-e89b-12d3-a456-426614174000')
      ).rejects.toThrow()
    })

    it('should handle group conversation without name', async () => {
      const groupDataWithoutName: ConversationCreate = {
        type: 'GROUP',
        participantIds: ['123e4567-e89b-12d3-a456-426614174004'],
      }

      prismaMock.conversation.create.mockResolvedValue({
        ...mockGroupConversation,
        name: null,
      })
      prismaMock.conversationParticipant.create.mockResolvedValue({
        id: 'participant-1',
        conversationId: '123e4567-e89b-12d3-a456-426614174006',
        userId: '123e4567-e89b-12d3-a456-426614174000',
        role: 'ADMIN',
        joinedAt: new Date(),
      })

      const result = await conversationService.createConversation(
        groupDataWithoutName,
        '123e4567-e89b-12d3-a456-426614174000'
      )

      expect(prismaMock.conversation.create).toHaveBeenCalledWith({
        data: {
          type: 'GROUP',
          name: null,
        },
        include: expect.any(Object),
      })
    })
  })

  describe('joinConversation', () => {
    it('should return true when user is participant', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue({
        id: 'participant-1',
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        userId: '123e4567-e89b-12d3-a456-426614174000',
        role: 'MEMBER',
        joinedAt: new Date(),
      })

      const result = await conversationService.joinConversation('123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001')

      expect(result).toBe(true)
      expect(prismaMock.conversationParticipant.findFirst).toHaveBeenCalledWith({
        where: {
          conversationId: '123e4567-e89b-12d3-a456-426614174001',
          userId: '123e4567-e89b-12d3-a456-426614174000',
        },
      })
    })

    it('should return false when user is not participant', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(null)

      const result = await conversationService.joinConversation('123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001')

      expect(result).toBe(false)
    })

    it('should return false on database errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      prismaMock.conversationParticipant.findFirst.mockRejectedValue(new Error('Database error'))

      const result = await conversationService.joinConversation('123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001')

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith('Error joining conversation:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })

  describe('findDirectConversation (private method)', () => {
    it('should find existing direct conversation between two users', async () => {
      // This tests the private method indirectly through createConversation
      prismaMock.conversation.findFirst.mockResolvedValue(mockConversation)

      const result = await conversationService.createConversation(
        { type: 'DIRECT', participantIds: ['123e4567-e89b-12d3-a456-426614174004'] },
        '123e4567-e89b-12d3-a456-426614174000'
      )

      expect(result).toEqual(mockConversation)
      expect(prismaMock.conversation.findFirst).toHaveBeenCalledWith({
        where: {
          type: 'DIRECT',
          participants: {
            every: {
              userId: {
                in: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174004'],
              },
            },
          },
        },
        include: {
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                  profilePicture: true,
                },
              },
            },
          },
        },
      })
    })
  })
})
