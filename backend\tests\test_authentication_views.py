# backend/tests/test_authentication_views.py
import pytest
import json
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch

from tests.factories import UserFactory

User = get_user_model()


@pytest.mark.django_db
class TestAuthenticationViews:
    """Test authentication endpoints"""

    def setup_method(self):
        """Set up test data"""
        self.client = APIClient()
        self.register_url = reverse('register')
        self.login_url = reverse('login')
        self.profile_url = reverse('profile')

    def test_register_success(self):
        """Test successful user registration"""
        data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.register_url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['success'] is True
        assert 'data' in response.data
        assert 'user' in response.data['data']
        assert 'tokens' in response.data['data']
        assert response.data['data']['user']['email'] == '<EMAIL>'
        assert response.data['data']['user']['username'] == 'testuser'
        
        # Verify user was created in database
        user = User.objects.get(email='<EMAIL>')
        assert user.username == 'testuser'
        assert user.first_name == 'Test'
        assert user.last_name == 'User'

    def test_register_duplicate_email(self):
        """Test registration with duplicate email"""
        UserFactory(email='<EMAIL>')
        
        data = {
            'email': '<EMAIL>',
            'username': 'testuser2',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.register_url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
        assert 'User with this email already exists' in response.data['error']

    def test_register_duplicate_username(self):
        """Test registration with duplicate username"""
        UserFactory(username='testuser')
        
        data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.register_url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
        assert 'User with this username already exists' in response.data['error']

    def test_register_invalid_data(self):
        """Test registration with invalid data"""
        data = {
            'email': 'invalid-email',
            'username': '',
            'first_name': '',
            'last_name': '',
            'password': '123'  # Too short
        }
        
        response = self.client.post(self.register_url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'errors' in response.data

    def test_register_missing_fields(self):
        """Test registration with missing required fields"""
        data = {
            'email': '<EMAIL>'
            # Missing other required fields
        }
        
        response = self.client.post(self.register_url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'errors' in response.data

    def test_login_success(self):
        """Test successful login"""
        user = UserFactory(email='<EMAIL>', password='testpass123')
        user.set_password('testpass123')
        user.save()
        
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert 'data' in response.data
        assert 'user' in response.data['data']
        assert 'tokens' in response.data['data']
        assert response.data['data']['user']['email'] == '<EMAIL>'

    def test_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        UserFactory(email='<EMAIL>', password='testpass123')
        
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert response.data['success'] is False
        assert 'Invalid credentials' in response.data['error']

    def test_login_nonexistent_user(self):
        """Test login with non-existent user"""
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert response.data['success'] is False
        assert 'Invalid credentials' in response.data['error']

    def test_login_invalid_data(self):
        """Test login with invalid data"""
        data = {
            'email': 'invalid-email',
            'password': ''
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'errors' in response.data

    def test_profile_success(self):
        """Test successful profile retrieval"""
        user = UserFactory()
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        response = self.client.get(self.profile_url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert 'data' in response.data
        assert str(response.data['data']['id']) == str(user.id)
        assert response.data['data']['email'] == user.email
        assert response.data['data']['username'] == user.username

    def test_profile_unauthenticated(self):
        """Test profile retrieval without authentication"""
        response = self.client.get(self.profile_url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_profile_invalid_token(self):
        """Test profile retrieval with invalid token"""
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid-token')
        
        response = self.client.get(self.profile_url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @patch('authentication.views.RefreshToken.for_user')
    def test_register_token_generation_error(self, mock_token):
        """Test registration when token generation fails"""
        mock_token.side_effect = Exception("Token generation failed")
        
        data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.register_url, data, format='json')
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert 'error' in response.data

    @patch('authentication.views.authenticate')
    def test_login_authentication_error(self, mock_auth):
        """Test login when authentication fails with exception"""
        mock_auth.side_effect = Exception("Authentication failed")
        
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert 'error' in response.data

    def test_case_insensitive_email_login(self):
        """Test login with case-insensitive email"""
        user = UserFactory(email='<EMAIL>')
        user.set_password('testpass123')
        user.save()
        
        data = {
            'email': '<EMAIL>',  # lowercase
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True

    def test_response_format_camel_case(self):
        """Test that responses use camelCase format"""
        user = UserFactory()
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        response = self.client.get(self.profile_url)
        
        assert response.status_code == status.HTTP_200_OK
        # Check camelCase fields
        assert 'firstName' in response.data['data']
        assert 'lastName' in response.data['data']
        assert 'profilePicture' in response.data['data']
