// frontend/src/store/__tests__/index.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { store, type RootState } from '../index'
import { api } from '../../services/api'
import { addMessage } from '../slices/messageSlice'
import { setSelectedConversation } from '../slices/conversationSlice'

// Mock localStorage for persistence
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

describe('Redux Store', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('store configuration', () => {
    it('should have correct initial state structure', () => {
      const state = store.getState()
      
      expect(state).toHaveProperty('messages')
      expect(state).toHaveProperty('conversations')
      expect(state).toHaveProperty('api')
      
      // Check initial state values
      expect(state.messages.messages).toEqual({})
      expect(state.messages.loading).toBe(false)
      expect(state.messages.error).toBe(null)
      
      expect(state.conversations.conversations).toEqual([])
      expect(state.conversations.selectedConversationId).toBe(null)
      expect(state.conversations.draftConversations).toEqual([])
      
      expect(state.api.queries).toBeDefined()
      expect(state.api.mutations).toBeDefined()
      expect(typeof state.api.queries).toBe('object')
      expect(typeof state.api.mutations).toBe('object')
    })

    it('should have correct reducer paths', () => {
      const state = store.getState()
      
      expect(state).toHaveProperty('messages')
      expect(state).toHaveProperty('conversations')
      expect(state).toHaveProperty(api.reducerPath)
    })

    it('should include RTK Query middleware', () => {
      // Test that RTK Query middleware is working by dispatching an API action
      const mockMessage = {
        id: 'msg-1',
        conversationId: 'conv-1',
        sender: {
          id: 'user-1',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          profile_picture: null,
        },
        content: 'Hello world!',
        messageType: 'TEXT' as const,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      }

      // This should work without errors if middleware is properly configured
      store.dispatch(addMessage(mockMessage))
      
      const state = store.getState()
      expect(state.messages.messages['conv-1']).toHaveLength(1)
    })
  })

  describe('state persistence', () => {
    it('should not persist state by default (persistence not configured)', () => {
      // Since persistence middleware is not configured, localStorage should not be called
      store.dispatch(setSelectedConversation('conv-1'))

      // No persistence middleware means no localStorage calls
      expect(mockLocalStorage.setItem).not.toHaveBeenCalled()
    })

    it('should handle state changes without persistence', () => {
      // Dispatch an action and verify state changes work without persistence
      store.dispatch(setSelectedConversation('conv-1'))

      const state = store.getState()
      expect(state.conversations.selectedConversationId).toBe('conv-1')
    })

    it('should handle localStorage availability gracefully', () => {
      // Even if localStorage is not available, the store should work
      expect(() => {
        store.dispatch(setSelectedConversation('conv-2'))
      }).not.toThrow()

      const state = store.getState()
      expect(state.conversations.selectedConversationId).toBe('conv-2')
    })
  })

  describe('type safety', () => {
    it('should have correct RootState type', () => {
      const state: RootState = store.getState()

      // TypeScript should enforce correct types
      expect(typeof state.messages.loading).toBe('boolean')
      // selectedConversationId can be null (object) or string
      expect(['object', 'string'].includes(typeof state.conversations.selectedConversationId)).toBe(true)
      expect(Array.isArray(state.conversations.conversations)).toBe(true)
      expect(Array.isArray(state.conversations.draftConversations)).toBe(true)
    })

    it('should provide correct dispatch types', () => {
      // These should compile without TypeScript errors
      store.dispatch(addMessage({
        id: 'msg-1',
        conversationId: 'conv-1',
        sender: {
          id: 'user-1',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          profile_picture: null,
        },
        content: 'Test message',
        messageType: 'TEXT',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      }))

      store.dispatch(setSelectedConversation('conv-1'))
      store.dispatch(setSelectedConversation(null))
    })
  })

  describe('middleware integration', () => {
    it('should handle async actions', async () => {
      // Test RTK Query async actions instead of createAsyncThunk
      const { messageApi } = await import('../../services/messageApi')

      // Dispatch an RTK Query action
      const promise = store.dispatch(
        messageApi.endpoints.getMessages.initiate({ conversationId: 'conv-1' })
      )

      // The action should be dispatched without errors
      expect(promise).toBeDefined()

      // Clean up the subscription
      promise.unsubscribe()
    })

    it('should handle RTK Query cache updates', () => {
      // Test that RTK Query cache is properly integrated
      const state1 = store.getState()
      expect(state1.api.queries).toBeDefined()
      expect(typeof state1.api.queries).toBe('object')

      // After dispatching an RTK Query action, the cache should be updated
      // This is tested indirectly through the API slice tests
    })
  })

  describe('dev tools integration', () => {
    it('should be configurable for development', () => {
      // In development, Redux DevTools should be enabled
      // This is more of a configuration test
      expect(store).toBeDefined()
      expect(typeof store.dispatch).toBe('function')
      expect(typeof store.getState).toBe('function')
      expect(typeof store.subscribe).toBe('function')
    })
  })

  describe('error handling', () => {
    it('should handle invalid actions gracefully', () => {
      const initialState = store.getState()

      // Dispatch an action with invalid type (should be ignored)
      expect(() => {
        store.dispatch({
          type: 'invalid/action',
          payload: 'invalid',
        })
      }).not.toThrow()

      // State should remain unchanged
      const currentState = store.getState()
      expect(currentState.messages).toEqual(initialState.messages)
      expect(currentState.conversations).toEqual(initialState.conversations)
    })

    it('should maintain state consistency with valid actions', () => {
      const initialState = store.getState()

      // Dispatch a valid action
      store.dispatch(setSelectedConversation('conv-1'))

      const currentState = store.getState()
      expect(currentState.conversations.selectedConversationId).toBe('conv-1')
      // Other state should remain unchanged
      expect(currentState.messages).toEqual(initialState.messages)
    })
  })

  describe('performance', () => {
    it('should handle multiple rapid dispatches', () => {
      const startTime = performance.now()
      
      // Dispatch many actions rapidly
      for (let i = 0; i < 100; i++) {
        store.dispatch(addMessage({
          id: `msg-${i}`,
          conversationId: 'conv-1',
          sender: {
            id: 'user-1',
            username: 'testuser',
            first_name: 'Test',
            last_name: 'User',
            profile_picture: null,
          },
          content: `Message ${i}`,
          messageType: 'TEXT',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }))
      }
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // Should complete within reasonable time (less than 1 second)
      expect(duration).toBeLessThan(1000)
      
      // All messages should be added
      const state = store.getState()
      expect(state.messages.messages['conv-1']).toHaveLength(100)
    })

    it('should not cause memory leaks with large state', () => {
      const initialMemory = process.memoryUsage().heapUsed
      
      // Add a large number of messages
      for (let i = 0; i < 1000; i++) {
        store.dispatch(addMessage({
          id: `msg-${i}`,
          conversationId: `conv-${i % 10}`, // Spread across 10 conversations
          sender: {
            id: 'user-1',
            username: 'testuser',
            first_name: 'Test',
            last_name: 'User',
            profile_picture: null,
          },
          content: `Message ${i}`,
          messageType: 'TEXT',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }))
      }
      
      const finalMemory = process.memoryUsage().heapUsed
      const memoryIncrease = finalMemory - initialMemory
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024)
    })
  })

  describe('subscription handling', () => {
    it('should notify subscribers of state changes', () => {
      const subscriber = vi.fn()
      const unsubscribe = store.subscribe(subscriber)
      
      store.dispatch(setSelectedConversation('conv-1'))
      
      expect(subscriber).toHaveBeenCalled()
      
      unsubscribe()
    })

    it('should handle unsubscription correctly', () => {
      const subscriber = vi.fn()
      const unsubscribe = store.subscribe(subscriber)
      
      unsubscribe()
      
      store.dispatch(setSelectedConversation('conv-1'))
      
      // Subscriber should not be called after unsubscription
      expect(subscriber).not.toHaveBeenCalled()
    })
  })
})
