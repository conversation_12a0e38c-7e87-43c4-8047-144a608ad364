// frontend/src/components/Chat/MediaFilePreview.tsx
import React from 'react';
import { File, Image, Video, Music, Archive, FileText, CheckCircle, XCircle } from 'lucide-react';

interface MediaFilePreviewProps {
  fileName: string;
  fileSize: number;
  fileType: 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other';
  mimeType: string;
  progress: number;
  status: 'pending' | 'encrypting' | 'uploading' | 'completed' | 'error';
  error?: string;
  preview?: string;
  onRetry?: () => void;
  onCancel?: () => void;
}

/**
 * Circular progress indicator component
 */
const CircularProgress: React.FC<{ progress: number; size?: number; strokeWidth?: number }> = ({ 
  progress, 
  size = 48, 
  strokeWidth = 4 
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (progress / 100) * circumference;

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-gray-200"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={circumference}
          strokeDashoffset={offset}
          className="text-blue-500 transition-all duration-300 ease-out"
          strokeLinecap="round"
        />
      </svg>
      {/* Progress percentage */}
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="text-xs font-semibold text-gray-700">
          {Math.round(progress)}%
        </span>
      </div>
    </div>
  );
};

/**
 * Get file icon based on file type
 */
const getFileIcon = (fileType: string, className: string = "w-6 h-6") => {
  switch (fileType) {
    case 'image':
      return <Image className={className} />;
    case 'video':
      return <Video className={className} />;
    case 'audio':
      return <Music className={className} />;
    case 'document':
      return <FileText className={className} />;
    case 'archive':
      return <Archive className={className} />;
    default:
      return <File className={className} />;
  }
};

/**
 * Format file size to human-readable format
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return Math.round((bytes / Math.pow(k, i)) * 100) / 100 + ' ' + sizes[i];
};

/**
 * Media file preview component with circular progress indicator
 * Displays file information and upload progress in the chat screen
 */
export const MediaFilePreview: React.FC<MediaFilePreviewProps> = ({
  fileName,
  fileSize,
  fileType,
  mimeType,
  progress,
  status,
  error,
  preview,
  onRetry,
  onCancel
}) => {
  const isUploading = status === 'uploading' || status === 'encrypting';
  const isCompleted = status === 'completed';
  const isError = status === 'error';

  return (
    <div className="flex items-center space-x-3 p-3 bg-white border rounded-lg shadow-sm hover:shadow-md transition-shadow">
      {/* File preview or icon with progress */}
      <div className="flex-shrink-0 relative">
        {preview && fileType === 'image' ? (
          <div className="relative">
            <img 
              src={preview} 
              alt={fileName}
              className="w-12 h-12 object-cover rounded"
            />
            {isUploading && (
              <div className="absolute inset-0 bg-black bg-opacity-50 rounded flex items-center justify-center">
                <CircularProgress progress={progress} size={40} strokeWidth={3} />
              </div>
            )}
          </div>
        ) : (
          <div className="w-12 h-12 flex items-center justify-center bg-gray-100 rounded">
            {isUploading ? (
              <CircularProgress progress={progress} size={40} strokeWidth={3} />
            ) : isCompleted ? (
              <CheckCircle className="w-8 h-8 text-green-500" />
            ) : isError ? (
              <XCircle className="w-8 h-8 text-red-500" />
            ) : (
              <div className="text-gray-500">
                {getFileIcon(fileType, "w-8 h-8")}
              </div>
            )}
          </div>
        )}
      </div>

      {/* File information */}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate" title={fileName}>
          {fileName}
        </p>
        <div className="flex items-center space-x-2 mt-1">
          <p className="text-xs text-gray-500">
            {formatFileSize(fileSize)}
          </p>
          {isUploading && (
            <>
              <span className="text-gray-300">•</span>
              <p className="text-xs text-blue-600 font-medium">
                Uploading {progress}%
              </p>
            </>
          )}
          {isCompleted && (
            <>
              <span className="text-gray-300">•</span>
              <p className="text-xs text-green-600 font-medium">
                Completed
              </p>
            </>
          )}
        </div>
        
        {/* Error message */}
        {error && (
          <p className="text-xs text-red-500 mt-1">{error}</p>
        )}

        {/* Progress bar (alternative to circular progress) */}
        {isUploading && (
          <div className="mt-2">
            <div className="bg-gray-200 rounded-full h-1.5 overflow-hidden">
              <div 
                className="bg-blue-500 h-full rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex-shrink-0">
        {isError && onRetry && (
          <button
            onClick={onRetry}
            className="px-3 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
          >
            Retry
          </button>
        )}
        {onCancel && !isCompleted && (
          <button
            onClick={onCancel}
            className="ml-2 text-gray-400 hover:text-red-500 transition-colors"
            title="Cancel upload"
          >
            <XCircle className="w-5 h-5" />
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * Compact version for message bubbles
 */
export const MediaFilePreviewCompact: React.FC<MediaFilePreviewProps> = ({
  fileName,
  fileSize,
  fileType,
  progress,
  status,
  preview
}) => {
  const isUploading = status === 'uploading' || status === 'encrypting';

  return (
    <div className="inline-flex items-center space-x-2 px-3 py-2 bg-gray-50 rounded-lg">
      <div className="flex-shrink-0">
        {preview && fileType === 'image' ? (
          <img 
            src={preview} 
            alt={fileName}
            className="w-8 h-8 object-cover rounded"
          />
        ) : (
          <div className="text-gray-500">
            {getFileIcon(fileType, "w-5 h-5")}
          </div>
        )}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium text-gray-900 truncate">
          {fileName}
        </p>
        <p className="text-xs text-gray-500">
          {formatFileSize(fileSize)}
        </p>
      </div>
      {isUploading && (
        <CircularProgress progress={progress} size={32} strokeWidth={3} />
      )}
    </div>
  );
};

export default MediaFilePreview;

