# Generated by Django 5.2.4 on 2025-09-26 15:32

from django.db import migrations


def populate_name_field(apps, schema_editor):
    """
    Populate the 'name' field with data from 'first_name' and 'last_name' fields.
    """
    User = apps.get_model('users', 'User')

    for user in User.objects.all():
        # Combine first_name and last_name into name field
        name_parts = []
        if user.first_name:
            name_parts.append(user.first_name.strip())
        if user.last_name:
            name_parts.append(user.last_name.strip())

        # Set name field, fallback to email if no name parts
        user.name = ' '.join(name_parts) if name_parts else user.email.split('@')[0]
        user.save(update_fields=['name'])


def reverse_populate_name_field(apps, schema_editor):
    """
    Reverse migration: split 'name' field back into 'first_name' and 'last_name'.
    """
    User = apps.get_model('users', 'User')

    for user in User.objects.all():
        if user.name:
            name_parts = user.name.strip().split(' ', 1)
            user.first_name = name_parts[0] if len(name_parts) > 0 else ''
            user.last_name = name_parts[1] if len(name_parts) > 1 else ''
            user.save(update_fields=['first_name', 'last_name'])


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_add_name_field'),
    ]

    operations = [
        migrations.RunPython(populate_name_field, reverse_populate_name_field),
    ]
