// socket-server/src/events/debugEvents.ts
import { Socket, Server } from 'socket.io';
import { AuthenticatedSocket } from '../middleware/auth';

/**
 * Debug events handler for troubleshooting WebRTC connections
 * These events should only be available in development mode
 */
export const registerDebugEvents = (io: Server, socket: AuthenticatedSocket): void => {
  // Get room members for debugging
  socket.on('debug_room_members', async (data: { room: string }, callback: Function) => {
    try {
      const { room } = data;
      
      if (!room) {
        return callback({ success: false, error: 'Room name is required' });
      }
      
      // Get all sockets in the room
      const sockets = await socket.in(room).fetchSockets();
      const members = sockets.map(s => {
        const socket = s as unknown as AuthenticatedSocket;
        return socket.userId || s.id;
      });
      
      console.log(`🔍 Debug: Room ${room} has ${members.length} members: ${members.join(', ')}`);
      
      callback({ success: true, members });
    } catch (error) {
      console.error('Error getting room members:', error);
      callback({ success: false, error: 'Failed to get room members' });
    }
  });
  
  // Manually emit call_active event for debugging
  socket.on('debug_emit_call_active', async (data: { callId: string }, callback?: Function) => {
    try {
      const { callId } = data;
      
      if (!callId) {
        if (callback) callback({ success: false, error: 'Call ID is required' });
        return;
      }
      
      const callRoom = `call:${callId}`;
      
      // Get all sockets in the room
      const sockets = await socket.in(callRoom).fetchSockets();
      const members = sockets.map(s => {
        const socket = s as unknown as AuthenticatedSocket;
        return socket.userId || s.id;
      });
      
      console.log(`🔧 Debug: Manually emitting call_active event to room ${callRoom}`);
      console.log(`🔧 Debug: Room members: ${members.join(', ')}`);
      
      // Emit call_active event to the room
      io.to(callRoom).emit('call_active', { callId });
      
      if (callback) callback({ success: true });
    } catch (error) {
      console.error('Error manually emitting call_active event:', error);
      if (callback) callback({ success: false, error: 'Failed to emit call_active event' });
    }
  });
};