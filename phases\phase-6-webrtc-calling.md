# Phase 6: WebRTC Audio/Video Calling with End-to-End Encryption

**Duration**: 4-5 weeks | **Priority**: Medium

## Overview
This phase implements secure real-time audio and video calling capabilities using WebRTC technology with end-to-end encryption, including encrypted call management, secure signaling server, and call quality optimization.

## Prerequisites
- Phase 5 completed successfully
- **Phase 3 encryption infrastructure fully implemented**
- Media sharing functionality working with E2EE
- Socket.io server stable with encrypted message routing
- Understanding of WebRTC concepts and encryption protocols
- STUN/TURN server access for production
- Signal Protocol implementation from Phase 3 available

## WebRTC Architecture with End-to-End Encryption

### Components
- **Encrypted Signaling Server**: Handles encrypted call initiation and WebRTC signaling
- **STUN Server**: Helps with NAT traversal
- **TURN Server**: Relays media when direct connection fails
- **Media Server**: Optional for group calls (future enhancement)
- **Call Encryption Layer**: Secure call metadata and signaling encryption

### Encrypted Call Flow
1. Caller initiates encrypted call request using existing E2EE session
2. Signaling server routes encrypted call notification to callee
3. Encrypted WebRTC peer connection establishment
4. Encrypted ICE candidate exchange
5. Secure media stream negotiation
6. Call establishment or termination with encrypted state updates

## Encryption Integration

### Call Encryption Architecture
Building upon Phase 3's Signal Protocol implementation, WebRTC calls incorporate:

#### 1. Encrypted Call Signaling
- **Call Initiation**: Encrypted call requests using existing E2EE sessions
- **WebRTC Offer/Answer**: SDP data encrypted before transmission
- **ICE Candidates**: Connection data encrypted for privacy
- **Call State Updates**: All call status changes encrypted

#### 2. Secure Call Metadata
- **Call Records**: Encrypted storage of call history and quality metrics
- **Participant Information**: Encrypted caller/callee details
- **Call Duration**: Encrypted timing information
- **Quality Metrics**: Encrypted performance data

#### 3. Privacy-First Design
- **Server Blindness**: Server cannot see call content or detailed metadata
- **Encrypted Storage**: All call data encrypted at rest
- **Secure Transport**: All signaling over encrypted channels
- **Forward Secrecy**: Call session keys rotated for security

### Encryption Implementation Details

#### Call Signaling Encryption
```typescript
// Encrypted call signaling using existing E2EE session
interface EncryptedCallSignal {
  conversationId: string;
  encryptedPayload: string; // Encrypted call data
  messageCounter: number;   // For replay protection
  timestamp: number;
}

// Call data structure (before encryption)
interface CallSignalData {
  type: 'call_offer' | 'call_answer' | 'ice_candidate' | 'call_end';
  callId: string;
  callType: 'audio' | 'video';
  sdpData?: string;        // WebRTC SDP offer/answer
  iceCandidate?: RTCIceCandidate;
  callerId?: string;
  calleeId?: string;
}
```

#### Encrypted Call Storage
```python
# Enhanced Call model with encryption fields
class EncryptedCall(models.Model):
    # ... existing fields ...
    
    # Encrypted call metadata
    encrypted_metadata = models.TextField()  # Encrypted call details
    metadata_key_id = models.CharField(max_length=64)  # Key reference
    
    # Encrypted quality metrics
    encrypted_quality_data = models.TextField(null=True, blank=True)
    quality_key_id = models.CharField(max_length=64, null=True, blank=True)
    
    # Privacy fields
    caller_encrypted_id = models.TextField()  # Encrypted caller reference
    callee_encrypted_id = models.TextField()  # Encrypted callee reference
```

## Database Schema Updates

### Step 1: Encrypted Call Management Models

```python
# backend/apps/calling/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class Call(models.Model):
    CALL_TYPES = [
        ('audio', 'Audio Call'),
        ('video', 'Video Call'),
    ]
    
    CALL_STATUS = [
        ('initiated', 'Initiated'),
        ('ringing', 'Ringing'),
        ('answered', 'Answered'),
        ('active', 'Active'),
        ('ended', 'Ended'),
        ('missed', 'Missed'),
        ('declined', 'Declined'),
        ('failed', 'Failed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey('messaging.Conversation', on_delete=models.CASCADE, related_name='calls')
    caller = models.ForeignKey(User, on_delete=models.CASCADE, related_name='initiated_calls')
    callee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_calls')
    
    call_type = models.CharField(max_length=10, choices=CALL_TYPES)
    status = models.CharField(max_length=15, choices=CALL_STATUS, default='initiated')
    
    # Timing
    initiated_at = models.DateTimeField(auto_now_add=True)
    answered_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    
    # Encrypted duration for privacy
    encrypted_duration = models.TextField(null=True, blank=True)
    duration_key_id = models.CharField(max_length=64, null=True, blank=True)
    
    # Encrypted WebRTC session data
    session_id = models.CharField(max_length=100, unique=True)
    encrypted_caller_sdp = models.TextField(blank=True, null=True)
    encrypted_callee_sdp = models.TextField(blank=True, null=True)
    sdp_key_id = models.CharField(max_length=64, null=True, blank=True)
    
    # Encrypted call quality metrics
    encrypted_quality_data = models.TextField(null=True, blank=True)
    quality_key_id = models.CharField(max_length=64, null=True, blank=True)
    
    # Encrypted metadata
    encrypted_metadata = models.TextField(null=True, blank=True)
    metadata_key_id = models.CharField(max_length=64, null=True, blank=True)
    
    # Encryption session reference
    encryption_session_id = models.CharField(max_length=100, null=True, blank=True)
    
    class Meta:
        db_table = 'calls'
        ordering = ['-initiated_at']
        indexes = [
            models.Index(fields=['conversation', 'status']),
            models.Index(fields=['caller', 'initiated_at']),
            models.Index(fields=['callee', 'initiated_at']),
            models.Index(fields=['encryption_session_id']),
        ]
    
    def __str__(self):
        return f"{self.call_type.title()} call from {self.caller.username} to {self.callee.username}"

class CallParticipant(models.Model):
    """For future group calling support"""
    PARTICIPANT_STATUS = [
        ('invited', 'Invited'),
        ('joined', 'Joined'),
        ('left', 'Left'),
        ('declined', 'Declined'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    call = models.ForeignKey(Call, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='call_participations')
    status = models.CharField(max_length=15, choices=PARTICIPANT_STATUS, default='invited')
    
    joined_at = models.DateTimeField(null=True, blank=True)
    left_at = models.DateTimeField(null=True, blank=True)
    
    # Media state
    audio_enabled = models.BooleanField(default=True)
    video_enabled = models.BooleanField(default=True)
    screen_sharing = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'call_participants'
        unique_together = ['call', 'user']

class CallEvent(models.Model):
    """Track call events for debugging and analytics"""
    EVENT_TYPES = [
        ('call_initiated', 'Call Initiated'),
        ('call_ringing', 'Call Ringing'),
        ('call_answered', 'Call Answered'),
        ('call_ended', 'Call Ended'),
        ('call_declined', 'Call Declined'),
        ('call_failed', 'Call Failed'),
        ('ice_candidate', 'ICE Candidate'),
        ('media_toggle', 'Media Toggle'),
        ('quality_issue', 'Quality Issue'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    call = models.ForeignKey(Call, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=20, choices=EVENT_TYPES)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='call_events')
    event_data = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'call_events'
        ordering = ['timestamp']

class CallQualityMetric(models.Model):
    """Store encrypted call quality metrics"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    call = models.ForeignKey(Call, on_delete=models.CASCADE, related_name='quality_metrics')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='call_quality_reports')
    
    # Encrypted quality metrics (all sensitive data encrypted)
    encrypted_network_metrics = models.TextField(null=True, blank=True)  # packet_loss, jitter, rtt, bandwidth
    encrypted_audio_metrics = models.TextField(null=True, blank=True)   # audio_level, quality_score
    encrypted_video_metrics = models.TextField(null=True, blank=True)   # resolution, framerate, quality_score
    
    # Encryption key references
    network_metrics_key_id = models.CharField(max_length=64, null=True, blank=True)
    audio_metrics_key_id = models.CharField(max_length=64, null=True, blank=True)
    video_metrics_key_id = models.CharField(max_length=64, null=True, blank=True)
    
    recorded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'call_quality_metrics'
        indexes = [
            models.Index(fields=['call', 'recorded_at']),
            models.Index(fields=['user', 'recorded_at']),
        ]
```

### Step 2: Encrypted Call API Implementation

```python
# backend/apps/calling/views.py
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction

from .models import Call, CallEvent, CallQualityMetric
from .serializers import CallSerializer, CallEventSerializer
from messaging.models import Conversation
from encryption.utils import (
    encrypt_call_data, decrypt_call_data,
    get_conversation_encryption_session,
    generate_call_encryption_key
)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def initiate_call(request):
    """Initiate a new encrypted call"""
    conversation_id = request.data.get('conversation_id')
    call_type = request.data.get('call_type', 'audio')
    
    if call_type not in ['audio', 'video']:
        return Response({'error': 'Invalid call type'}, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        conversation = Conversation.objects.get(id=conversation_id, type='direct')
        
        # Check if user is participant
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Not a participant in this conversation'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        # Verify encryption session exists for this conversation
        encryption_session = get_conversation_encryption_session(conversation, request.user)
        if not encryption_session:
            return Response({'error': 'No encryption session found for this conversation'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # Get the other participant (callee)
        callee = conversation.participants.exclude(user=request.user).first().user
        
        # Check if there's already an active call
        active_call = Call.objects.filter(
            conversation=conversation,
            status__in=['initiated', 'ringing', 'answered', 'active']
        ).first()
        
        if active_call:
            return Response({'error': 'Call already in progress'}, 
                          status=status.HTTP_409_CONFLICT)
        
        with transaction.atomic():
            # Generate encryption keys for call data
            call_encryption_key = generate_call_encryption_key()
            
            # Prepare call metadata for encryption
            call_metadata = {
                'call_type': call_type,
                'caller_id': str(request.user.id),
                'callee_id': str(callee.id),
                'initiated_timestamp': timezone.now().isoformat()
            }
            
            # Encrypt call metadata
            encrypted_metadata, metadata_key_id = encrypt_call_data(
                call_metadata, encryption_session
            )
            
            # Create encrypted call record
            call = Call.objects.create(
                conversation=conversation,
                caller=request.user,
                callee=callee,
                call_type=call_type,
                session_id=f"call_{conversation.id}_{int(timezone.now().timestamp())}",
                encrypted_call_metadata=encrypted_metadata,
                metadata_encryption_key_id=metadata_key_id,
                encryption_session_id=encryption_session.session_id
            )
            
            # Create encrypted call event
            event_data = {'call_type': call_type, 'encrypted': True}
            encrypted_event_data, event_key_id = encrypt_call_data(
                event_data, encryption_session
            )
            
            CallEvent.objects.create(
                call=call,
                event_type='call_initiated',
                user=request.user,
                encrypted_event_data=encrypted_event_data,
                event_key_id=event_key_id
            )
            
            # Notify callee via encrypted WebSocket message
            from .socket_handlers import notify_encrypted_incoming_call
            notify_encrypted_incoming_call(call, encryption_session)
        
        return Response(CallSerializer(call).data, status=status.HTTP_201_CREATED)
        
    except Conversation.DoesNotExist:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': 'Failed to initiate encrypted call'}, 
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def answer_call(request, call_id):
    """Answer an incoming call"""
    try:
        call = Call.objects.get(id=call_id, callee=request.user, status='ringing')
        
        with transaction.atomic():
            call.status = 'answered'
            call.answered_at = timezone.now()
            call.save()
            
            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_answered',
                user=request.user
            )
            
            # Notify caller via WebSocket
            from .socket_handlers import notify_call_answered
            notify_call_answered(call)
        
        return Response(CallSerializer(call).data)
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found or cannot be answered'}, 
                       status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def decline_call(request, call_id):
    """Decline an incoming call"""
    try:
        call = Call.objects.get(id=call_id, callee=request.user, status__in=['initiated', 'ringing'])
        
        with transaction.atomic():
            call.status = 'declined'
            call.ended_at = timezone.now()
            call.save()
            
            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_declined',
                user=request.user
            )
            
            # Notify caller via WebSocket
            from .socket_handlers import notify_call_declined
            notify_call_declined(call)
        
        return Response({'message': 'Call declined'})
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def end_call(request, call_id):
    """End an active call"""
    try:
        call = Call.objects.get(
            id=call_id,
            status__in=['answered', 'active'],
            participants__user=request.user
        )
        
        with transaction.atomic():
            call.status = 'ended'
            call.ended_at = timezone.now()
            call.save()
            
            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_ended',
                user=request.user
            )
            
            # Notify other participant via WebSocket
            from .socket_handlers import notify_call_ended
            notify_call_ended(call, request.user)
        
        return Response({'message': 'Call ended'})
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_call_sdp(request, call_id):
    """Update SDP offer/answer for WebRTC negotiation"""
    try:
        call = Call.objects.get(id=call_id)
        
        # Check if user is participant
        if request.user not in [call.caller, call.callee]:
            return Response({'error': 'Not a participant in this call'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        sdp_type = request.data.get('type')  # 'offer' or 'answer'
        sdp_data = request.data.get('sdp')
        
        if not sdp_type or not sdp_data:
            return Response({'error': 'SDP type and data required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # Update appropriate SDP field
        if request.user == call.caller:
            call.caller_sdp = sdp_data
        else:
            call.callee_sdp = sdp_data
        
        call.save()
        
        # Notify other participant via WebSocket
        from .socket_handlers import notify_sdp_update
        notify_sdp_update(call, request.user, sdp_type, sdp_data)
        
        return Response({'message': 'SDP updated'})
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def report_call_quality(request, call_id):
    """Report call quality metrics"""
    try:
        call = Call.objects.get(id=call_id)
        
        # Check if user is participant
        if request.user not in [call.caller, call.callee]:
            return Response({'error': 'Not a participant in this call'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        # Create quality metric record
        CallQualityMetric.objects.create(
            call=call,
            user=request.user,
            packet_loss=request.data.get('packet_loss'),
            jitter=request.data.get('jitter'),
            round_trip_time=request.data.get('round_trip_time'),
            bandwidth_upload=request.data.get('bandwidth_upload'),
            bandwidth_download=request.data.get('bandwidth_download'),
            audio_level=request.data.get('audio_level'),
            audio_quality_score=request.data.get('audio_quality_score'),
            video_resolution=request.data.get('video_resolution'),
            video_framerate=request.data.get('video_framerate'),
            video_quality_score=request.data.get('video_quality_score')
        )
        
        return Response({'message': 'Quality metrics recorded'})
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_call_history(request):
    """Get user's call history"""
    calls = Call.objects.filter(
        models.Q(caller=request.user) | models.Q(callee=request.user)
    ).order_by('-initiated_at')[:50]  # Last 50 calls
    
    return Response(CallSerializer(calls, many=True).data)
```

### Step 3: Encrypted WebRTC Socket Handlers

```javascript
// frontend/src/utils/callEncryption.js
import { getEncryptionSession } from './encryptionUtils';

export async function encryptCallSignaling(data, encryptionSession) {
  try {
    const jsonData = JSON.stringify(data);
    const encryptedData = await encryptionSession.encrypt(jsonData);
    return {
      encryptedPayload: encryptedData,
      messageCounter: encryptionSession.getMessageCounter(),
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Failed to encrypt call signaling:', error);
    throw error;
  }
}

export async function decryptCallSignaling(encryptedMessage, encryptionSession) {
  try {
    const decryptedJson = await encryptionSession.decrypt(encryptedMessage.encryptedPayload);
    return JSON.parse(decryptedJson);
  } catch (error) {
    console.error('Failed to decrypt call signaling:', error);
    throw error;
  }
}
```

### Step 3: Encrypted WebRTC Socket Handlers

```javascript
// socket-server/src/handlers/encryptedCallHandlers.js
const { encryptCallSignaling, decryptCallSignaling } = require('../utils/callEncryption');
const { getUserEncryptionSession } = require('../utils/encryptionUtils');

class EncryptedCallHandler {
  constructor(io, redisClient) {
    this.io = io;
    this.redisClient = redisClient;
    this.activeCalls = new Map(); // Store active encrypted call sessions
  }

  handleConnection(socket) {
    // Verify user has encryption session
    socket.on('verify_encryption_session', async (data) => {
      try {
        const encryptionSession = await getUserEncryptionSession(socket.userId);
        if (!encryptionSession) {
          socket.emit('encryption_error', { message: 'No encryption session found' });
          return;
        }
        socket.encryptionSession = encryptionSession;
        socket.emit('encryption_verified', { success: true });
      } catch (error) {
        socket.emit('encryption_error', { message: 'Encryption verification failed' });
      }
    });

    // Handle encrypted call initiation
    socket.on('initiate_encrypted_call', async (encryptedData) => {
      try {
        if (!socket.encryptionSession) {
          socket.emit('call_error', { message: 'Encryption session required' });
          return;
        }

        // Decrypt call initiation data
        const decryptedData = await decryptCallSignaling(encryptedData, socket.encryptionSession);
        const { callId, calleeId, callType, conversationId } = decryptedData;
        
        // Get callee's encryption session
        const calleeEncryptionSession = await getUserEncryptionSession(calleeId);
        if (!calleeEncryptionSession) {
          socket.emit('call_error', { message: 'Callee encryption session not found' });
          return;
        }
        
        // Store encrypted call session
        this.activeCalls.set(callId, {
          callerId: socket.userId,
          calleeId,
          callType,
          conversationId,
          status: 'initiated',
          callerSocketId: socket.id,
          callerEncryptionSession: socket.encryptionSession,
          calleeEncryptionSession
        });
        
        // Prepare encrypted notification for callee
        const callNotification = {
          callId,
          callerId: socket.userId,
          callType,
          conversationId,
          timestamp: new Date().toISOString(),
          encrypted: true
        };
        
        const encryptedNotification = await encryptCallSignaling(
          callNotification, 
          calleeEncryptionSession
        );
        
        // Notify callee with encrypted message
        socket.to(`user_${calleeId}`).emit('encrypted_incoming_call', {
          encryptedData: encryptedNotification
        });
        
        // Update call status to ringing after delay
        setTimeout(() => {
          const call = this.activeCalls.get(callId);
          if (call && call.status === 'initiated') {
            call.status = 'ringing';
            const ringingData = { callId, status: 'ringing' };
            const encryptedRinging = encryptCallSignaling(ringingData, call.callerEncryptionSession);
            this.io.to(`user_${call.callerId}`).emit('encrypted_call_ringing', {
              encryptedData: encryptedRinging
            });
          }
        }, 1000);
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to initiate encrypted call' });
      }
    });

    // Handle encrypted call answer
    socket.on('answer_encrypted_call', async (encryptedData) => {
      try {
        if (!socket.encryptionSession) {
          socket.emit('call_error', { message: 'Encryption session required' });
          return;
        }

        const decryptedData = await decryptCallSignaling(encryptedData, socket.encryptionSession);
        const { callId } = decryptedData;
        const call = this.activeCalls.get(callId);
        
        if (!call || call.calleeId !== socket.userId) {
          socket.emit('call_error', { message: 'Call not found or unauthorized' });
          return;
        }
        
        call.status = 'answered';
        call.calleeSocketId = socket.id;
        
        // Prepare encrypted answer notification
        const answerData = {
          callId,
          calleeId: socket.userId,
          status: 'answered',
          timestamp: new Date().toISOString()
        };
        
        const encryptedAnswer = await encryptCallSignaling(answerData, call.callerEncryptionSession);
        
        // Notify caller that call was answered
        this.io.to(call.callerSocketId).emit('encrypted_call_answered', {
          encryptedData: encryptedAnswer
        });
        
        // Both participants join the encrypted call room
        socket.join(`encrypted_call_${callId}`);
        this.io.sockets.sockets.get(call.callerSocketId)?.join(`encrypted_call_${callId}`);
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to answer encrypted call' });
      }
    });

    // Handle call decline
    socket.on('decline_call', async (data) => {
      try {
        const { callId } = data;
        const call = this.activeCalls.get(callId);
        
        if (!call || call.calleeId !== socket.userId) {
          socket.emit('call_error', { message: 'Call not found or unauthorized' });
          return;
        }
        
        // Notify caller that call was declined
        this.io.to(call.callerSocketId).emit('call_declined', { callId });
        
        // Clean up call session
        this.activeCalls.delete(callId);
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to decline call' });
      }
    });

    // Handle call end
    socket.on('end_call', async (data) => {
      try {
        const { callId } = data;
        const call = this.activeCalls.get(callId);
        
        if (!call) {
          socket.emit('call_error', { message: 'Call not found' });
          return;
        }
        
        // Notify other participant
        socket.to(`call_${callId}`).emit('call_ended', {
          callId,
          endedBy: socket.userId
        });
        
        // Clean up call session
        this.activeCalls.delete(callId);
        
        // Remove participants from call room
        this.io.in(`call_${callId}`).socketsLeave(`call_${callId}`);
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to end call' });
      }
    });

    // Handle encrypted WebRTC signaling
    socket.on('encrypted_webrtc_offer', async (encryptedData) => {
      try {
        if (!socket.encryptionSession) {
          socket.emit('call_error', { message: 'Encryption session required' });
          return;
        }

        const decryptedData = await decryptCallSignaling(encryptedData, socket.encryptionSession);
        const { callId, offer } = decryptedData;
        const call = this.activeCalls.get(callId);
        
        if (!call) {
          socket.emit('call_error', { message: 'Call session not found' });
          return;
        }
        
        // Encrypt offer for the other participant
        const offerData = {
          callId,
          offer,
          from: socket.userId,
          type: 'webrtc_offer'
        };
        
        const targetEncryptionSession = socket.userId === call.callerId ? 
          call.calleeEncryptionSession : call.callerEncryptionSession;
        
        const encryptedOffer = await encryptCallSignaling(offerData, targetEncryptionSession);
        
        socket.to(`encrypted_call_${callId}`).emit('encrypted_webrtc_offer', {
          encryptedData: encryptedOffer
        });
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to process encrypted WebRTC offer' });
      }
    });

    socket.on('encrypted_webrtc_answer', async (encryptedData) => {
      try {
        if (!socket.encryptionSession) {
          socket.emit('call_error', { message: 'Encryption session required' });
          return;
        }

        const decryptedData = await decryptCallSignaling(encryptedData, socket.encryptionSession);
        const { callId, answer } = decryptedData;
        const call = this.activeCalls.get(callId);
        
        if (!call) {
          socket.emit('call_error', { message: 'Call session not found' });
          return;
        }
        
        // Encrypt answer for the other participant
        const answerData = {
          callId,
          answer,
          from: socket.userId,
          type: 'webrtc_answer'
        };
        
        const targetEncryptionSession = socket.userId === call.callerId ? 
          call.calleeEncryptionSession : call.callerEncryptionSession;
        
        const encryptedAnswer = await encryptCallSignaling(answerData, targetEncryptionSession);
        
        socket.to(`encrypted_call_${callId}`).emit('encrypted_webrtc_answer', {
          encryptedData: encryptedAnswer
        });
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to process encrypted WebRTC answer' });
      }
    });

    socket.on('encrypted_webrtc_ice_candidate', async (encryptedData) => {
      try {
        if (!socket.encryptionSession) {
          socket.emit('call_error', { message: 'Encryption session required' });
          return;
        }

        const decryptedData = await decryptCallSignaling(encryptedData, socket.encryptionSession);
        const { callId, candidate } = decryptedData;
        const call = this.activeCalls.get(callId);
        
        if (!call) {
          socket.emit('call_error', { message: 'Call session not found' });
          return;
        }
        
        // Encrypt ICE candidate for the other participant
        const candidateData = {
          callId,
          candidate,
          from: socket.userId,
          type: 'webrtc_ice_candidate'
        };
        
        const targetEncryptionSession = socket.userId === call.callerId ? 
          call.calleeEncryptionSession : call.callerEncryptionSession;
        
        const encryptedCandidate = await encryptCallSignaling(candidateData, targetEncryptionSession);
        
        socket.to(`encrypted_call_${callId}`).emit('encrypted_webrtc_ice_candidate', {
          encryptedData: encryptedCandidate
        });
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to process encrypted ICE candidate' });
      }
    });

    // Handle media state changes
    socket.on('toggle_audio', (data) => {
      const { callId, enabled } = data;
      socket.to(`call_${callId}`).emit('participant_audio_toggle', {
        userId: socket.userId,
        enabled
      });
    });

    socket.on('toggle_video', (data) => {
      const { callId, enabled } = data;
      socket.to(`call_${callId}`).emit('participant_video_toggle', {
        userId: socket.userId,
        enabled
      });
    });

    // Handle call quality reports
    socket.on('call_quality_report', (data) => {
      const { callId, metrics } = data;
      // Store metrics or forward to analytics service
      console.log(`Call quality report for ${callId}:`, metrics);
    });

    // Handle disconnection during call
    socket.on('disconnect', () => {
      // Find and clean up any active calls for this user
      for (const [callId, call] of this.activeCalls.entries()) {
        if (call.callerSocketId === socket.id || call.calleeSocketId === socket.id) {
          // Notify other participant about disconnection
          socket.to(`call_${callId}`).emit('call_ended', {
            callId,
            reason: 'participant_disconnected',
            endedBy: socket.userId
          });
          
          // Clean up
          this.activeCalls.delete(callId);
          this.io.in(`call_${callId}`).socketsLeave(`call_${callId}`);
        }
      }
    });
  }

  // Helper method to notify about incoming calls
  notifyIncomingCall(call) {
    this.io.to(`user_${call.callee.id}`).emit('incoming_call', {
      callId: call.id,
      caller: {
        id: call.caller.id,
        username: call.caller.username,
        first_name: call.caller.first_name,
        last_name: call.caller.last_name,
        profile_picture: call.caller.profile_picture
      },
      callType: call.call_type,
      timestamp: call.initiated_at
    });
  }
}

module.exports = EncryptedCallHandler;
```

### Step 4: Frontend Encrypted WebRTC Implementation

```typescript
// frontend/src/utils/encryptedWebrtc.ts
import { encryptCallSignaling, decryptCallSignaling } from './callEncryption';
import { getConversationEncryptionSession } from './encryptionUtils';

export class EncryptedWebRTCManager {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private socket: any;
  private callId: string | null = null;
  private encryptionSession: any = null;
  private conversationId: string | null = null;

  // STUN/TURN server configuration with DTLS-SRTP for media encryption
  private readonly rtcConfiguration: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      // Add TURN servers for production
      // {
      //   urls: 'turn:your-turn-server.com:3478',
      //   username: 'username',
      //   credential: 'password'
      // }
    ],
    iceCandidatePoolSize: 10,
    // Enable DTLS-SRTP for media stream encryption
    bundlePolicy: 'max-bundle',
    rtcpMuxPolicy: 'require',
    sdpSemantics: 'unified-plan'
  };

  constructor(socket: any) {
    this.socket = socket;
    this.setupEncryptedSocketListeners();
  }

  async initializeEncryption(conversationId: string): Promise<void> {
    this.conversationId = conversationId;
    try {
      this.encryptionSession = await getConversationEncryptionSession(conversationId);
      if (!this.encryptionSession) {
        throw new Error('No encryption session found for conversation');
      }
      
      // Verify encryption session with server
      const verificationData = await encryptCallSignaling(
        { type: 'verify_encryption_session', conversationId },
        this.encryptionSession
      );
      
      this.socket.emit('verify_encryption_session', verificationData);
      
      return new Promise((resolve, reject) => {
        this.socket.once('encryption_verified', () => resolve());
        this.socket.once('encryption_error', (error: any) => reject(new Error(error.message)));
      });
    } catch (error) {
      console.error('Failed to initialize call encryption:', error);
      throw error;
    }
  }

  private setupEncryptedSocketListeners() {
    // Listen for encrypted incoming calls
    this.socket.on('encrypted_incoming_call', async (data: any) => {
      try {
        const decryptedData = await decryptCallSignaling(data.encryptedData, this.encryptionSession);
        this.handleIncomingCall(decryptedData);
      } catch (error) {
        console.error('Failed to decrypt incoming call:', error);
      }
    });
    
    // Listen for encrypted WebRTC signaling
    this.socket.on('encrypted_webrtc_offer', async (data: any) => {
      try {
        const decryptedData = await decryptCallSignaling(data.encryptedData, this.encryptionSession);
        this.handleOffer(decryptedData);
      } catch (error) {
        console.error('Failed to decrypt WebRTC offer:', error);
      }
    });
    
    this.socket.on('encrypted_webrtc_answer', async (data: any) => {
      try {
        const decryptedData = await decryptCallSignaling(data.encryptedData, this.encryptionSession);
        this.handleAnswer(decryptedData);
      } catch (error) {
        console.error('Failed to decrypt WebRTC answer:', error);
      }
    });
    
    this.socket.on('encrypted_webrtc_ice_candidate', async (data: any) => {
      try {
        const decryptedData = await decryptCallSignaling(data.encryptedData, this.encryptionSession);
        this.handleIceCandidate(decryptedData);
      } catch (error) {
        console.error('Failed to decrypt ICE candidate:', error);
      }
    });
  }

  private handleIncomingCall(data: any) {
    this.onIncomingCall?.(data);
  }

  async initializeCall(callId: string, isVideo: boolean = false): Promise<void> {
    this.callId = callId;
    
    try {
      // Get user media
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: isVideo
      });

      // Create peer connection
      this.peerConnection = new RTCPeerConnection(this.rtcConfiguration);
      
      // Add local stream to peer connection
      this.localStream.getTracks().forEach(track => {
        if (this.peerConnection && this.localStream) {
          this.peerConnection.addTrack(track, this.localStream);
        }
      });

      // Handle remote stream
      this.peerConnection.ontrack = (event) => {
        this.remoteStream = event.streams[0];
        this.onRemoteStream?.(this.remoteStream);
      };

      // Handle ICE candidates with encryption
      this.peerConnection.onicecandidate = async (event) => {
        if (event.candidate && this.encryptionSession) {
          try {
            const candidateData = {
              callId: this.callId,
              candidate: event.candidate,
              type: 'webrtc_ice_candidate'
            };
            
            const encryptedCandidate = await encryptCallSignaling(candidateData, this.encryptionSession);
            
            this.socket.emit('encrypted_webrtc_ice_candidate', {
              encryptedData: encryptedCandidate
            });
          } catch (error) {
            console.error('Failed to encrypt ICE candidate:', error);
          }
        }
      };

      // Handle connection state changes
      this.peerConnection.onconnectionstatechange = () => {
        const state = this.peerConnection?.connectionState;
        this.onConnectionStateChange?.(state || 'unknown');
        
        if (state === 'connected') {
          this.startQualityMonitoring();
        }
      };

    } catch (error) {
      console.error('Failed to initialize call:', error);
      throw error;
    }
  }

  async createEncryptedOffer(): Promise<void> {
    if (!this.peerConnection || !this.encryptionSession) {
      throw new Error('Peer connection or encryption session not initialized');
    }

    try {
      const offer = await this.peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true
      });

      await this.peerConnection.setLocalDescription(offer);

      // Encrypt the offer before sending
      const offerData = {
        callId: this.callId,
        offer: offer,
        type: 'webrtc_offer'
      };
      
      const encryptedOffer = await encryptCallSignaling(offerData, this.encryptionSession);

      this.socket.emit('encrypted_webrtc_offer', {
        encryptedData: encryptedOffer
      });

    } catch (error) {
      console.error('Failed to create encrypted offer:', error);
      throw error;
    }
  }

  private async handleOffer(data: any): Promise<void> {
    if (!this.peerConnection || !this.encryptionSession) {
      throw new Error('Peer connection or encryption session not initialized');
    }

    try {
      await this.peerConnection.setRemoteDescription(data.offer);
      
      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);

      // Encrypt the answer before sending
      const answerData = {
        callId: data.callId,
        answer: answer,
        type: 'webrtc_answer'
      };
      
      const encryptedAnswer = await encryptCallSignaling(answerData, this.encryptionSession);

      this.socket.emit('encrypted_webrtc_answer', {
        encryptedData: encryptedAnswer
      });

    } catch (error) {
      console.error('Failed to handle encrypted offer:', error);
      throw error;
    }
  }

  private async handleAnswer(data: any): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      await this.peerConnection.setRemoteDescription(data.answer);
    } catch (error) {
      console.error('Failed to handle answer:', error);
      throw error;
    }
  }

  private async handleIceCandidate(data: any): Promise<void> {
    if (!this.peerConnection) {
      return;
    }

    try {
      await this.peerConnection.addIceCandidate(data.candidate);
    } catch (error) {
      console.error('Failed to add ICE candidate:', error);
    }
  }

  toggleAudio(): boolean {
    if (!this.localStream) return false;
    
    const audioTrack = this.localStream.getAudioTracks()[0];
    if (audioTrack) {
      audioTrack.enabled = !audioTrack.enabled;
      
      this.socket.emit('toggle_audio', {
        callId: this.callId,
        enabled: audioTrack.enabled
      });
      
      return audioTrack.enabled;
    }
    return false;
  }

  toggleVideo(): boolean {
    if (!this.localStream) return false;
    
    const videoTrack = this.localStream.getVideoTracks()[0];
    if (videoTrack) {
      videoTrack.enabled = !videoTrack.enabled;
      
      this.socket.emit('toggle_video', {
        callId: this.callId,
        enabled: videoTrack.enabled
      });
      
      return videoTrack.enabled;
    }
    return false;
  }

  private startQualityMonitoring(): void {
    if (!this.peerConnection) return;

    const interval = setInterval(async () => {
      if (!this.peerConnection || this.peerConnection.connectionState !== 'connected') {
        clearInterval(interval);
        return;
      }

      try {
        const stats = await this.peerConnection.getStats();
        const metrics = this.parseStats(stats);
        
        this.socket.emit('call_quality_report', {
          callId: this.callId,
          metrics
        });

        this.onQualityUpdate?.(metrics);
        
      } catch (error) {
        console.error('Failed to get call stats:', error);
      }
    }, 5000); // Report every 5 seconds
  }

  private parseStats(stats: RTCStatsReport): any {
    const metrics: any = {};
    
    stats.forEach((report) => {
      if (report.type === 'inbound-rtp' && report.mediaType === 'audio') {
        metrics.audioPacketsLost = report.packetsLost;
        metrics.audioJitter = report.jitter;
      } else if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
        metrics.videoPacketsLost = report.packetsLost;
        metrics.videoFramesDecoded = report.framesDecoded;
        metrics.videoFrameWidth = report.frameWidth;
        metrics.videoFrameHeight = report.frameHeight;
      } else if (report.type === 'candidate-pair' && report.state === 'succeeded') {
        metrics.roundTripTime = report.currentRoundTripTime;
      }
    });
    
    return metrics;
  }

  endCall(): void {
    // Stop local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    // Close peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Reset state
    this.remoteStream = null;
    this.callId = null;
  }

  // Event handlers (to be set by the calling component)
  onRemoteStream?: (stream: MediaStream) => void;
  onConnectionStateChange?: (state: string) => void;
  onQualityUpdate?: (metrics: any) => void;
  onIncomingCall?: (data: any) => void;
  onEncryptionError?: (error: Error) => void;
}

// Call encryption utilities
export class CallEncryptionUtils {
  static async encryptCallSignaling(data: any, encryptionSession: any): Promise<string> {
    // Implementation depends on your encryption library
    // This should use the same encryption as Phase 3
    const jsonData = JSON.stringify(data);
    return await encryptionSession.encrypt(jsonData);
  }
  
  static async decryptCallSignaling(encryptedData: string, encryptionSession: any): Promise<any> {
    // Implementation depends on your encryption library
    const decryptedJson = await encryptionSession.decrypt(encryptedData);
    return JSON.parse(decryptedJson);
  }
}
```

## Integration Points

### Socket.io Integration
- Call signaling through WebSocket
- Real-time call state updates
- WebRTC offer/answer exchange

### Frontend Integration
- Call UI components
- Media stream handling
- Call quality monitoring

## Acceptance Criteria

### Phase 6 Completion Checklist
- [ ] Encrypted database models created and migrated
- [ ] Encrypted API endpoints implemented and tested
- [ ] Encrypted socket handlers for real-time communication
- [ ] WebRTC peer connection with DTLS-SRTP encryption
- [ ] Encrypted audio/video stream signaling
- [ ] Encrypted call quality monitoring
- [ ] End-to-end encryption validation
- [ ] Error handling and recovery for encryption failures
- [ ] Frontend encrypted integration complete
- [ ] Mobile responsiveness with encryption verified
- [ ] Encryption key rotation support
- [ ] Secure call metadata handling

### Testing Requirements
1. **Unit Tests**
   - Encrypted call model validation
   - Encrypted API endpoint functionality
   - WebRTC encryption utility functions
   - Call signaling encryption/decryption

2. **Integration Tests**
   - End-to-end encrypted call flow
   - Encrypted socket communication
   - Encrypted database operations
   - Cross-device encryption compatibility

3. **Security Tests**
   - Encryption key validation
   - Man-in-the-middle attack prevention
   - Call metadata privacy verification
   - Signal Protocol integration validation

4. **Manual Testing**
   - Encrypted audio-only calls
   - Encrypted video calls
   - Call quality with encryption overhead
   - Multiple simultaneous encrypted calls
   - Call interruption and encryption recovery scenarios
   - Cross-platform encrypted calling

5. **Performance Tests**
   - Encryption/decryption latency impact
   - Call setup time with encryption
   - Bandwidth usage with encrypted signaling
   - Battery impact on mobile devices

## Common Issues & Troubleshooting

### Encryption-Related Issues
- **Encryption Session Not Found**: Verify Phase 3 encryption is properly implemented
- **Key Exchange Failure**: Check Signal Protocol key generation and exchange
- **Decryption Errors**: Validate message counter synchronization
- **Encryption Overhead**: Monitor performance impact of encryption/decryption
- **Cross-Device Key Sync**: Ensure encryption keys are properly synchronized

### WebRTC Connection Issues
- Check STUN/TURN server configuration
- Verify firewall and NAT settings
- Test ICE candidate gathering
- **DTLS-SRTP Handshake Failed**: Verify WebRTC encryption configuration
- **Encrypted Signaling Timeout**: Check encrypted Socket.io connection stability

### Media Access Problems
- Ensure browser permissions for camera/microphone
- Handle device availability gracefully
- Test on different devices and browsers
- **Encrypted Media Setup**: Verify DTLS-SRTP is properly configured

### Call Quality Issues
- Monitor network conditions
- Implement adaptive bitrate
- Provide quality feedback to users
- **Encryption Performance Impact**: Monitor CPU usage during encryption/decryption
- **Encrypted Call Latency**: Optimize encryption processing for real-time communication

### Security Considerations
- **Man-in-the-Middle Prevention**: Validate encryption keys through Signal Protocol
- **Forward Secrecy**: Ensure proper key rotation for ongoing calls
- **Metadata Protection**: Verify all call metadata is encrypted
- **Cross-Device Security**: Validate encryption consistency across platforms
- **Call Recording Prevention**: Ensure encrypted calls cannot be intercepted

## Next Phase Dependencies
- End-to-end encrypted audio/video calling fully functional
- Encrypted call quality acceptable with minimal performance impact
- Encrypted WebRTC signaling stable and secure
- UI responsive for encrypted call operations
- Signal Protocol integration validated for call encryption
- Cross-device encrypted calling compatibility verified
- Security audit of call encryption implementation completed

## Security Validation Checklist
- [ ] All call signaling is end-to-end encrypted
- [ ] WebRTC media streams use DTLS-SRTP encryption
- [ ] Call metadata is encrypted and protected
- [ ] No plaintext call data is stored or transmitted
- [ ] Encryption keys are properly rotated and managed
- [ ] Cross-platform encryption compatibility verified
- [ ] Performance impact of encryption is acceptable (<100ms latency)

This phase enables secure real-time communication with end-to-end encryption. Ensure thorough security testing and encryption validation across different network conditions and devices before proceeding to Phase 7 (Advanced Features).
