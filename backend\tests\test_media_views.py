# backend/tests/test_media_views.py
import pytest
import json
import tempfile
import uuid
from io import BytesIO
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, MagicMock, mock_open

from media.models import MediaFile, MediaDownload, MediaChunk
from messaging.models import Conversation, ConversationParticipant
from tests.factories import (
    UserFactory, ConversationFactory, ConversationParticipantFactory,
    MediaFileFactory, MediaDownloadFactory, MediaChunkFactory
)

User = get_user_model()


@pytest.mark.django_db
class TestMediaViews:
    """Test media endpoints"""

    def setup_method(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.conversation = ConversationFactory()
        ConversationParticipantFactory(conversation=self.conversation, user=self.user)
        
        # Authenticate client
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def create_test_file(self, filename='test.jpg', content=b'test file content'):
        """Create a test file for upload"""
        return SimpleUploadedFile(filename, content, content_type='image/jpeg')

    @patch('media.views.scan_file_for_virus')
    @patch('media.views.default_storage.save')
    @patch('media.views.default_storage.open')
    def test_upload_media_simple_success(self, mock_storage_open, mock_storage_save, mock_virus_scan):
        """Test successful simple media upload"""
        mock_virus_scan.return_value = {'infected': False, 'clean': True}
        mock_storage_save.return_value = 'media/test/file.enc'
        
        test_file = self.create_test_file()
        data = {
            'file': test_file,
            'conversation_id': str(self.conversation.id),
            'wrapped_file_key': 'encrypted_file_key',
            'file_nonce': 'file_nonce',
            'file_hash': 'file_hash'
        }
        
        url = reverse('upload_media_simple')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert 'media_file' in response.data
        assert 'message' in response.data
        assert 'message_id' in response.data
        assert 'media_id' in response.data
        
        # Verify media file was created
        media_file = MediaFile.objects.get(id=response.data['media_id'])
        assert media_file.original_filename == 'test.jpg'
        assert media_file.uploader == self.user

    @patch('media.views.scan_file_for_virus')
    def test_upload_media_simple_virus_detected(self, mock_virus_scan):
        """Test media upload when virus is detected"""
        mock_virus_scan.return_value = {'infected': True, 'threat': 'Test.Virus'}
        
        test_file = self.create_test_file()
        data = {
            'file': test_file,
            'conversation_id': str(self.conversation.id),
            'wrapped_file_key': 'encrypted_file_key',
            'file_nonce': 'file_nonce',
            'file_hash': 'file_hash'
        }
        
        url = reverse('upload_media_simple')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'File rejected' in response.data['error']
        assert 'Test.Virus' in response.data['error']

    def test_upload_media_simple_no_file(self):
        """Test media upload without file"""
        data = {
            'conversation_id': str(self.conversation.id),
            'wrapped_file_key': 'encrypted_file_key',
            'file_nonce': 'file_nonce'
        }
        
        url = reverse('upload_media_simple')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'No file provided' in response.data['error']

    def test_upload_media_simple_missing_params(self):
        """Test media upload with missing required parameters"""
        test_file = self.create_test_file()
        data = {
            'file': test_file,
            'conversation_id': str(self.conversation.id)
            # Missing wrapped_file_key and file_nonce
        }
        
        url = reverse('upload_media_simple')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'Missing required parameters' in response.data['error']

    def test_upload_media_simple_not_participant(self):
        """Test media upload when user is not conversation participant"""
        other_conversation = ConversationFactory()
        # Don't add user as participant
        
        test_file = self.create_test_file()
        data = {
            'file': test_file,
            'conversation_id': str(other_conversation.id),
            'wrapped_file_key': 'encrypted_file_key',
            'file_nonce': 'file_nonce'
        }
        
        url = reverse('upload_media_simple')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'Access denied to conversation' in response.data['error']

    def test_upload_media_simple_file_too_large(self):
        """Test media upload with file too large for simple upload"""
        # Create a large file (> 10MB)
        large_content = b'x' * (11 * 1024 * 1024)
        test_file = SimpleUploadedFile('large.jpg', large_content, content_type='image/jpeg')
        
        data = {
            'file': test_file,
            'conversation_id': str(self.conversation.id),
            'wrapped_file_key': 'encrypted_file_key',
            'file_nonce': 'file_nonce'
        }
        
        url = reverse('upload_media_simple')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'File too large for simple upload' in response.data['error']

    def test_upload_media_simple_unauthenticated(self):
        """Test media upload without authentication"""
        self.client.credentials()  # Remove authentication
        
        test_file = self.create_test_file()
        data = {
            'file': test_file,
            'conversation_id': str(self.conversation.id),
            'wrapped_file_key': 'encrypted_file_key',
            'file_nonce': 'file_nonce'
        }
        
        url = reverse('upload_media_simple')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_upload_media_chunked_first_chunk(self):
        """Test chunked media upload - first chunk"""
        import hashlib
        chunk_data = b'chunk data'
        test_chunk = self.create_test_file('chunk.dat', chunk_data)
        # Calculate correct hash for the chunk data
        chunk_hash = hashlib.sha256(chunk_data).hexdigest()
        
        data = {
            'chunk': test_chunk,
            'conversation_id': str(self.conversation.id),
            'upload_session': str(uuid.uuid4()),
            'chunk_number': 0,
            'total_chunks': 2,
            'chunk_hash': chunk_hash,
            'original_filename': 'test.jpg',
            'file_size': 1000,
            'mime_type': 'image/jpeg',
            'wrapped_file_key': 'encrypted_file_key',
            'file_nonce': 'file_nonce'
        }
        
        url = reverse('upload_media_chunked')
        response = self.client.post(url, data, format='multipart')
        
        if response.status_code != status.HTTP_200_OK:
            print(f"Response status: {response.status_code}")
            print(f"Response data: {response.data}")
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['status'] == 'chunk_received'
        assert response.data['received_chunks'] == 1
        assert response.data['total_chunks'] == 2
        
        # Verify chunk was stored
        chunk = MediaChunk.objects.get(upload_session=data['upload_session'])
        assert chunk.chunk_number == 0

    def test_upload_media_chunked_chunk_integrity_fail(self):
        """Test chunked upload with chunk integrity check failure"""
        test_chunk = self.create_test_file('chunk.dat', b'chunk data')
        data = {
            'chunk': test_chunk,
            'conversation_id': str(self.conversation.id),
            'upload_session': str(uuid.uuid4()),
            'chunk_number': 0,
            'total_chunks': 1,
            'chunk_hash': 'wrong_hash',  # Wrong hash
            'original_filename': 'test.jpg'
        }
        
        url = reverse('upload_media_chunked')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'Chunk integrity check failed' in response.data['error']

    def test_upload_media_chunked_missing_params(self):
        """Test chunked upload with missing parameters"""
        test_chunk = self.create_test_file('chunk.dat', b'chunk data')
        data = {
            'chunk': test_chunk
            # Missing required parameters
        }
        
        url = reverse('upload_media_chunked')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'Missing required parameters' in response.data['error']

    def test_download_media_success(self):
        """Test successful media download token generation"""
        media_file = MediaFileFactory(uploader=self.user)
        media_file.message.conversation = self.conversation
        media_file.message.save()
        
        url = reverse('download_media', kwargs={'media_id': media_file.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'download_url' in response.data
        assert 'filename' in response.data
        assert 'file_size' in response.data
        assert 'expires_at' in response.data
        assert 'wrapped_file_key' in response.data
        assert 'file_nonce' in response.data
        
        # Verify download record was created
        download = MediaDownload.objects.get(media_file=media_file)
        assert download.downloaded_by == self.user

    def test_download_media_not_found(self):
        """Test download for non-existent media file"""
        url = reverse('download_media', kwargs={'media_id': '00000000-0000-0000-0000-000000000000'})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert 'Media file not found' in response.data['error']

    def test_download_media_access_denied(self):
        """Test download when user doesn't have access to conversation"""
        other_user = UserFactory()
        other_conversation = ConversationFactory()
        ConversationParticipantFactory(conversation=other_conversation, user=other_user)
        
        media_file = MediaFileFactory(uploader=other_user)
        media_file.message.conversation = other_conversation
        media_file.message.save()
        
        url = reverse('download_media', kwargs={'media_id': media_file.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'Access denied' in response.data['error']

    @patch('media.views.default_storage.open')
    def test_secure_download_success(self, mock_storage_open):
        """Test successful secure download"""
        mock_storage_open.return_value.__enter__.return_value.read.return_value = b'encrypted file data'
        
        media_file = MediaFileFactory()
        download = MediaDownloadFactory(media_file=media_file, downloaded_by=self.user)
        
        url = reverse('secure_download', kwargs={'download_token': download.download_token})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response['Content-Type'] == 'application/octet-stream'
        assert 'attachment' in response['Content-Disposition']
        
        # Verify download count was incremented
        download.refresh_from_db()
        assert download.download_count == 1

    def test_secure_download_invalid_token(self):
        """Test secure download with invalid token"""
        url = reverse('secure_download', kwargs={'download_token': 'invalid-token'})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_secure_download_expired_token(self):
        """Test secure download with expired token"""
        from django.utils import timezone
        from datetime import timedelta
        
        media_file = MediaFileFactory()
        download = MediaDownloadFactory(
            media_file=media_file,
            downloaded_by=self.user,
            expires_at=timezone.now() - timedelta(hours=1)  # Expired
        )
        
        url = reverse('secure_download', kwargs={'download_token': download.download_token})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_thumbnail_success(self):
        """Test successful thumbnail retrieval"""
        media_file = MediaFileFactory(
            uploader=self.user,
            encrypted_thumbnail_path='thumbnails/test.enc',
            thumbnail_nonce='thumbnail_nonce'
        )
        media_file.message.conversation = self.conversation
        media_file.message.save()
        
        with patch('media.views.default_storage.open') as mock_open:
            mock_open.return_value.__enter__.return_value.read.return_value = b'encrypted thumbnail data'
            
            url = reverse('get_thumbnail', kwargs={'media_id': media_file.id})
            response = self.client.get(url)
            
            assert response.status_code == status.HTTP_200_OK
            assert 'encrypted_thumbnail' in response.data
            assert 'thumbnail_nonce' in response.data
            assert 'wrapped_file_key' in response.data

    def test_get_thumbnail_not_available(self):
        """Test thumbnail retrieval when thumbnail is not available"""
        media_file = MediaFileFactory(
            uploader=self.user,
            encrypted_thumbnail_path=None  # No thumbnail
        )
        media_file.message.conversation = self.conversation
        media_file.message.save()
        
        url = reverse('get_thumbnail', kwargs={'media_id': media_file.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert 'Thumbnail not available' in response.data['error']

    def test_get_thumbnail_access_denied(self):
        """Test thumbnail retrieval when user doesn't have access"""
        other_user = UserFactory()
        other_conversation = ConversationFactory()
        ConversationParticipantFactory(conversation=other_conversation, user=other_user)
        
        media_file = MediaFileFactory(
            uploader=other_user,
            encrypted_thumbnail_path='thumbnails/test.enc'
        )
        media_file.message.conversation = other_conversation
        media_file.message.save()
        
        url = reverse('get_thumbnail', kwargs={'media_id': media_file.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'Access denied' in response.data['error']

    def test_get_thumbnail_unauthenticated(self):
        """Test thumbnail retrieval without authentication"""
        self.client.credentials()  # Remove authentication
        
        media_file = MediaFileFactory()
        
        url = reverse('get_thumbnail', kwargs={'media_id': media_file.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @patch('media.views.validate_file_metadata')
    def test_file_validation(self, mock_validate):
        """Test file validation during upload"""
        mock_validate.return_value = "File type not supported"
        
        test_file = self.create_test_file()
        data = {
            'file': test_file,
            'conversation_id': str(self.conversation.id),
            'wrapped_file_key': 'encrypted_file_key',
            'file_nonce': 'file_nonce'
        }
        
        url = reverse('upload_media_simple')
        response = self.client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'File type not supported' in response.data['error']
