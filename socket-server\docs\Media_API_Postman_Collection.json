{"info": {"_postman_id": "media-api-collection-2024", "name": "Media API Collection", "description": "Complete collection for testing the Media API endpoints including chunked upload, streaming, and download token functionality.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/health", "host": ["{{BASE_URL}}"], "path": ["health"]}, "description": "Check if the media API is running and healthy."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has status property\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "});"], "type": "text/javascript"}}]}, {"name": "Start Chunked Upload", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}, {"key": "X-User-Id", "value": "{{USER_ID}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"originalFilename\": \"test_file.txt\",\n  \"mimeType\": \"text/plain\",\n  \"fileSize\": 37\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/upload/chunked/start", "host": ["{{BASE_URL}}"], "path": ["upload", "chunked", "start"]}, "description": "Start a new chunked upload session."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required properties\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', true);", "    pm.expect(jsonData).to.have.property('uploadSession');", "    pm.expect(jsonData).to.have.property('mediaFileId');", "});", "", "// Store values for subsequent requests", "var jsonData = pm.response.json();", "if (jsonData.uploadSession) {", "    pm.environment.set('UPLOAD_SESSION', jsonData.uploadSession);", "}", "if (jsonData.mediaFileId) {", "    pm.environment.set('MEDIA_FILE_ID', jsonData.mediaFileId);", "}"], "type": "text/javascript"}}]}, {"name": "Upload Chunk", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}, {"key": "X-User-Id", "value": "{{USER_ID}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "uploadSession", "value": "{{UPLOAD_SESSION}}", "type": "text"}, {"key": "chunkIndex", "value": "0", "type": "text"}, {"key": "totalChunks", "value": "1", "type": "text"}, {"key": "chunk", "type": "file", "src": "/path/to/test_file.txt"}]}, "url": {"raw": "{{BASE_URL}}/upload/chunked/chunk", "host": ["{{BASE_URL}}"], "path": ["upload", "chunked", "chunk"]}, "description": "Upload a file chunk. Update the file path in the form data to point to your test file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success property\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', true);", "});", "", "pm.test(\"Check if upload completed\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.message && jsonData.message.includes('completed')) {", "        console.log('Upload completed successfully');", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Media File Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}, {"key": "X-User-Id", "value": "{{USER_ID}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/info/{{MEDIA_FILE_ID}}", "host": ["{{BASE_URL}}"], "path": ["info", "{{MEDIA_FILE_ID}}"]}, "description": "Get information about a media file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has media file properties\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('originalFilename');", "    pm.expect(jsonData).to.have.property('mimeType');", "    pm.expect(jsonData).to.have.property('fileSize');", "    pm.expect(jsonData).to.have.property('processingStatus');", "});", "", "pm.test(\"Processing status should be completed\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.processingStatus).to.eql('completed');", "});"], "type": "text/javascript"}}]}, {"name": "Create Download Token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}, {"key": "X-User-Id", "value": "{{USER_ID}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"maxDownloads\": 5,\n  \"expiresInMinutes\": 60\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/download/token/{{MEDIA_FILE_ID}}", "host": ["{{BASE_URL}}"], "path": ["download", "token", "{{MEDIA_FILE_ID}}"]}, "description": "Create a download token for a media file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has download token properties\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('downloadToken');", "    pm.expect(jsonData).to.have.property('expiresAt');", "    pm.expect(jsonData).to.have.property('maxDownloads');", "});", "", "// Store download token for streaming requests", "var jsonData = pm.response.json();", "if (jsonData.downloadToken) {", "    pm.environment.set('DOWNLOAD_TOKEN', jsonData.downloadToken);", "}"], "type": "text/javascript"}}]}, {"name": "Stream Media - Full File", "request": {"method": "GET", "header": [{"key": "Range", "value": "bytes=0-", "type": "text"}], "url": {"raw": "{{BASE_URL}}/stream/{{DOWNLOAD_TOKEN}}", "host": ["{{BASE_URL}}"], "path": ["stream", "{{DOWNLOAD_TOKEN}}"]}, "description": "Stream the complete media file using download token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 206 (Partial Content)\", function () {", "    pm.response.to.have.status(206);", "});", "", "pm.test(\"Response has Content-Range header\", function () {", "    pm.expect(pm.response.headers.get('Content-Range')).to.exist;", "});", "", "pm.test(\"Response has Accept-Ranges header\", function () {", "    pm.expect(pm.response.headers.get('Accept-Ranges')).to.eql('bytes');", "});", "", "pm.test(\"Response body contains expected content\", function () {", "    pm.expect(pm.response.text()).to.include('This is a test file');", "});"], "type": "text/javascript"}}]}, {"name": "Stream Media - Partial Range (0-10)", "request": {"method": "GET", "header": [{"key": "Range", "value": "bytes=0-10", "type": "text"}], "url": {"raw": "{{BASE_URL}}/stream/{{DOWNLOAD_TOKEN}}", "host": ["{{BASE_URL}}"], "path": ["stream", "{{DOWNLOAD_TOKEN}}"]}, "description": "Stream a partial range of the media file (first 11 bytes)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 206 (Partial Content)\", function () {", "    pm.response.to.have.status(206);", "});", "", "pm.test(\"Response has correct Content-Range header\", function () {", "    var contentRange = pm.response.headers.get('Content-Range');", "    pm.expect(contentRange).to.include('bytes 0-10');", "});", "", "pm.test(\"Response body has correct length\", function () {", "    pm.expect(pm.response.text().length).to.eql(11);", "});", "", "pm.test(\"Response body contains expected partial content\", function () {", "    pm.expect(pm.response.text()).to.eql('This is a t');", "});"], "type": "text/javascript"}}]}, {"name": "Stream Media - Partial Range (10-20)", "request": {"method": "GET", "header": [{"key": "Range", "value": "bytes=10-20", "type": "text"}], "url": {"raw": "{{BASE_URL}}/stream/{{DOWNLOAD_TOKEN}}", "host": ["{{BASE_URL}}"], "path": ["stream", "{{DOWNLOAD_TOKEN}}"]}, "description": "Stream a partial range of the media file (bytes 10-20)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 206 (Partial Content)\", function () {", "    pm.response.to.have.status(206);", "});", "", "pm.test(\"Response has correct Content-Range header\", function () {", "    var contentRange = pm.response.headers.get('Content-Range');", "    pm.expect(contentRange).to.include('bytes 10-20');", "});", "", "pm.test(\"Response body has correct length\", function () {", "    pm.expect(pm.response.text().length).to.eql(11);", "});", "", "pm.test(\"Response body contains expected partial content\", function () {", "    pm.expect(pm.response.text()).to.eql('test file f');", "});"], "type": "text/javascript"}}]}, {"name": "Stream Media - No Range (Full Download)", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/stream/{{DOWNLOAD_TOKEN}}", "host": ["{{BASE_URL}}"], "path": ["stream", "{{DOWNLOAD_TOKEN}}"]}, "description": "Download the complete media file without range headers."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has Content-Length header\", function () {", "    pm.expect(pm.response.headers.get('Content-Length')).to.exist;", "});", "", "pm.test(\"Response body contains complete file content\", function () {", "    pm.expect(pm.response.text()).to.eql('This is a test file for media streaming');", "});"], "type": "text/javascript"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "// You can add any global setup here"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "// You can add any global tests here"]}}], "variable": [{"key": "BASE_URL", "value": "http://localhost:7000/api/media", "type": "string"}, {"key": "TOKEN", "value": "test-token", "type": "string"}, {"key": "USER_ID", "value": "d4f0e4d1-0367-45bc-a42e-cf622c695a48", "type": "string"}]}