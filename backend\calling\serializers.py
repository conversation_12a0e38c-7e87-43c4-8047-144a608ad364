# backend/calling/serializers.py
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Call, CallParticipant, CallEvent, CallQualityMetric

User = get_user_model()


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user info for call responses"""
    class Meta:
        model = User
        fields = ['id', 'name', 'profile_picture']


class CallEventSerializer(serializers.ModelSerializer):
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = CallEvent
        fields = ['id', 'event_type', 'user', 'event_data', 'timestamp']


class CallQualityMetricSerializer(serializers.ModelSerializer):
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = CallQualityMetric
        fields = [
            'id', 'user', 'packet_loss', 'jitter', 'round_trip_time',
            'bandwidth_upload', 'bandwidth_download', 'audio_level',
            'audio_quality_score', 'video_resolution', 'video_framerate',
            'video_quality_score', 'recorded_at'
        ]


class CallParticipantSerializer(serializers.ModelSerializer):
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = CallParticipant
        fields = [
            'id', 'user', 'status', 'joined_at', 'left_at',
            'audio_enabled', 'video_enabled', 'screen_sharing'
        ]


class CallSerializer(serializers.ModelSerializer):
    caller = UserBasicSerializer(read_only=True)
    callee = UserBasicSerializer(read_only=True)
    participants = CallParticipantSerializer(many=True, read_only=True)
    events = CallEventSerializer(many=True, read_only=True)
    quality_metrics = CallQualityMetricSerializer(many=True, read_only=True)
    
    class Meta:
        model = Call
        fields = [
            'id', 'conversation', 'caller', 'callee', 'call_type', 'status',
            'initiated_at', 'answered_at', 'ended_at', 'duration',
            'session_id', 'quality_rating', 'quality_issues', 'metadata',
            'participants', 'events', 'quality_metrics'
        ]
        read_only_fields = [
            'id', 'session_id', 'initiated_at', 'duration'
        ]


class CallCreateSerializer(serializers.Serializer):
    conversation_id = serializers.UUIDField(required=False)
    conversationId = serializers.UUIDField(required=False)  # camelCase version
    call_type = serializers.ChoiceField(choices=['audio', 'video'], default='audio', required=False)
    callType = serializers.ChoiceField(choices=['audio', 'video'], default='audio', required=False)  # camelCase version
    
    def validate(self, data):
        # Handle conversationId vs conversation_id
        conversation_id = data.get('conversation_id') or data.get('conversationId')
        if not conversation_id:
            raise serializers.ValidationError("Either 'conversation_id' or 'conversationId' is required")
        
        # Handle callType vs call_type
        call_type = data.get('call_type') or data.get('callType', 'audio')
        if call_type not in ['audio', 'video']:
            raise serializers.ValidationError("Call type must be 'audio' or 'video'")
        
        # Normalize the data to snake_case for internal use
        return {
            'conversation_id': conversation_id,
            'call_type': call_type
        }
    
    def validate_call_type(self, value):
        if value not in ['audio', 'video']:
            raise serializers.ValidationError("Call type must be 'audio' or 'video'")
        return value


class CallSDPUpdateSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=['offer', 'answer'])
    sdp = serializers.CharField()
    
    def validate_type(self, value):
        if value not in ['offer', 'answer']:
            raise serializers.ValidationError("SDP type must be 'offer' or 'answer'")
        return value


class CallQualityReportSerializer(serializers.Serializer):
    packet_loss = serializers.FloatField(required=False, allow_null=True)
    jitter = serializers.FloatField(required=False, allow_null=True)
    round_trip_time = serializers.FloatField(required=False, allow_null=True)
    bandwidth_upload = serializers.IntegerField(required=False, allow_null=True)
    bandwidth_download = serializers.IntegerField(required=False, allow_null=True)
    audio_level = serializers.FloatField(required=False, allow_null=True)
    audio_quality_score = serializers.FloatField(required=False, allow_null=True)
    video_resolution = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    video_framerate = serializers.FloatField(required=False, allow_null=True)
    video_quality_score = serializers.FloatField(required=False, allow_null=True)
