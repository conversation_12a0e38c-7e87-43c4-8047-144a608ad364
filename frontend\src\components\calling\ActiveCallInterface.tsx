// frontend/src/components/calling/ActiveCallInterface.tsx
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { X, Minimize2 } from 'lucide-react';
import { selectCallingState, selectShowActiveCall } from '../../store/slices/callingSlice';
import { VideoContainer } from './VideoContainer';
import { CallControls } from './CallControls';
import { CallStatusIndicator } from './CallStatusIndicator';

export const ActiveCallInterface: React.FC = () => {
  const showActiveCall = useSelector(selectShowActiveCall);
  const { currentCall } = useSelector(selectCallingState);
  const [callDuration, setCallDuration] = useState(0);
  const [isMinimized, setIsMinimized] = useState(false);

  // Update call duration every second
  useEffect(() => {
    let interval: number;

    if (currentCall.status === 'active' && currentCall.startTime) {
      interval = setInterval(() => {
        const duration = Math.floor((Date.now() - currentCall.startTime!) / 1000);
        setCallDuration(duration);
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [currentCall.status, currentCall.startTime]);

  // Format duration as MM:SS or HH:MM:SS
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  // Don't render if not showing active call
  if (!showActiveCall) {
    return null;
  }

  // Minimized view
  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-gray-900 rounded-lg shadow-2xl p-4 min-w-[280px]">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <CallStatusIndicator />
              <span className="text-white text-sm font-medium">
                {currentCall.type === 'video' ? 'Video Call' : 'Audio Call'}
              </span>
            </div>
            <button
              onClick={handleMinimize}
              className="text-gray-400 hover:text-white p-1"
              title="Expand call"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {/* Call duration */}
          <div className="text-center mb-3">
            <span className="text-white text-lg font-mono">
              {formatDuration(callDuration)}
            </span>
          </div>

          {/* Minimized video container */}
          {currentCall.type === 'video' && (
            <div className="mb-3">
              <VideoContainer isMinimized={true} />
            </div>
          )}

          {/* Minimized call controls */}
          <CallControls isMinimized={true} />
        </div>
      </div>
    );
  }

  // Full screen view
  return (
    <div data-testid="active-call-interface" className="fixed inset-0 z-50 bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-gray-800">
        <div className="flex items-center space-x-3">
          <CallStatusIndicator />
          <div>
            <h2 className="text-white text-lg font-semibold">
              {currentCall.type === 'video' ? 'Video Call' : 'Audio Call'}
            </h2>
            <p className="text-gray-300 text-sm">
              {currentCall.participants.length > 0 
                ? `${currentCall.participants[0].firstName} ${currentCall.participants[0].lastName}`
                : 'Unknown'
              }
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Call duration */}
          <div className="text-white text-lg font-mono bg-gray-700 px-3 py-1 rounded">
            {formatDuration(callDuration)}
          </div>

          {/* Minimize button */}
          <button
            onClick={handleMinimize}
            className="text-gray-400 hover:text-white p-2 rounded hover:bg-gray-700"
            title="Minimize call"
          >
            <Minimize2 className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Video container */}
      <div className="flex-1 relative">
        <VideoContainer />
      </div>

      {/* Call status and info */}
      <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-10">
        {currentCall.status !== 'active' && (
          <div className="bg-black bg-opacity-50 text-white px-4 py-2 rounded-full text-sm">
            {currentCall.status === 'initiating' && 'Calling...'}
            {currentCall.status === 'ringing' && 'Ringing...'}
            {currentCall.status === 'ending' && 'Ending call...'}
          </div>
        )}
      </div>

      {/* Call controls */}
      <div className="p-6 bg-gray-800">
        <CallControls />
      </div>

      {/* Connection quality indicator */}
      <div className="absolute top-4 right-20">
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-white text-xs ml-1">Good</span>
        </div>
      </div>
    </div>
  );
};

export default ActiveCallInterface;
