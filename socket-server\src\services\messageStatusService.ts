// socket-server/src/services/messageStatusService.ts
import { PrismaClient } from '@prisma/client';
import { MessageStatusUpdateSchema, type MessageStatusUpdate, type MessageStatusType } from '../schemas';

export class MessageStatusService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async updateMessageStatus(
    messageId: string,
    userId: string,
    status: MessageStatusType
  ) {
    // Validate input
    const validatedData = MessageStatusUpdateSchema.parse({
      messageId,
      status
    });

    // Check if message exists and user has access to it
    const message = await this.prisma.message.findFirst({
      where: {
        id: messageId,
        conversation: {
          participants: {
            some: {
              userId: userId
            }
          }
        }
      },
      include: {
        conversation: {
          include: {
            participants: true
          }
        }
      }
    });

    if (!message) {
      throw new Error('Message not found or access denied');
    }

    // Don't allow updating status for own messages to READ
    if (message.senderId === userId && status === 'READ') {
      throw new Error('Cannot mark own message as read');
    }

    // Create or update message status
    const messageStatus = await this.prisma.messageStatus.upsert({
      where: {
        messageId_userId: {
          messageId,
          userId
        }
      },
      update: {
        status,
        updatedAt: new Date()
      },
      create: {
        messageId,
        userId,
        status,
        createdAt: new Date()
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            profile_picture: true
          }
        }
      }
    });

    return messageStatus;
  }

  async getMessageStatuses(messageId: string, requestingUserId: string) {
    // Check if requesting user has access to the message
    const message = await this.prisma.message.findFirst({
      where: {
        id: messageId,
        conversation: {
          participants: {
            some: {
              userId: requestingUserId
            }
          }
        }
      }
    });

    if (!message) {
      throw new Error('Message not found or access denied');
    }

    const statuses = await this.prisma.messageStatus.findMany({
      where: {
        messageId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            profile_picture: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return statuses;
  }

  async markMessageAsDelivered(messageId: string, userId: string) {
    return this.updateMessageStatus(messageId, userId, 'DELIVERED');
  }

  async markMessageAsRead(messageId: string, userId: string) {
    return this.updateMessageStatus(messageId, userId, 'READ');
  }

  async markAllMessageStatusesAsRead(messageId: string, readByUserId: string) {
    // Check if the user has access to the message
    const message = await this.prisma.message.findFirst({
      where: {
        id: messageId,
        conversation: {
          participants: {
            some: {
              userId: readByUserId
            }
          }
        }
      },
      include: {
        conversation: {
          include: {
            participants: true
          }
        }
      }
    });

    if (!message) {
      throw new Error('Message not found or access denied');
    }

    // Get all participants in the conversation
    const participantIds = message.conversation.participants.map(p => p.userId);

    // Update all existing message statuses to 'READ' and create new ones for participants who don't have them
    const updatePromises = participantIds.map(async (participantId) => {
      return this.prisma.messageStatus.upsert({
        where: {
          messageId_userId: {
            messageId,
            userId: participantId
          }
        },
        update: {
          status: 'READ',
          updatedAt: new Date()
        },
        create: {
          messageId,
          userId: participantId,
          status: 'READ',
          createdAt: new Date()
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              profile_picture: true
            }
          }
        }
      });
    });

    const updatedStatuses = await Promise.all(updatePromises);
    return updatedStatuses;
  }

  async markMessageAsFailed(messageId: string, userId: string) {
    return this.updateMessageStatus(messageId, userId, 'FAILED');
  }

  async getConversationReadStatus(conversationId: string, userId: string) {
    // Get all messages in conversation that are not from the requesting user
    const messages = await this.prisma.message.findMany({
      where: {
        conversationId,
        NOT: {
          senderId: userId
        }
      },
      include: {
        statuses: {
          where: {
            userId
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return messages.map(message => ({
      messageId: message.id,
      status: message.statuses[0]?.status || 'SENT',
      updatedAt: message.statuses[0]?.updatedAt || message.createdAt
    }));
  }
}
