// frontend/src/contexts/SocketContext.tsx
import React, { createContext, useContext, useEffect, useRef, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { io, Socket } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';
import { useAuth } from './AuthContext';
import { useEncryption } from './EncryptionContext';
import type { AppDispatch } from '../store';
import { store } from '../store';
import type {
  KeyExchangeRequest,
  KeyExchangeResponse,
  EncryptionStatusRequest,
  EncryptionStatusResponse
} from '../types/encryption';
import {
  addMessage,
  addOptimisticMessage,
  addTypingUser,
  removeTypingUser,
  setError as setMessageError,
  updateMessageStatus,
  markMessageAsFailed,
  retryMessage
} from '../store/slices/messageSlice';
import {
  updateConversationLastMessage,
  addConversation,
  convertDraftToRealConversation
} from '../store/slices/conversationSlice';
import {
  receiveIncomingCall,
  updateCallStatus,
  resetCallState,
  setCallingError,
  showIncomingCallModal,
  hideIncomingCallModal,
  showActiveCallInterface,
  setCurrentCall,
  type IncomingCallData
} from '../store/slices/callingSlice';
import { useCreateConversationMutation, conversationApi } from '../services/conversationApi';
import { useAddMessageToCacheMutation } from '../services/messageApi';

interface SocketContextType {
  // Connection status
  isConnected: boolean;
  socket: Socket | null;

  // Message operations
  sendMessage: (conversationId: string, content: string, messageType?: string) => Promise<string>;

  // Conversation operations
  joinConversations: () => void;
  joinConversation: (conversationId: string) => void;

  // Typing indicators
  startTyping: (conversationId: string) => void;
  stopTyping: (conversationId: string) => void;

  // Message status
  markMessageAsDelivered: (messageId: string, tempId?: string) => void;
  markMessageAsRead: (messageId: string, tempId?: string) => void;
  retryFailedMessage: (messageId: string, conversationId: string, content: string, messageType?: string) => Promise<string>;

  // User status
  setUserOnline: () => void;

  // Encryption operations
  requestKeyExchange: (targetUserId: string, conversationId: string, ephemeralPublicKey: string) => void;
  checkEncryptionStatus: (conversationId: string) => void;

  // Encryption event handlers (for external subscription)
  onKeyExchangeResponse: (handler: (response: KeyExchangeResponse) => void) => () => void;
  onEncryptionStatusResponse: (handler: (response: EncryptionStatusResponse) => void) => () => void;

  // Note: Call management operations (initiate, answer, decline, end) are now handled via REST API
  // Only WebRTC signaling and media controls remain in socket context

  // WebRTC signaling
  sendWebRTCOffer: (callId: string, offer: RTCSessionDescriptionInit) => void;
  sendWebRTCAnswer: (callId: string, answer: RTCSessionDescriptionInit) => void;
  sendICECandidate: (callId: string, candidate: RTCIceCandidateInit) => void;

  // Media controls
  toggleCallAudio: (callId: string, enabled: boolean) => void;
  toggleCallVideo: (callId: string, enabled: boolean) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = React.useState(false);
  const { token, isAuthenticated, user } = useAuth();
  const { encryptMessageForSending, decryptReceivedMessage, isEncryptionReady } = useEncryption();
  const dispatch = useDispatch<AppDispatch>();
  const tempIdToMessageIdRef = useRef<Record<string, string>>({});
  const messageTimeoutsRef = useRef<Record<string, number>>({});
  const [createConversation] = useCreateConversationMutation();
  const [addMessageToCache] = useAddMessageToCacheMutation();

  // Encryption event handlers
  const keyExchangeHandlersRef = useRef<Set<(response: KeyExchangeResponse) => void>>(new Set());
  const encryptionStatusHandlersRef = useRef<Set<(response: EncryptionStatusResponse) => void>>(new Set());

  // Setup all socket event listeners
  const setupEventListeners = useCallback(() => {
    if (!socketRef.current) return;

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('error', handleError);

    // Conversation events
    socket.on('conversations_joined', handleConversationsJoined);
    socket.on('joined_conversation', handleJoinedConversation);
    socket.on('conversation_created', handleConversationCreated);

    // Message events
    socket.on('new_message', handleNewMessage);
    socket.on('message_sent', handleMessageSent);
    socket.on('message_status_updated', handleMessageStatusUpdated);
    socket.on('message_failed', handleMessageFailed);

    // Typing events
    socket.on('user_typing', handleUserTyping);

    // User status events
    socket.on('user_status_change', handleUserStatusChange);

    // Encryption events
    socket.on('key_exchange_response', handleKeyExchangeResponse);
    socket.on('encryption_status_response', handleEncryptionStatusResponse);

    // Media events
    socket.on('media_upload_started', handleMediaUploadStarted);
    socket.on('media_upload_progress', handleMediaUploadProgress);
    socket.on('media_upload_completed', handleMediaUploadCompleted);
    socket.on('media_upload_failed', handleMediaUploadFailed);
    socket.on('media_message_received', handleMediaMessageReceived);
    socket.on('media_download_started', handleMediaDownloadStarted);

    // Calling events (following exact HTML test patterns)
    socket.on('call_initiated', handleCallInitiated);
    socket.on('incoming_call', handleIncomingCall);
    socket.on('call_answered', handleCallAnswered);
    socket.on('call_active', handleCallActive);
    socket.on('call_declined', handleCallDeclined);
    socket.on('call_ended', handleCallEnded);

    // WebRTC signaling events (following exact HTML test patterns)
    socket.on('webrtc_offer', handleWebRTCOffer);
    socket.on('webrtc_answer', handleWebRTCAnswer);
    socket.on('webrtc_ice_candidate', handleWebRTCICECandidate);

    // Media control events (following exact HTML test patterns)
    socket.on('toggle_audio', handleToggleAudio);
    socket.on('toggle_video', handleToggleVideo);
  }, [dispatch]);

   // Cleanup function
   const cleanup = useCallback(() => {
     if (socketRef.current) {
       console.log('Cleaning up socket connection...');

       // Remove all event listeners
       socketRef.current.off('connect');
       socketRef.current.off('disconnect');
       socketRef.current.off('error');
       socketRef.current.off('conversations_joined');
       socketRef.current.off('joined_conversation');
       socketRef.current.off('new_message');
       socketRef.current.off('message_sent');
       socketRef.current.off('message_status_updated');
       socketRef.current.off('message_failed');
       socketRef.current.off('user_typing');
       socketRef.current.off('user_status_change');
       socketRef.current.off('key_exchange_response');
       socketRef.current.off('encryption_status_response');

       // Remove calling event listeners
       socketRef.current.off('call_initiated');
       socketRef.current.off('incoming_call');
       socketRef.current.off('call_answered');
       socketRef.current.off('call_active');
       socketRef.current.off('call_declined');
       socketRef.current.off('call_ended');
       socketRef.current.off('webrtc_offer');
       socketRef.current.off('webrtc_answer');
       socketRef.current.off('webrtc_ice_candidate');
       socketRef.current.off('toggle_audio');
       socketRef.current.off('toggle_video');

       // Disconnect socket
       socketRef.current.disconnect();
       socketRef.current = null;
     }

     // Clear all message timeouts
     Object.values(messageTimeoutsRef.current).forEach(clearTimeout);
     messageTimeoutsRef.current = {};

     setIsConnected(false);
   }, []);

   // Initialize socket connection
  useEffect(() => {
    if (isAuthenticated && token) {
      console.log('🔌 [SOCKET_DEBUG] === INITIALIZING SOCKET CONNECTION ===');
      console.log('🔌 [SOCKET_DEBUG] User authenticated:', isAuthenticated);
      console.log('🔌 [SOCKET_DEBUG] Token available:', !!token);
      console.log('🔌 [SOCKET_DEBUG] User ID:', user?.id);
      console.log('🔌 [SOCKET_DEBUG] Connecting to: http://localhost:7000');

      socketRef.current = io('http://localhost:7000', {
        auth: {
          token: token
        }
      });
      
      console.log('🔌 [SOCKET_DEBUG] Socket instance created');
      console.log('🔌 [SOCKET_DEBUG] Socket ID (initial):', socketRef.current?.id);
      console.log('🔌 [SOCKET_DEBUG] Socket connected (initial):', socketRef.current?.connected);

      setupEventListeners();
      console.log('🔌 [SOCKET_DEBUG] Event listeners setup complete');

      return () => {
        console.log('🔌 [SOCKET_DEBUG] Cleaning up socket connection');
        cleanup();
      };
    } else {
      console.log('🔌 [SOCKET_DEBUG] Socket initialization skipped - not authenticated or no token');
      console.log('🔌 [SOCKET_DEBUG] Authenticated:', isAuthenticated, 'Token:', !!token);
    }
  }, [isAuthenticated, token, setupEventListeners, cleanup]);



  // Event handlers
  const handleConnect = useCallback(() => {
    console.log('🔌 [SOCKET_DEBUG] === SOCKET CONNECTED ===');
    console.log('🔌 [SOCKET_DEBUG] Socket ID:', socketRef.current?.id);
    console.log('🔌 [SOCKET_DEBUG] Socket connected:', socketRef.current?.connected);
    console.log('🔌 [SOCKET_DEBUG] User authenticated:', isAuthenticated);
    console.log('🔌 [SOCKET_DEBUG] User ID:', user?.id);
    setIsConnected(true);

    // Auto-join conversations on connect
    if (socketRef.current) {
      console.log('🔌 [SOCKET_DEBUG] Emitting join_conversations');
      socketRef.current.emit('join_conversations');
      console.log('🔌 [SOCKET_DEBUG] Emitting user_online');
      socketRef.current.emit('user_online');
    }

    console.log('✅ [SOCKET_DEBUG] Socket connection setup complete');
  }, [isAuthenticated, user?.id]);

  const handleDisconnect = useCallback((reason: string) => {
    console.warn('⚠️ [SOCKET_DEBUG] === SOCKET DISCONNECTED ===');
    console.warn('⚠️ [SOCKET_DEBUG] Disconnect reason:', reason);
    console.warn('⚠️ [SOCKET_DEBUG] Socket ID was:', socketRef.current?.id);
    console.warn('⚠️ [SOCKET_DEBUG] User ID:', user?.id);
    setIsConnected(false);
  }, [user?.id]);

  const handleError = useCallback((error: { message: string; details?: any }) => {
    console.error('❌ [SOCKET_DEBUG] === SOCKET ERROR ===');
    console.error('❌ [SOCKET_DEBUG] Error message:', error.message);
    console.error('❌ [SOCKET_DEBUG] Error details:', error.details);
    console.error('❌ [SOCKET_DEBUG] Socket connected:', socketRef.current?.connected);
    console.error('❌ [SOCKET_DEBUG] Socket ID:', socketRef.current?.id);
    dispatch(setMessageError(error.message));
  }, [dispatch]);

  const handleConversationsJoined = useCallback((data: {
    success: boolean;
    count: number;
    conversations?: any[]
  }) => {
    console.log(`Joined ${data.count} conversations`);

    if (data.conversations) {
      data.conversations.forEach(conversation => {
        dispatch(addConversation(conversation));
      });
    }
  }, [dispatch]);

  const handleJoinedConversation = useCallback((data: { conversationId: string }) => {
    console.log(`Joined conversation: ${data.conversationId}`);
  }, []);

  const handleConversationCreated = useCallback((data: {
    conversation: any;
    creator_id: string;
    timestamp: string;
  }) => {
    console.log('🔔 [CONVERSATION_CREATED] ===== NEW CONVERSATION NOTIFICATION =====');
    console.log('🔔 [CONVERSATION_CREATED] Conversation ID:', data.conversation.id);
    console.log('🔔 [CONVERSATION_CREATED] Creator ID:', data.creator_id);
    console.log('🔔 [CONVERSATION_CREATED] Timestamp:', data.timestamp);
    console.log('🔔 [CONVERSATION_CREATED] Full conversation data:', data.conversation);

    // Add the conversation to the store
    dispatch(addConversation(data.conversation));

    // Invalidate conversations cache to refresh the list
    store.dispatch(conversationApi.util.invalidateTags(['Conversation']));

    console.log('🔔 [CONVERSATION_CREATED] ✅ Conversation added to store and cache invalidated');
  }, [dispatch]);

  const handleNewMessage = useCallback(async (message: any) => {
    console.log('🔵 [NEW_MESSAGE] ===== RECEIVED NEW MESSAGE =====');
    console.log('🔵 [NEW_MESSAGE] Message ID:', message.id);
    console.log('🔵 [NEW_MESSAGE] Sender:', message.sender.username);
    console.log('🔵 [NEW_MESSAGE] Is Encrypted:', message.isEncrypted);
    console.log('🔵 [NEW_MESSAGE] Conversation ID:', message.conversationId);
    console.log('🔵 [NEW_MESSAGE] Has encryptedContent:', !!message.encryptedContent);
    console.log('🔵 [NEW_MESSAGE] Has plaintext content:', !!message.content);
    console.log('🔵 [NEW_MESSAGE] Full message object:', message);

    // Decrypt message if it's encrypted
    let content = message.content;
    console.log('🔓 [NEW_MESSAGE] ===== DECRYPTION PROCESS =====');
    console.log('🔓 [NEW_MESSAGE] Original content field:', message.content);
    console.log('🔓 [NEW_MESSAGE] Is encrypted flag:', message.isEncrypted);
    console.log('🔓 [NEW_MESSAGE] Has encryptedContent:', !!message.encryptedContent);

    if (message.isEncrypted && message.encryptedContent) {
      console.log('🔓 [NEW_MESSAGE] Starting decryption process...');
      console.log('🔓 [NEW_MESSAGE] EncryptedContent length:', message.encryptedContent.length);
      console.log('🔓 [NEW_MESSAGE] IV length:', message.iv?.length || 0);
      console.log('🔓 [NEW_MESSAGE] SenderRatchetKey length:', message.senderRatchetKey?.length || 0);

      try {
        content = await decryptReceivedMessage(
          message.encryptedContent,
          message.iv,
          message.senderRatchetKey
        );
        console.log('🔓 [NEW_MESSAGE] ✅ Message decrypted successfully!');
        console.log('🔓 [NEW_MESSAGE] Decrypted content:', content);
      } catch (error) {
        console.error('🔓 [NEW_MESSAGE] ❌ Failed to decrypt message:', error);
        content = '[Encrypted Message]';
      }
    } else if (message.content) {
      console.log('🔓 [NEW_MESSAGE] Using existing plaintext content');
      content = message.content;
    } else {
      console.warn('🔓 [NEW_MESSAGE] ⚠️ No content or encrypted content found');
      content = '[No Content]';
    }

    console.log('🔓 [NEW_MESSAGE] Final content for UI:', content);

    // Check if this message should replace an optimistic message
    console.log('🔵 [NEW_MESSAGE] ===== MESSAGE REPLACEMENT LOGIC =====');
    console.log('🔵 [NEW_MESSAGE] Current tempId mappings:', tempIdToMessageIdRef.current);

    let tempId = tempIdToMessageIdRef.current[message.id];
    console.log('🔵 [NEW_MESSAGE] TempId lookup by messageId:', message.id, '->', tempId);

    // If no direct mapping found, check if this might be a message we sent
    // by looking for a tempId in the message itself (some backends include this)
    if (!tempId && message.tempId) {
      tempId = message.tempId;
      console.log('🔵 [NEW_MESSAGE] Using tempId from message payload:', tempId);
    }

    // Additional fallback: check if we have any pending messages that match this one
    if (!tempId && user && message.sender.id === user.id) {
      console.log('🔵 [NEW_MESSAGE] Checking for matching optimistic message for user message');
      // Look for optimistic message with same content and sender
      // This handles the case where new_message arrives before message_sent
      const reduxMessages = store.getState().messages.messages[message.conversationId] || [];
      const optimisticMessage = reduxMessages.find(msg =>
        msg.isOptimistic &&
        msg.content === content && // Use decrypted content for comparison
        msg.sender.id === message.sender.id &&
        Math.abs(new Date(msg.createdAt).getTime() - new Date(message.createdAt).getTime()) < 10000 // Within 10 seconds
      );

      if (optimisticMessage) {
        tempId = optimisticMessage.tempId || optimisticMessage.id;
        console.log('🔵 [NEW_MESSAGE] Found matching optimistic message with tempId:', tempId);
      }
    }

    console.log('🔵 [NEW_MESSAGE] Final tempId for replacement:', tempId);
    console.log('🔵 [NEW_MESSAGE] Current tempId mappings:', tempIdToMessageIdRef.current);

    // For real conversations, only use RTK Query cache
    // For draft conversations, use Redux store
    const isDraftConversation = message.conversationId?.startsWith('draft-');

    // Create the message object with decrypted content
    const messageToAdd = {
      ...message,
      content, // Use decrypted content
      tempId: tempId
    };

    console.log('🔵 [NEW_MESSAGE] ===== REDUX DISPATCH =====');
    console.log('🔵 [NEW_MESSAGE] Message to add:', messageToAdd);
    console.log('🔵 [NEW_MESSAGE] Is draft conversation:', isDraftConversation);

    if (isDraftConversation) {
      console.log('🔵 [NEW_MESSAGE] Adding message to Redux store (draft conversation)...');
      console.log('🔵 [NEW_MESSAGE] TempId for replacement:', tempId);
      // Add message to Redux store (with tempId if it exists)
      dispatch(addMessage(messageToAdd));
      console.log('🔵 [NEW_MESSAGE] ✅ Dispatched addMessage to Redux store');
    } else {
      console.log('🔵 [NEW_MESSAGE] Skipping Redux store for real conversation');
    }

    console.log('🔵 [NEW_MESSAGE] Adding message to RTK Query cache...');
    // Add message to RTK Query cache for real-time UI updates
    // Pass tempId to enable proper optimistic message replacement
    addMessageToCache({
      conversationId: message.conversationId,
      message: messageToAdd, // Use message with decrypted content
      tempId: tempId
    });
    console.log('🔵 [NEW_MESSAGE] ✅ Added to RTK Query cache');

    // Clean up the mapping (both directions)
    if (tempId) {
      const messageId = tempIdToMessageIdRef.current[tempId];
      console.log('🔵 [NEW_MESSAGE] Cleaning up tempId mapping:', tempId, '<->', messageId);
      delete tempIdToMessageIdRef.current[tempId];
      if (messageId) {
        delete tempIdToMessageIdRef.current[messageId];
      }
      console.log('🔵 [NEW_MESSAGE] ✅ Cleaned up bidirectional tempId mapping');
    } else {
      console.log('🔵 [NEW_MESSAGE] ⚠️ No tempId to clean up - this might be a duplicate!');
    }

    // Update conversation's last message
    dispatch(updateConversationLastMessage({
      conversationId: message.conversationId,
      message: {
        id: message.id,
        content: content, // Use decrypted content
        sender: { username: message.sender.username },
        createdAt: message.createdAt
      }
    }));
  }, [dispatch, addMessageToCache, user, decryptReceivedMessage]);

  const handleMessageSent = useCallback((data: { tempId: string; messageId: string; status: string }) => {
    console.log('🟢 [MESSAGE_SENT] Received message_sent event');
    console.log('🟢 [MESSAGE_SENT] TempId:', data.tempId);
    console.log('🟢 [MESSAGE_SENT] MessageId:', data.messageId);
    console.log('🟢 [MESSAGE_SENT] Status:', data.status);

    // Clear timeout since message was successfully sent
    if (messageTimeoutsRef.current[data.tempId]) {
      console.log('🟢 [MESSAGE_SENT] Clearing timeout for tempId:', data.tempId);
      clearTimeout(messageTimeoutsRef.current[data.tempId]);
      delete messageTimeoutsRef.current[data.tempId];
    }

    // Store the mapping for when the new_message event arrives
    // We need both directions: tempId -> messageId and messageId -> tempId
    console.log('🟢 [MESSAGE_SENT] Storing bidirectional tempId mapping...');
    tempIdToMessageIdRef.current[data.tempId] = data.messageId;
    tempIdToMessageIdRef.current[data.messageId] = data.tempId;
    console.log('🟢 [MESSAGE_SENT] ✅ Stored mapping:', data.tempId, '<->', data.messageId);
    console.log('🟢 [MESSAGE_SENT] All tempId mappings:', tempIdToMessageIdRef.current);

    // Set a cleanup timeout in case new_message event doesn't arrive
    // This prevents memory leaks from accumulating tempId mappings
    setTimeout(() => {
      if (tempIdToMessageIdRef.current[data.tempId]) {
        console.log('Cleaning up stale tempId mapping:', data.tempId);
        delete tempIdToMessageIdRef.current[data.tempId];
        delete tempIdToMessageIdRef.current[data.messageId];
      }
    }, 30000); // 30 seconds timeout

    // Update message status to delivered
    dispatch(updateMessageStatus({
      messageId: data.messageId,
      status: data.status as 'SENT' | 'DELIVERED' | 'READ' | 'FAILED',
      tempId: data.tempId
    }));
  }, [dispatch]);

  const handleUserTyping = useCallback((data: {
    userId: string;
    conversationId: string;
    isTyping: boolean
  }) => {
    if (data.isTyping) {
      dispatch(addTypingUser({
        conversationId: data.conversationId,
        userId: data.userId
      }));
    } else {
      dispatch(removeTypingUser({
        conversationId: data.conversationId,
        userId: data.userId
      }));
    }
  }, [dispatch]);

  const handleUserStatusChange = useCallback((data: { userId: string; status: 'online' | 'offline' }) => {
    console.log(`User ${data.userId} is now ${data.status}`);
    // This could update user status in a separate slice if needed
  }, []);

  const handleMessageStatusUpdated = useCallback((data: {
    messageId: string;
    userId: string;
    status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';
    tempId?: string;
    updatedAt: string;
  }) => {
    console.log(`Message ${data.messageId} status updated to ${data.status}`);
    dispatch(updateMessageStatus({
      messageId: data.messageId,
      status: data.status,
      tempId: data.tempId
    }));
  }, [dispatch]);

  const handleMessageFailed = useCallback((data: {
    messageId?: string;
    tempId?: string;
    error: string;
  }) => {
    console.log(`Message failed:`, data);
    dispatch(markMessageAsFailed({
      messageId: data.messageId,
      tempId: data.tempId
    }));
  }, [dispatch]);

  // Encryption event handlers
  const handleKeyExchangeResponse = useCallback((response: KeyExchangeResponse) => {
    console.log('🔐 [KEY_EXCHANGE] Received key exchange response:', response);

    // Notify all registered handlers
    keyExchangeHandlersRef.current.forEach(handler => {
      try {
        handler(response);
      } catch (error) {
        console.error('Error in key exchange handler:', error);
      }
    });
  }, []);

  const handleEncryptionStatusResponse = useCallback((response: EncryptionStatusResponse) => {
    console.log('🔐 [ENCRYPTION_STATUS] Received encryption status response:', response);

    // Notify all registered handlers
    encryptionStatusHandlersRef.current.forEach(handler => {
      try {
        handler(response);
      } catch (error) {
        console.error('Error in encryption status handler:', error);
      }
    });
  }, []);

  // Media event handlers
  const handleMediaUploadStarted = useCallback((data: {
    conversationId: string;
    messageId: string;
    uploaderId: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    tempId?: string;
    timestamp: string;
  }) => {
    console.log('📎 [MEDIA] Upload started:', data);
    // Show upload notification in UI - could dispatch a notification action here
    // The MediaUpload component handles the local state
  }, []);

  const handleMediaUploadProgress = useCallback((data: {
    conversationId: string;
    messageId: string;
    uploaderId: string;
    progress: number;
    tempId?: string;
    timestamp: string;
  }) => {
    console.log('📎 [MEDIA] Upload progress:', data);
    // Update progress indicators in UI
    // The MediaUpload component handles the local state
    // This event is useful for syncing progress across multiple devices
  }, []);

  const handleMediaUploadCompleted = useCallback((data: {
    conversationId?: string;
    messageId?: string;
    mediaFileId: string;
    uploaderId?: string;
    fileName?: string;
    fileSize?: number;
    fileType?: string;
    mimeType?: string;
    tempId?: string;
    timestamp: string;
  }) => {
    console.log('📎 [MEDIA] Upload completed:', data);

    // Update Redux store to mark upload as completed
    // This is emitted by the Socket Server when all chunks are assembled
    if (data.conversationId && data.mediaFileId) {
      // Create a media message for the completed upload
      const mediaMessage = {
        id: data.messageId || `media-${data.mediaFileId}`,
        conversationId: data.conversationId,
        sender: {
          id: data.uploaderId || user?.id || 'unknown',
          name: user?.name || 'User',
          profilePicture: user?.profilePicture || null
        },
        content: `📎 ${data.fileName || 'Shared a file'}`,
        createdAt: data.timestamp,
        updatedAt: data.timestamp,
        messageType: 'FILE',
        hasMedia: true,
        mediaCount: 1,
        mediaFileId: data.mediaFileId,
        tempId: data.tempId,
        isEncrypted: false, // Socket Server handles encryption differently
      };

      // Add the media message to the store
      dispatch(addMessage({
        conversationId: data.conversationId,
        message: mediaMessage
      }));

      // Update conversation last message
      dispatch(updateConversationLastMessage({
        conversationId: data.conversationId,
        lastMessage: {
          id: mediaMessage.id,
          content: mediaMessage.content,
          createdAt: mediaMessage.createdAt,
          sender: mediaMessage.sender
        }
      }));

      console.log(`✅ Media file ${data.mediaFileId} uploaded successfully and added to conversation`);
    }
  }, [dispatch, user]);

  const handleMediaUploadFailed = useCallback((data: {
    conversationId?: string;
    messageId?: string;
    tempId?: string;
    error: string;
    timestamp: string;
  }) => {
    console.error('📎 [MEDIA] Upload failed:', data);
    // Show error notification
    // The MediaUpload component handles the error state
  }, []);

  const handleMediaMessageReceived = useCallback((data: any) => {
    console.log('📎 [MEDIA] Media message received:', data);

    // Add the media message to the store
    dispatch(addMessage({
      conversationId: data.conversationId,
      message: data
    }));

    // Update conversation last message
    dispatch(updateConversationLastMessage({
      conversationId: data.conversationId,
      lastMessage: {
        id: data.id,
        content: `📎 ${data.mediaFileId ? 'Shared a file' : 'Media message'}`,
        createdAt: data.createdAt,
        sender: data.sender
      }
    }));
  }, [dispatch]);

  const handleMediaDownloadStarted = useCallback((data: {
    conversationId: string;
    mediaFileId: string;
    downloaderId: string;
    fileName?: string;
    timestamp: string;
  }) => {
    console.log('📎 [MEDIA] Download started:', data);
    // Could show download notification
  }, []);

  // Public API methods
  const sendMessage = useCallback(async (conversationId: string, content: string, messageType: string = 'TEXT'): Promise<string> => {
    if (!socketRef.current || !user) {
      console.error('Socket not connected or user not available');
      return '';
    }

    const tempId = uuidv4();
    let actualConversationId = conversationId;

    // Check if this is a draft conversation
    if (conversationId.startsWith('draft-')) {
      try {
        if (conversationId.startsWith('draft-group-')) {
          // Handle group draft conversion
          // Find the draft group in Redux store
          const state = store.getState();
          const draftGroup = state.conversations.draftConversations.find(
            draft => draft.id === conversationId
          );

          if (!draftGroup) {
            throw new Error('Draft group not found');
          }

          // Create the real group conversation via API
          const result = await createConversation({
            type: 'GROUP',
            name: draftGroup.name,
            participant_ids: draftGroup.participants.map(p => p.id)
          }).unwrap();

          // Handle both wrapped and direct response formats
          const conversationData = result.data || result;

          if (conversationData && conversationData.id) {
            actualConversationId = conversationData.id;

            // Convert draft to real conversation in Redux
            dispatch(convertDraftToRealConversation({
              draftId: conversationId,
              realConversation: conversationData
            }));
          } else {
            console.error('Invalid group conversation response:', result);
            throw new Error('Failed to create group conversation');
          }
        } else {
          // Handle direct message draft conversion
          // Extract user ID from draft conversation ID
          // Format: draft-{userId}-{timestamp}
          // Since userId is a UUID with hyphens, we need to extract everything between 'draft-' and the last '-'
          const parts = conversationId.split('-');
          const userId = parts.slice(1, -1).join('-'); // Everything except 'draft' and timestamp

          // Create the real conversation via API
          const result = await createConversation({
            type: 'DIRECT',
            participant_ids: [userId]
          }).unwrap();

          // Handle both wrapped and direct response formats
          const conversationData = result.data || result;

          if (conversationData && conversationData.id) {
            actualConversationId = conversationData.id;

            // Convert draft to real conversation in Redux
            dispatch(convertDraftToRealConversation({
              draftId: conversationId,
              realConversation: conversationData
            }));
          } else {
            console.error('Invalid conversation response:', result);
            throw new Error('Failed to create conversation');
          }
        }

      } catch (error) {
        console.error('Failed to create conversation:', error);
        return '';
      }
    }

    const optimisticMessage = {
      id: tempId,
      conversation_id: actualConversationId,
      conversationId: actualConversationId,
      content,
      sender: {
        id: user.id,
        username: user.username,
        first_name: user.firstName,
        last_name: user.lastName,
        profile_picture: user.profilePicture
      },
      message_type: messageType as any,
      messageType: messageType as any,
      created_at: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // Store tempId for replacement logic
      tempId: tempId,
      isOptimistic: true
    };

    // For real conversations, only use RTK Query cache
    // For draft conversations, use Redux store
    const isDraftConversation = actualConversationId?.startsWith('draft-');

    if (isDraftConversation) {
      console.log('🟡 [SEND_MESSAGE] Adding optimistic message to Redux store (draft conversation)...');
      // Add optimistic message to Redux store
      dispatch(addOptimisticMessage({
        tempId,
        message: optimisticMessage
      }));
      console.log('🟡 [SEND_MESSAGE] ✅ Added optimistic message to Redux store');
    } else {
      console.log('🟡 [SEND_MESSAGE] Skipping Redux store for real conversation');
    }

    console.log('🟡 [SEND_MESSAGE] Adding optimistic message to RTK Query cache...');
    // Add optimistic message to RTK Query cache for immediate UI update
    addMessageToCache({
      conversationId: actualConversationId,
      message: optimisticMessage,
      tempId: tempId  // Pass tempId for replacement logic
    });
    console.log('🟡 [SEND_MESSAGE] ✅ Added optimistic message to RTK Query cache');

    console.log('🟡 [SEND_MESSAGE] ✅ Optimistic message setup complete');
    console.log('🟡 [SEND_MESSAGE] TempId:', tempId);
    console.log('🟡 [SEND_MESSAGE] Conversation:', actualConversationId);

    // Encrypt and send message through socket
    try {
      console.log('🔐 [SEND_MESSAGE] Encrypting message before sending...');
      const encryptedPayload = await encryptMessageForSending(
        actualConversationId,
        content,
        messageType,
        tempId
      );

      console.log('🔐 [SEND_MESSAGE] Message encrypted successfully, sending to server...');
      socketRef.current.emit('send_message', encryptedPayload);
    } catch (error) {
      console.error('🔐 [SEND_MESSAGE] Encryption failed:', error);
      // Mark message as failed instead of falling back to plaintext
      dispatch(markMessageAsFailed({ messageId: tempId }));
      throw error;
    }

    // Set timeout to mark message as failed if not delivered within 30 seconds
    const timeoutId = setTimeout(() => {
      dispatch(markMessageAsFailed({ tempId }));
      delete messageTimeoutsRef.current[tempId];
    }, 30000);

    messageTimeoutsRef.current[tempId] = timeoutId;

    return tempId;
  }, [dispatch, user, encryptMessageForSending]);

  const joinConversations = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('join_conversations');
    }
  }, []);

  const joinConversation = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('join_conversation', { conversationId });
    }
  }, []);

  const startTyping = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('typing_start', { conversationId });
    }
  }, []);

  const stopTyping = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('typing_stop', { conversationId });
    }
  }, []);

  const markMessageAsDelivered = useCallback((messageId: string, tempId?: string) => {
    if (!socketRef.current) {
      console.error('Socket not connected');
      return;
    }

    socketRef.current.emit('message_delivered', {
      messageId,
      tempId
    });
  }, []);

  const markMessageAsRead = useCallback((messageId: string, tempId?: string) => {
    if (!socketRef.current) {
      console.error('Socket not connected');
      return;
    }

    socketRef.current.emit('message_read', {
      messageId,
      tempId
    });
  }, []);

  const retryFailedMessage = useCallback(async (messageId: string, conversationId: string, content: string, messageType: string = 'TEXT') => {
    if (!socketRef.current || !user) {
      console.error('Socket not connected or user not available');
      return '';
    }

    const tempId = uuidv4();

    // Update Redux state to indicate retry
    dispatch(retryMessage({ messageId, tempId }));

    const retryOptimisticMessage = {
      id: tempId,
      conversation_id: conversationId,
      conversationId: conversationId,
      content,
      sender: {
        id: user.id,
        username: user.username,
        first_name: user.firstName,
        last_name: user.lastName,
        profile_picture: user.profilePicture
      },
      message_type: messageType as any,
      messageType: messageType as any,
      created_at: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add optimistic message to Redux store
    dispatch(addOptimisticMessage({
      tempId,
      message: retryOptimisticMessage
    }));

    // Also add optimistic message to RTK Query cache for immediate UI update
    addMessageToCache({
      conversationId: conversationId,
      message: retryOptimisticMessage
    });

    // Encrypt and send retry message through socket
    try {
      console.log('🔐 [RETRY_MESSAGE] Encrypting retry message before sending...');
      const encryptedPayload = await encryptMessageForSending(
        conversationId,
        content,
        messageType,
        tempId
      );

      console.log('🔐 [RETRY_MESSAGE] Retry message encrypted successfully, sending to server...');
      socketRef.current.emit('send_message', {
        ...encryptedPayload,
        retryOf: messageId // Indicate this is a retry
      });
    } catch (error) {
      console.error('🔐 [RETRY_MESSAGE] Encryption failed:', error);
      // Mark retry as failed instead of falling back to plaintext
      dispatch(markMessageAsFailed({ messageId: tempId }));
      throw error;
    }

    return tempId;
  }, [dispatch, user, encryptMessageForSending]);

  const setUserOnline = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('user_online');
    }
  }, []);

  // Encryption API methods
  const requestKeyExchange = useCallback((
    targetUserId: string,
    conversationId: string,
    ephemeralPublicKey: string
  ) => {
    if (!socketRef.current) {
      console.error('Socket not connected');
      return;
    }

    console.log('🔐 [KEY_EXCHANGE] Requesting key exchange for user:', targetUserId);

    const request: KeyExchangeRequest = {
      targetUserId,
      conversationId,
      ephemeralPublicKey
    };

    socketRef.current.emit('key_exchange_request', request);
  }, []);

  const checkEncryptionStatus = useCallback((conversationId: string) => {
    if (!socketRef.current) {
      console.error('Socket not connected');
      return;
    }

    console.log('🔐 [ENCRYPTION_STATUS] Checking encryption status for conversation:', conversationId);

    const request: EncryptionStatusRequest = {
      conversationId
    };

    socketRef.current.emit('encryption_status_check', request);
  }, []);

  // Event subscription methods
  const onKeyExchangeResponse = useCallback((handler: (response: KeyExchangeResponse) => void) => {
    keyExchangeHandlersRef.current.add(handler);

    // Return unsubscribe function
    return () => {
      keyExchangeHandlersRef.current.delete(handler);
    };
  }, []);

  const onEncryptionStatusResponse = useCallback((handler: (response: EncryptionStatusResponse) => void) => {
    encryptionStatusHandlersRef.current.add(handler);

    // Return unsubscribe function
    return () => {
      encryptionStatusHandlersRef.current.delete(handler);
    };
  }, []);

  // Calling event handlers (following exact HTML test patterns)
  const handleCallInitiated = useCallback((data: any) => {
    console.log('📞 Received call_initiated:', data);
    // Update call ID and status to 'calling' (caller is now waiting for answer)
    dispatch(setCurrentCall({
      id: data.callId,
      status: 'calling'
    }));
    dispatch(showActiveCallInterface());

    // Trigger WebRTC offer creation for the caller
    const event = new CustomEvent('webrtc-create-offer', { detail: data });
    window.dispatchEvent(event);
  }, [dispatch]);

  const handleIncomingCall = useCallback((data: any) => {
    console.log('🔔 Received incoming_call:', data);

    const incomingCallData: IncomingCallData = {
      callId: data.callId,
      caller: {
        id: data.caller.id,
        username: data.caller.username,
        firstName: data.caller.first_name || data.caller.firstName,
        lastName: data.caller.last_name || data.caller.lastName,
        avatar: data.caller.profile_picture || data.caller.avatar,
      },
      callType: data.callType,
      conversationId: data.conversationId,
    };

    dispatch(receiveIncomingCall(incomingCallData));
    dispatch(showIncomingCallModal(incomingCallData));
  }, [dispatch]);

  const handleCallAnswered = useCallback((data: any) => {
    console.log('✅ Received call_answered:', data);
    // Update call status to answered (not active yet - wait for call_active event)
    dispatch(updateCallStatus({ callId: data.callId, status: 'answered' }));
    // Ensure both parties show the active call interface
    dispatch(hideIncomingCallModal());
    dispatch(showActiveCallInterface());
  }, [dispatch]);

  const handleCallActive = useCallback((data: any) => {
    console.log('🔥 Received call_active:', data);
    // Update call status to active and start timer
    dispatch(updateCallStatus({ callId: data.callId, status: 'active' }));
    dispatch(startCallTimer());
    // Ensure both parties show the active call interface
    dispatch(showActiveCallInterface());
  }, [dispatch]);

  const handleCallDeclined = useCallback((data: any) => {
    console.log('❌ Received call_declined:', data);
    dispatch(resetCallState());
  }, [dispatch]);

  const handleCallEnded = useCallback((data: any) => {
    console.log('📞 Received call_ended:', data);

    // Trigger proper media cleanup before resetting state
    // This will be handled by CallManager's cleanup function
    const event = new CustomEvent('webrtc-call-ended', { detail: data });
    window.dispatchEvent(event);

    dispatch(resetCallState());
  }, [dispatch]);

  const handleWebRTCOffer = useCallback((data: any) => {
    console.log('📤 Received webrtc_offer:', data);
    // This will be handled by the CallManager component
    // For now, just log the event
  }, []);

  const handleWebRTCAnswer = useCallback((data: any) => {
    console.log('📥 Received webrtc_answer:', data);
    // This will be handled by the CallManager component
    // For now, just log the event
  }, []);

  const handleWebRTCICECandidate = useCallback((data: any) => {
    console.log('🧊 Received webrtc_ice_candidate:', data);
    // This will be handled by the CallManager component
    // For now, just log the event
  }, []);

  const handleToggleAudio = useCallback((data: any) => {
    console.log('🎤 Received toggle_audio:', data);
    // This will be handled by the CallManager component
    // For now, just log the event
  }, []);

  const handleToggleVideo = useCallback((data: any) => {
    console.log('📹 Received toggle_video:', data);
    // This will be handled by the CallManager component
    // For now, just log the event
  }, []);

  // Note: Call management methods (initiateCall, answerCall, declineCall, endCall)
  // have been removed from socket context. These operations now use REST API endpoints
  // via RTK Query hooks in callingApi.ts for proper validation and database operations.

  const sendWebRTCOffer = useCallback((callId: string, offer: RTCSessionDescriptionInit) => {
    if (!socketRef.current?.connected) {
      console.error('❌ Socket not connected, cannot send WebRTC offer');
      return;
    }

    console.log('📤 Emitting webrtc_offer for call:', callId);
    socketRef.current.emit('webrtc_offer', { callId, offer });
  }, []);

  const sendWebRTCAnswer = useCallback((callId: string, answer: RTCSessionDescriptionInit) => {
    if (!socketRef.current?.connected) {
      console.error('❌ Socket not connected, cannot send WebRTC answer');
      return;
    }

    console.log('📥 Emitting webrtc_answer for call:', callId);
    socketRef.current.emit('webrtc_answer', { callId, answer });
  }, []);

  const sendICECandidate = useCallback((callId: string, candidate: RTCIceCandidateInit) => {
    if (!socketRef.current?.connected) {
      console.error('❌ Socket not connected, cannot send ICE candidate');
      return;
    }

    console.log('🧊 Emitting webrtc_ice_candidate for call:', callId);
    socketRef.current.emit('webrtc_ice_candidate', { callId, candidate });
  }, []);

  const toggleCallAudio = useCallback((callId: string, enabled: boolean) => {
    if (!socketRef.current?.connected) {
      console.error('❌ Socket not connected, cannot toggle audio');
      return;
    }

    console.log('🎤 Emitting toggle_audio:', { callId, enabled });
    socketRef.current.emit('toggle_audio', { callId, enabled });
  }, []);

  const toggleCallVideo = useCallback((callId: string, enabled: boolean) => {
    if (!socketRef.current?.connected) {
      console.error('❌ Socket not connected, cannot toggle video');
      return;
    }

    console.log('📹 Emitting toggle_video:', { callId, enabled });
    socketRef.current.emit('toggle_video', { callId, enabled });
  }, []);

  const contextValue: SocketContextType = {
    isConnected,
    socket: socketRef.current,
    sendMessage,
    joinConversations,
    joinConversation,
    startTyping,
    stopTyping,
    markMessageAsDelivered,
    markMessageAsRead,
    retryFailedMessage,
    setUserOnline,
    requestKeyExchange,
    checkEncryptionStatus,
    onKeyExchangeResponse,
    onEncryptionStatusResponse,
    // WebRTC signaling methods (call management now uses REST API)
    sendWebRTCOffer,
    sendWebRTCAnswer,
    sendICECandidate,
    toggleCallAudio,
    toggleCallVideo,
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
