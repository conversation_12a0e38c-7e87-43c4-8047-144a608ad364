{"auth": [{"endpoint": "/auth/register", "response": {"success": true, "data": {"user": {"email": "<EMAIL>", "name": "<PERSON>", "id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "profilePicture": null, "isVerified": false, "lastSeen": "2025-09-28T04:42:09.302357Z", "createdAt": "2025-09-28T04:42:09.302364Z"}, "tokens": {"access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzU5MDM4NDIwLCJpYXQiOjE3NTkwMzQ4MjAsImp0aSI6ImFjNzA4NGNmOGY0NjRkMTQ4OTM5NmUwYjhmNTZhNjYzIiwidXNlcl9pZCI6IjVmOTU5NzJhLThmZWItNDZkOC1hNzA3LWJlMzljODRiYzFjYSJ9.95Ce9V4oV8fWeZdNIkJ4XqrvXQSCRlL3sAVyd4UICiY", "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1OTYzOTYyMCwiaWF0IjoxNzU5MDM0ODIwLCJqdGkiOiIzM2NhNjQxNTVlYjg0Y2JlOTU2M2UzYWYxMjVjOWRmYSIsInVzZXJfaWQiOiI1Zjk1OTcyYS04ZmViLTQ2ZDgtYTcwNy1iZTM5Yzg0YmMxY2EifQ.VVxH3b5mxQ6DYPYW_qoU6DKzeBtRrR9-WcHAjSRR6Zo"}}}}, {"endpoint": "/auth/login", "response": {"success": true, "data": {"user": {"email": "<EMAIL>", "name": "<PERSON>", "id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "profilePicture": null, "isVerified": false, "lastSeen": "2025-09-28T04:42:09.302357Z", "createdAt": "2025-09-28T04:42:09.302364Z"}, "tokens": {"access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzU5MDM4NDIwLCJpYXQiOjE3NTkwMzQ4MjAsImp0aSI6ImFjNzA4NGNmOGY0NjRkMTQ4OTM5NmUwYjhmNTZhNjYzIiwidXNlcl9pZCI6IjVmOTU5NzJhLThmZWItNDZkOC1hNzA3LWJlMzljODRiYzFjYSJ9.95Ce9V4oV8fWeZdNIkJ4XqrvXQSCRlL3sAVyd4UICiY", "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1OTYzOTYyMCwiaWF0IjoxNzU5MDM0ODIwLCJqdGkiOiIzM2NhNjQxNTVlYjg0Y2JlOTU2M2UzYWYxMjVjOWRmYSIsInVzZXJfaWQiOiI1Zjk1OTcyYS04ZmViLTQ2ZDgtYTcwNy1iZTM5Yzg0YmMxY2EifQ.VVxH3b5mxQ6DYPYW_qoU6DKzeBtRrR9-WcHAjSRR6Zo"}}}}, {"endpoint": "auth/refresh/", "response": {"access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzU5MDQ1NzAzLCJpYXQiOjE3NTkwNDIxMDMsImp0aSI6IjcyNWZlNzRhMDAyNzRkNDk4OThmNmE0YmQzMDc3M2NlIiwidXNlcl9pZCI6IjVmOTU5NzJhLThmZWItNDZkOC1hNzA3LWJlMzljODRiYzFjYSJ9.dXoV0hWmHcXCJ6GaqsX7J9M17Dye-zGiBAVyibjOPH4"}}, {"endpoint": "auth/profile/", "response": {"success": true, "data": {"email": "<EMAIL>", "name": "<PERSON>", "id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "profilePicture": null, "isVerified": false, "lastSeen": "2025-09-28T04:42:09.302357Z", "createdAt": "2025-09-28T04:42:09.302364Z"}}}], "conversations": [{"endpoint": "messaging/conversations/create/", "response": {"id": "e04993de-6bf4-4a8c-9bb8-56c020de2e0d", "type": "DIRECT", "name": null, "participants": [{"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null}, {"id": "9eb56860-b940-415b-a6bb-302a3546ee4c", "name": "mohanda<PERSON> gandhi", "profilePicture": null}], "lastMessage": null, "createdAt": "2025-09-28T04:53:17.607028Z", "updatedAt": "2025-09-28T04:53:17.607227Z", "isEncrypted": false, "encryptionParticipants": null}}, {"endpoint": "messaging/conversations/", "response": {"count": 3, "next": null, "previous": null, "results": [{"id": "0ea22020-9113-4d8c-b86a-ac401a4827e7", "type": "DIRECT", "name": null, "participants": [{"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null}, {"id": "d4f0e4d1-0367-45bc-a42e-cf622c695a48", "name": "harry parot", "profilePicture": null}], "lastMessage": null, "createdAt": "2025-09-28T04:57:31.613523Z", "updatedAt": "2025-09-28T04:57:31.613536Z", "isEncrypted": false, "encryptionParticipants": [{"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null, "hasEncryption": false}, {"id": "d4f0e4d1-0367-45bc-a42e-cf622c695a48", "name": "harry parot", "profilePicture": null, "hasEncryption": false}]}, {"id": "ce2ca137-3eb6-4cd6-8e3a-a82eecb9fef5", "type": "DIRECT", "name": null, "participants": [{"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null}, {"id": "aca940d4-e9b7-44ab-bf45-a5d6de46c9b3", "name": "Comprehensive Test User", "profilePicture": null}], "lastMessage": null, "createdAt": "2025-09-28T04:57:09.362744Z", "updatedAt": "2025-09-28T04:57:09.362759Z", "isEncrypted": false, "encryptionParticipants": [{"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null, "hasEncryption": false}, {"id": "aca940d4-e9b7-44ab-bf45-a5d6de46c9b3", "name": "Comprehensive Test User", "profilePicture": null, "hasEncryption": false}]}, {"id": "e04993de-6bf4-4a8c-9bb8-56c020de2e0d", "type": "DIRECT", "name": null, "participants": [{"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null}, {"id": "9eb56860-b940-415b-a6bb-302a3546ee4c", "name": "mohanda<PERSON> gandhi", "profilePicture": null}], "lastMessage": null, "createdAt": "2025-09-28T04:53:17.607028Z", "updatedAt": "2025-09-28T04:53:17.607227Z", "isEncrypted": false, "encryptionParticipants": [{"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null, "hasEncryption": false}, {"id": "9eb56860-b940-415b-a6bb-302a3546ee4c", "name": "mohanda<PERSON> gandhi", "profilePicture": null, "hasEncryption": false}]}]}}, {"endpoint": "messaging/conversations/{{conversation_id}}/messages/", "response": {"count": 3, "next": null, "previous": null, "results": [{"id": "a83fb68c-811d-4520-b83c-95fa3fe52a41", "conversationId": "0ea22020-9113-4d8c-b86a-ac401a4827e7", "sender": {"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null}, "messageType": "TEXT", "createdAt": "2025-09-28T05:23:28.726356Z", "updatedAt": "2025-09-28T05:23:28.726452Z", "status": "SENT", "content": null, "isEncrypted": true, "encryptedContent": "B6TnKF62xoggl/kU0jTXKw==", "iv": "SYii122TFIkIkYK4", "senderRatchetKey": "ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0", "messageNumber": 1, "previousChainLength": 0}, {"id": "4a05678a-8494-4177-b6df-e5e7b3f82a89", "conversationId": "0ea22020-9113-4d8c-b86a-ac401a4827e7", "sender": {"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null}, "messageType": "TEXT", "createdAt": "2025-09-28T05:16:12.213511Z", "updatedAt": "2025-09-28T05:16:12.213534Z", "status": "SENT", "content": null, "isEncrypted": true, "encryptedContent": "dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50", "iv": "SYii122TFIkIkYK4", "senderRatchetKey": "ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0", "messageNumber": 1, "previousChainLength": 0}, {"id": "a3ef7f76-e255-48d7-b086-ee9643bfa346", "conversationId": "0ea22020-9113-4d8c-b86a-ac401a4827e7", "sender": {"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null}, "messageType": "TEXT", "createdAt": "2025-09-28T05:14:37.223935Z", "updatedAt": "2025-09-28T05:14:37.223969Z", "status": "SENT", "content": null, "isEncrypted": true, "encryptedContent": "dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50", "iv": "SYii122TFIkIkYK4", "senderRatchetKey": "ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0", "messageNumber": 1, "previousChainLength": 0}]}}, {"endpoint": "messaging/users/search/?q=ha", "response": {"success": true, "results": [{"id": "d4f0e4d1-0367-45bc-a42e-cf622c695a48", "name": "harry parot", "profile_picture": null, "email": "<EMAIL>"}, {"id": "9eb56860-b940-415b-a6bb-302a3546ee4c", "name": "mohanda<PERSON> gandhi", "profile_picture": null, "email": "<EMAIL>"}]}}], "groups": [{"endpoint": "api/messaging/groups/create/", "response": {"success": true, "data": {"id": "d6be803c-9498-4e45-851b-d171ca5e5f17", "type": "GROUP", "name": "Test Group", "participants": [{"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null}, {"id": "9eb56860-b940-415b-a6bb-302a3546ee4c", "name": "mohanda<PERSON> gandhi", "profilePicture": null}, {"id": "d4f0e4d1-0367-45bc-a42e-cf622c695a48", "name": "harry parot", "profilePicture": null}], "lastMessage": null, "createdAt": "2025-09-28T05:31:23.528567Z", "updatedAt": "2025-09-28T05:31:23.528582Z", "isEncrypted": false, "encryptionParticipants": null}}}, {"endpoint": "messaging/groups/{{conversation_id}}/add-member/", "response": {"message": "Member added successfully", "participant": {"id": "6c73e546-d5b7-41f7-99f2-f053389e3d64", "user": {"id": "1b81b090-bf25-4f20-9d5c-22b475fc2d6d", "name": "Test User", "profile_picture": null}, "role": "member", "joined_at": "2025-09-28T05:35:06.858117+00:00"}}}, {"endpoint": "messaging/groups/{{conversation_id}}/remove-member/{{user_id}}/", "response": {"message": "Member removed successfully"}}, {"endpoint": "messaging/groups/{{conversation_id}}/update/", "response": {"id": "d6be803c-9498-4e45-851b-d171ca5e5f17", "type": "GROUP", "name": "Updated Group Name", "participants": [{"id": "5f95972a-8feb-46d8-a707-be39c84bc1ca", "name": "<PERSON>", "profilePicture": null}, {"id": "9eb56860-b940-415b-a6bb-302a3546ee4c", "name": "mohanda<PERSON> gandhi", "profilePicture": null}, {"id": "d4f0e4d1-0367-45bc-a42e-cf622c695a48", "name": "harry parot", "profilePicture": null}, {"id": "1b81b090-bf25-4f20-9d5c-22b475fc2d6d", "name": "Test User", "profilePicture": null}], "lastMessage": null, "createdAt": "2025-09-28T05:31:23.528567Z", "updatedAt": "2025-09-28T05:52:17.670888Z", "isEncrypted": false, "encryptionParticipants": null}}]}